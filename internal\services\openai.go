package services

import (
	"context"
	"fmt"
	"io"
	"strings"

	"aike_go/internal/config"
	"aike_go/internal/interfaces"
	"aike_go/internal/models"

	"github.com/sashabaranov/go-openai"
)

// OpenAIService OpenAI服务结构体
// 思路：封装OpenAI API调用，提供聊天补全和流式响应功能
// 使用例子：service := NewOpenAIService(cfg); response := service.ChatCompletion(ctx, messages)
type OpenAIService struct {
	client *openai.Client
	config *config.OpenAIConfig
}

// ChatMessage 聊天消息结构体
// 思路：统一的消息格式，便于在不同模块间传递
type ChatMessage struct {
	Role    string `json:"role"`    // 角色：system, user, assistant
	Content string `json:"content"` // 消息内容
}

// ChatCompletionRequest 聊天补全请求
// 思路：封装请求参数，支持自定义配置
type ChatCompletionRequest struct {
	Messages    []ChatMessage `json:"messages"`              // 消息列表
	Model       string        `json:"model,omitempty"`       // 模型名称
	Temperature *float32      `json:"temperature,omitempty"` // 温度参数
	MaxTokens   int           `json:"max_tokens,omitempty"`  // 最大token数
	Stream      bool          `json:"stream,omitempty"`      // 是否流式响应
}

// ChatCompletionResponse 聊天补全响应
// 思路：统一的响应格式，包含使用统计信息
type ChatCompletionResponse struct {
	Content      string `json:"content"`       // 回复内容
	Model        string `json:"model"`         // 使用的模型
	TokensUsed   int    `json:"tokens_used"`   // 使用的token数
	FinishReason string `json:"finish_reason"` // 结束原因
}

// StreamCallback 流式响应回调函数
// 思路：支持实时处理流式响应数据
type StreamCallback func(content string, done bool) error

// NewOpenAIService 创建OpenAI服务实例
// 思路：根据配置初始化OpenAI客户端
// 使用例子：service := NewOpenAIService(cfg)
func NewOpenAIService(cfg *config.Config) *OpenAIService {
	clientConfig := openai.DefaultConfig(cfg.OpenAI.APIKey)

	// 如果配置了自定义BaseURL，则使用自定义地址
	if cfg.OpenAI.BaseURL != "" && cfg.OpenAI.BaseURL != "https://api.openai.com/v1" {
		clientConfig.BaseURL = cfg.OpenAI.BaseURL
	}

	client := openai.NewClientWithConfig(clientConfig)

	return &OpenAIService{
		client: client,
		config: &cfg.OpenAI,
	}
}

// ChatCompletion 聊天补全（非流式）
// 思路：发送消息到OpenAI并获取完整回复
// 使用例子：response, err := service.ChatCompletion(ctx, request)
func (s *OpenAIService) ChatCompletion(ctx context.Context, req ChatCompletionRequest) (*ChatCompletionResponse, error) {
	// 设置默认模型
	if req.Model == "" {
		req.Model = s.config.Model
	}

	// 转换消息格式
	messages := make([]openai.ChatCompletionMessage, len(req.Messages))
	for i, msg := range req.Messages {
		messages[i] = openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// 构建请求
	openaiReq := openai.ChatCompletionRequest{
		Model:    req.Model,
		Messages: messages,
	}

	// 设置可选参数
	if req.Temperature != nil {
		openaiReq.Temperature = *req.Temperature
	}
	if req.MaxTokens > 0 {
		openaiReq.MaxTokens = req.MaxTokens
	}

	// 发送请求
	resp, err := s.client.CreateChatCompletion(ctx, openaiReq)
	if err != nil {
		return nil, fmt.Errorf("OpenAI API调用失败: %w", err)
	}

	// 检查响应
	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("OpenAI API返回空响应")
	}

	return &ChatCompletionResponse{
		Content:      resp.Choices[0].Message.Content,
		Model:        resp.Model,
		TokensUsed:   resp.Usage.TotalTokens,
		FinishReason: string(resp.Choices[0].FinishReason),
	}, nil
}

// ChatCompletionStream 聊天补全（流式）
// 思路：流式接收OpenAI响应，实时处理数据
// 使用例子：err := service.ChatCompletionStream(ctx, request, callback)
func (s *OpenAIService) ChatCompletionStream(ctx context.Context, req ChatCompletionRequest, callback StreamCallback) error {
	// 设置默认模型
	if req.Model == "" {
		req.Model = s.config.Model
	}

	// 转换消息格式
	messages := make([]openai.ChatCompletionMessage, len(req.Messages))
	for i, msg := range req.Messages {
		messages[i] = openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// 构建流式请求
	openaiReq := openai.ChatCompletionRequest{
		Model:    req.Model,
		Messages: messages,
		Stream:   true,
	}

	// 设置可选参数
	if req.Temperature != nil {
		openaiReq.Temperature = *req.Temperature
	}
	if req.MaxTokens > 0 {
		openaiReq.MaxTokens = req.MaxTokens
	}

	// 创建流式请求
	stream, err := s.client.CreateChatCompletionStream(ctx, openaiReq)
	if err != nil {
		return fmt.Errorf("创建流式请求失败: %w", err)
	}
	defer stream.Close()

	// 处理流式响应
	var fullContent strings.Builder
	for {
		response, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				// 流结束，发送最终回调
				if callback != nil {
					if err := callback(fullContent.String(), true); err != nil {
						return fmt.Errorf("流式回调处理失败: %w", err)
					}
				}
				break
			}
			return fmt.Errorf("接收流式响应失败: %w", err)
		}

		// 处理响应数据
		if len(response.Choices) > 0 {
			content := response.Choices[0].Delta.Content
			if content != "" {
				fullContent.WriteString(content)

				// 调用回调函数
				if callback != nil {
					if err := callback(content, false); err != nil {
						return fmt.Errorf("流式回调处理失败: %w", err)
					}
				}
			}
		}
	}

	return nil
}

// BuildMessagesFromHistory 从聊天历史构建消息列表
// 思路：将数据库中的消息记录转换为OpenAI API格式
// 使用例子：messages := service.BuildMessagesFromHistory(history, systemPrompt)
func (s *OpenAIService) BuildMessagesFromHistory(history []models.Message, systemPrompt string) []ChatMessage {
	messages := make([]ChatMessage, 0, len(history)+1)

	// 添加系统提示词
	if systemPrompt != "" {
		messages = append(messages, ChatMessage{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加历史消息
	for _, msg := range history {
		role := "user"
		if msg.IsOutgoing() {
			role = "assistant"
		}

		messages = append(messages, ChatMessage{
			Role:    role,
			Content: msg.Content,
		})
	}

	return messages
}

// GetDefaultSystemPrompt 获取默认系统提示词
// 思路：提供默认的客服系统提示词
// 使用例子：prompt := service.GetDefaultSystemPrompt()
func (s *OpenAIService) GetDefaultSystemPrompt() string {
	return `你是一个专业的客服助手，请遵循以下规则：
1. 保持友好、专业的语调
2. 尽量提供准确、有用的信息
3. 如果不确定答案，请诚实说明并建议联系人工客服
4. 回复要简洁明了，避免过长的回答
5. 如果用户要求转人工客服，请引导用户使用相应的转接方式`
}

// ValidateAPIKey 验证API密钥是否有效
// 思路：通过简单的API调用验证密钥有效性
// 使用例子：err := service.ValidateAPIKey(ctx)
func (s *OpenAIService) ValidateAPIKey(ctx context.Context) error {
	// 发送一个简单的请求来验证API密钥
	req := ChatCompletionRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "Hello"},
		},
		MaxTokens: 5,
	}

	_, err := s.ChatCompletion(ctx, req)
	if err != nil {
		return fmt.Errorf("API密钥验证失败: %w", err)
	}

	return nil
}

// 实现interfaces.OpenAIService接口

// LuaChatCompletion 聊天补全（Lua接口实现）
// 思路：为Lua脚本提供OpenAI调用接口
func (s *OpenAIService) LuaChatCompletion(ctx context.Context, messages []interfaces.OpenAIMessage) (map[string]interface{}, error) {
	// 转换消息格式
	var chatMessages []ChatMessage
	for _, msg := range messages {
		chatMessages = append(chatMessages, ChatMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// 构建请求
	temp := float32(0.7)
	req := ChatCompletionRequest{
		Messages:    chatMessages,
		MaxTokens:   1000,
		Temperature: &temp,
	}

	// 调用原有方法
	response, err := s.ChatCompletion(ctx, req)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}, err
	}

	// 转换为通用格式
	result := map[string]interface{}{
		"success": true,
		"choices": []map[string]interface{}{
			{
				"message": map[string]interface{}{
					"content": response.Content,
				},
			},
		},
	}

	return result, nil
}

// SimpleChat 简单聊天（接口实现）
// 思路：为Lua脚本提供简化的聊天接口
func (s *OpenAIService) SimpleChat(ctx context.Context, userMessage, systemPrompt string) (string, error) {
	messages := []ChatMessage{
		{
			Role:    "system",
			Content: systemPrompt,
		},
		{
			Role:    "user",
			Content: userMessage,
		},
	}

	temp := float32(0.7)
	req := ChatCompletionRequest{
		Messages:    messages,
		MaxTokens:   1000,
		Temperature: &temp,
	}

	response, err := s.ChatCompletion(ctx, req)
	if err != nil {
		return "", err
	}

	return response.Content, nil
}
