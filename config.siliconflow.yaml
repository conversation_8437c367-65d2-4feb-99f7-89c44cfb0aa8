# 硅基流动配置示例
# 思路：提供硅基流动的完整配置示例，用户可以直接复制使用
# 使用方法：将此文件重命名为 config.yaml 或复制内容到现有配置文件

server:
  host: "0.0.0.0"
  port: 8080

database:
  type: "sqlite"
  database: "data/aike.db"

# 硅基流动 OpenAI 兼容配置
openai:
  api_key: "your_siliconflow_api_key_here"  # 从 https://cloud.siliconflow.cn/account/ak 获取
  base_url: "https://api.siliconflow.cn/v1"  # 硅基流动 API 地址
  model: "Qwen/Qwen2.5-72B-Instruct"        # 推荐的高性能模型

# 其他可用的硅基流动模型：
# model: "deepseek-ai/DeepSeek-V3"           # DeepSeek V3 模型
# model: "meta-llama/Llama-3.1-8B-Instruct" # Llama 3.1 模型
# model: "01-ai/Yi-1.5-34B-Chat"            # Yi 1.5 模型
# model: "Qwen/Qwen2.5-32B-Instruct"        # Qwen 2.5 32B 模型
# model: "Pro/deepseek-ai/DeepSeek-R1"      # DeepSeek R1 推理模型（付费）

platforms:
  qq:
    enabled: true
    base_url: "http://localhost:3000"
    token: ""
  
  telegram:
    enabled: false
    token: ""
  
  wechat:
    enabled: false
    app_id: ""
    secret: ""
  
  qianniu:
    enabled: false
    app_key: ""
    secret: ""

# 硅基流动使用说明：
# 1. 注册账号：访问 https://cloud.siliconflow.cn/ 注册账号
# 2. 获取 API Key：进入 https://cloud.siliconflow.cn/account/ak 创建 API 密钥
# 3. 选择模型：访问 https://cloud.siliconflow.cn/models 查看可用模型
# 4. 配置密钥：将 API Key 填入上面的 api_key 字段
# 5. 启动服务：正常启动 AI 客服系统即可

# 硅基流动优势：
# - 完全兼容 OpenAI 接口，无需修改代码
# - 提供多种开源大模型选择
# - 价格相比 OpenAI 更便宜
# - 国内访问速度更快
# - 支持中文优化的模型
