UPDATE scripts SET content = '-- 聊天记录存储管理器
-- 思路：通过Lua脚本管理聊天记录的存储、查询和配置
-- 类型：message_handler
-- 优先级：50

-- 检查是否是语法验证模式，如果是则跳过实际处理
-- 调试：检查语法验证模式标志
log("info", "聊天存储脚本开始执行，检查语法验证模式: " .. tostring(__syntax_check_mode))
if __syntax_check_mode then
    log("info", "语法验证模式，跳过聊天存储处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content:lower()
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

log("info", "聊天存储管理器处理消息: " .. content .. " 来自用户: " .. user_id)

-- 自动存储当前消息
local session_id = get_var("session_id_" .. user_id)
if not session_id then
    session_id = platform .. "_" .. user_id
    set_var("session_id_" .. user_id, session_id)
end

local message_data = {
    id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
    user_id = user_id,
    platform = platform,
    content = message.content,
    message_type = message.message_type or "text",
    direction = "incoming",
    session_id = session_id,
    group_id = message.metadata and message.metadata.group_id or "",
    reply_to_id = message.metadata and message.metadata.reply_to_id or "",
    status = "received",
    is_ai_generated = false,
    ai_model = "",
    tokens_used = 0,
    parent_id = "",
    metadata = {
        platform_message_id = message.metadata and message.metadata.message_id or "",
        sender_nickname = message.from and message.from.nickname or "",
        sender_avatar = message.from and message.from.avatar or "",
        message_source = "napcat_websocket",
        processing_time = os.time(),
        filter_passed = true,
        auto_reply = false
    }
}

local store_success, store_message_text = store_message(message_data)
if store_success then
    log("info", "消息已自动存储: " .. message_data.id .. " (用户: " .. user_id .. ", 平台: " .. platform .. ")")
    set_var("last_message_id_" .. user_id, message_data.id)
    set_var("last_message_time_" .. user_id, os.time())
else
    log("warn", "消息存储失败: " .. (store_message_text or "未知错误"))
end

log("info", "聊天存储管理器处理完成，消息继续传递")
return nil' WHERE name = '聊天记录存储管理器';
