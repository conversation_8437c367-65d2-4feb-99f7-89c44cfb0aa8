# 🔧 Lua脚本系统使用指南

AI客服系统集成了强大的Lua脚本引擎，支持自定义消息处理逻辑和业务规则。

## 📁 目录结构

```
scripts/
├── README.md           # 本文档
├── config.yaml         # 脚本配置文件
├── filters/            # 消息过滤器脚本
│   └── spam_filter.lua
├── handlers/           # 消息处理器脚本
│   └── auto_reply.lua
└── rules/              # 业务规则脚本
    └── work_hours.lua
```

## 🎯 脚本类型

### 1. 消息过滤器 (`message_filter`)
- **用途**: 过滤和预处理接收到的消息
- **执行时机**: 消息接收后，处理前
- **返回值**: `true`(通过) / `false`(拦截)
- **示例**: 垃圾消息过滤、敏感词检测

### 2. 消息处理器 (`message_handler`)
- **用途**: 处理消息并生成响应
- **执行时机**: 消息通过过滤器后
- **返回值**: 字符串(处理结果) / `nil`(未处理)
- **示例**: 自动回复、关键词响应

### 3. 业务规则 (`business_rule`)
- **用途**: 执行业务逻辑和规则验证
- **执行时机**: 消息处理流程中
- **返回值**: 字符串(规则结果)
- **示例**: 工作时间检查、权限验证

## 🔧 脚本API

### 日志函数
```lua
log("info", "这是一条信息日志")
log("warn", "这是一条警告日志")
log("error", "这是一条错误日志")
```

### 消息发送
```lua
-- 发送消息给用户
send_message(user_id, "回复内容")
```

### 变量管理
```lua
-- 设置变量
set_var("key", "value")

-- 获取变量
local value = get_var("key")
```

### 时间函数
```lua
-- 获取当前时间戳
local timestamp = now()

-- 格式化时间
local time_str = os.date("%Y-%m-%d %H:%M:%S")
```

## 📝 上下文变量

### message 对象
```lua
message.content         -- 消息内容
message.platform        -- 平台名称 (qq, telegram, etc.)
message.from.platform_user_id  -- 发送者ID
message.from.nickname    -- 发送者昵称
message.timestamp       -- 消息时间戳
```

### user 对象
```lua
user.id                 -- 用户ID
user.nickname           -- 用户昵称
user.platform           -- 用户平台
```

### session 对象
```lua
session.id              -- 会话ID
session.status          -- 会话状态
```

## 🚀 快速开始

### 1. 创建脚本文件
在对应目录下创建 `.lua` 文件：

```lua
-- scripts/filters/my_filter.lua
if message and message.content then
    log("info", "处理消息: " .. message.content)
    return true
end
return false
```

### 2. 配置脚本
在 `config.yaml` 中添加配置：

```yaml
scripts:
  - name: "我的过滤器"
    description: "自定义消息过滤器"
    type: "message_filter"
    priority: 100
    enabled: true
    file: "filters/my_filter.lua"
```

### 3. 加载脚本
```bash
go run cmd/load_scripts/main.go
```

## 🛠️ 管理工具

### 脚本管理器
```bash
# 列出所有脚本
go run cmd/script_manager/main.go list

# 查看脚本详情
go run cmd/script_manager/main.go show <script_id>

# 删除脚本
go run cmd/script_manager/main.go delete <script_id>

# 查看脚本类型
go run cmd/script_manager/main.go types
```

### API接口
```bash
# 获取脚本列表
GET /api/v1/scripts/

# 获取脚本详情
GET /api/v1/scripts/{id}

# 创建脚本
POST /api/v1/scripts/

# 更新脚本
PUT /api/v1/scripts/{id}

# 删除脚本
DELETE /api/v1/scripts/{id}

# 执行脚本
POST /api/v1/scripts/{id}/execute

# 获取脚本类型
GET /api/v1/scripts/types
```

## 📋 脚本示例

### 垃圾消息过滤器
```lua
local spam_keywords = {"广告", "推广", "加微信"}

if message and message.content then
    local content = message.content:lower()
    for _, keyword in ipairs(spam_keywords) do
        if content:find(keyword) then
            log("warn", "检测到垃圾消息: " .. keyword)
            return false  -- 拦截消息
        end
    end
end

return true  -- 通过过滤
```

### 自动回复处理器
```lua
if not message or not message.content then
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id

if content == "你好" then
    send_message(user_id, "您好！欢迎使用AI客服系统")
    return "已发送问候回复"
end

if content == "帮助" then
    send_message(user_id, "可用命令: 你好, 帮助, 时间, 状态")
    return "已发送帮助信息"
end

return nil  -- 未处理
```

### 工作时间检查
```lua
local current_hour = tonumber(os.date("%H"))
local current_day = tonumber(os.date("%w"))

-- 工作时间: 周一到周五 9:00-18:00
local is_workday = current_day >= 1 and current_day <= 5
local is_workhour = current_hour >= 9 and current_hour < 18

if not (is_workday and is_workhour) then
    if message and message.from then
        local user_id = message.from.platform_user_id
        send_message(user_id, "现在是非工作时间，工作时间会及时回复您")
        return "非工作时间自动回复"
    end
end

return "工作时间内，继续处理"
```

## 🎉 优势总结

✅ **独立文件编辑** - 每个脚本都是独立的.lua文件，支持语法高亮  
✅ **配置化管理** - 通过YAML配置文件统一管理  
✅ **热加载支持** - 无需重启服务器即可加载新脚本  
✅ **类型化组织** - 按功能类型组织脚本目录  
✅ **丰富的API** - 提供完整的消息处理和系统交互API  
✅ **管理工具** - 命令行工具和REST API双重管理方式  

现在你可以轻松地编辑 `scripts/` 目录下的 `.lua` 文件来自定义你的业务逻辑！
