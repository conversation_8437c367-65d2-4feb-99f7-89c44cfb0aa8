package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"aike_go/internal/adapters"
	"aike_go/internal/server"
	"aike_go/internal/services"
)

// mainRefactored 重构后的服务器启动入口
// 思路：使用模块化的服务器架构，提高代码可维护性和可测试性
// 注意：这是一个示例文件，实际使用时需要重命名为main()
func mainRefactored() {
	log.Println("🚀 启动AI客服系统 (重构版本)...")

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化配置
	config := server.DefaultServerConfig()

	// 可以从配置文件或环境变量中加载配置
	// config = loadConfigFromFile("config.yaml")
	// config = loadConfigFromEnv(config)

	// 初始化服务依赖
	deps, err := initializeDependencies(ctx)
	if err != nil {
		log.Fatalf("初始化服务依赖失败: %v", err)
	}

	// 创建重构后的服务器
	srv := server.NewRefactoredServer(config, deps)

	// 设置优雅关闭
	setupGracefulShutdown(srv, cancel)

	// 启动服务器
	log.Printf("🌐 服务器启动在 %s", config.GetAddress())
	if err := srv.Start(ctx); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// initializeDependencies 初始化服务依赖
// 思路：集中初始化所有服务依赖，便于管理和测试
// 使用例子：deps, err := initializeDependencies(ctx)
func initializeDependencies(ctx context.Context) (*server.ServerDependencies, error) {
	log.Println("📦 初始化服务依赖...")

	// 注意：这是一个示例实现，实际使用时需要根据具体的服务构造函数进行调整

	// 初始化适配器管理器（需要根据实际的构造函数参数进行调整）
	// adapterManager := adapters.NewAdapterManager(config, messageHandler)
	var adapterManager *adapters.AdapterManager = nil // 暂时设为nil，实际使用时需要正确初始化

	// 初始化聊天服务（需要根据实际的构造函数参数进行调整）
	// chatService := services.NewChatService(config)
	var chatService *services.ChatService = nil // 暂时设为nil，实际使用时需要正确初始化

	// 初始化知识库服务（需要根据实际的构造函数参数进行调整）
	// knowledgeService := services.NewKnowledgeService()
	var knowledgeService *services.KnowledgeService = nil // 暂时设为nil，实际使用时需要正确初始化

	// 初始化脚本服务（需要根据实际的构造函数参数进行调整）
	// scriptService := services.NewScriptService()
	var scriptService *services.ScriptService = nil // 暂时设为nil，实际使用时需要正确初始化

	// 初始化WebSocket服务（需要根据实际的构造函数参数进行调整）
	// wsService := services.NewWebSocketService()
	var wsService *services.WebSocketService = nil // 暂时设为nil，实际使用时需要正确初始化

	// 构建依赖项
	deps := &server.ServerDependencies{
		AdapterManager:   adapterManager,
		ChatService:      chatService,
		KnowledgeService: knowledgeService,
		ScriptService:    scriptService,
		WSService:        wsService,
	}

	log.Println("✅ 服务依赖初始化完成（示例版本）")
	return deps, nil
}

// setupGracefulShutdown 设置优雅关闭
// 思路：监听系统信号，优雅地关闭服务器
// 使用例子：setupGracefulShutdown(server, cancel)
func setupGracefulShutdown(srv *server.RefactoredServer, cancel context.CancelFunc) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动信号监听协程
	go func() {
		sig := <-sigChan
		log.Printf("🛑 收到信号 %v，开始优雅关闭...", sig)

		// 取消上下文
		cancel()

		// 停止服务器
		if err := srv.Stop(); err != nil {
			log.Printf("❌ 服务器关闭失败: %v", err)
			os.Exit(1)
		}

		log.Println("✅ 服务器已优雅关闭")
		os.Exit(0)
	}()
}

// loadConfigFromFile 从文件加载配置
// 思路：支持从YAML或JSON文件加载配置
// 使用例子：config := loadConfigFromFile("config.yaml")
func loadConfigFromFile(filename string) *server.ServerConfig {
	// 这里可以实现从文件加载配置的逻辑
	// 支持YAML、JSON等格式
	log.Printf("📄 从文件加载配置: %s", filename)

	// 暂时返回默认配置
	return server.DefaultServerConfig()
}

// loadConfigFromEnv 从环境变量加载配置
// 思路：支持通过环境变量覆盖配置项
// 使用例子：config := loadConfigFromEnv(defaultConfig)
func loadConfigFromEnv(config *server.ServerConfig) *server.ServerConfig {
	log.Println("🌍 从环境变量加载配置...")

	// 从环境变量中读取配置
	if host := os.Getenv("SERVER_HOST"); host != "" {
		config.Host = host
	}

	if port := os.Getenv("SERVER_PORT"); port != "" {
		// 转换端口号
		// config.Port = convertPort(port)
	}

	if mode := os.Getenv("GIN_MODE"); mode != "" {
		config.Mode = mode
	}

	// 可以添加更多环境变量配置
	return config
}

// 示例：如何扩展服务器功能
//
// 1. 添加新的处理器：
//    - 在 internal/server/handlers/ 目录下创建新的处理器文件
//    - 在 RefactoredServer 中添加处理器字段
//    - 在 initHandlers() 方法中初始化处理器
//    - 在路由管理器中添加相应的路由
//
// 2. 添加新的服务：
//    - 在 internal/services/ 目录下创建新的服务文件
//    - 在 ServerDependencies 中添加服务字段
//    - 在 initializeDependencies() 方法中初始化服务
//
// 3. 添加新的中间件：
//    - 在 initMiddleware() 方法中添加中间件
//    - 可以根据配置条件性地启用中间件
//
// 4. 添加新的配置项：
//    - 在 ServerConfig 结构体中添加配置字段
//    - 在 DefaultServerConfig() 中设置默认值
//    - 在 Validate() 方法中添加验证逻辑
//
// 5. 测试：
//    - 每个处理器都可以独立测试
//    - 可以通过依赖注入模拟服务
//    - 可以通过 GetEngine() 方法获取Gin引擎进行集成测试

// 使用示例：
//
// // 创建测试服务器
// func createTestServer() *server.RefactoredServer {
//     config := server.DefaultServerConfig()
//     config.Port = 0 // 使用随机端口
//
//     deps := &server.ServerDependencies{
//         AdapterManager:   mockAdapterManager,
//         ChatService:      mockChatService,
//         KnowledgeService: mockKnowledgeService,
//         ScriptService:    mockScriptService,
//         WSService:        mockWSService,
//     }
//
//     return server.NewRefactoredServer(config, deps)
// }
//
// // 测试API端点
// func TestPlatformAPI(t *testing.T) {
//     srv := createTestServer()
//     engine := srv.GetEngine()
//
//     w := httptest.NewRecorder()
//     req, _ := http.NewRequest("GET", "/api/v1/platforms/", nil)
//     engine.ServeHTTP(w, req)
//
//     assert.Equal(t, 200, w.Code)
// }
