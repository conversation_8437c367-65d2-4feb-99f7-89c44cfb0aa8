package models

import (
	"time"
	"gorm.io/gorm"
)

// Session 会话模型
// 思路：管理用户会话状态，支持上下文保持和会话统计
// 使用例子：session := &Session{UserID: 1, Platform: "qq", Status: "active"}
type Session struct {
	ID        uint           `gorm:"primarykey" json:"id"`                     // 主键ID
	CreatedAt time.Time      `json:"created_at"`                               // 创建时间
	UpdatedAt time.Time      `json:"updated_at"`                               // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`        // 软删除时间
	
	// 关联用户
	UserID uint `gorm:"not null;index" json:"user_id"`                        // 用户ID
	User   User `gorm:"foreignKey:UserID" json:"user,omitempty"`              // 关联用户
	
	// 会话标识
	SessionID string `gorm:"size:100;uniqueIndex;not null" json:"session_id"` // 会话唯一标识
	Platform  string `gorm:"size:20;not null" json:"platform"`                // 平台类型
	
	// 会话状态
	Status    string `gorm:"size:20;default:active" json:"status"`             // 会话状态：active, inactive, closed
	Title     string `gorm:"size:200" json:"title"`                            // 会话标题
	
	// 时间信息
	StartedAt    time.Time  `json:"started_at"`                               // 会话开始时间
	LastActiveAt time.Time  `json:"last_active_at"`                           // 最后活跃时间
	EndedAt      *time.Time `json:"ended_at"`                                 // 会话结束时间
	
	// 统计信息
	MessageCount int `gorm:"default:0" json:"message_count"`                   // 消息总数
	Duration     int `gorm:"default:0" json:"duration"`                        // 会话时长（秒）
	
	// AI相关
	AIModel      string `gorm:"size:50" json:"ai_model"`                      // 使用的AI模型
	TotalTokens  int    `gorm:"default:0" json:"total_tokens"`                // 总token使用量
	
	// 上下文信息
	Context      string `gorm:"type:text" json:"context"`                     // 会话上下文（JSON格式）
	SystemPrompt string `gorm:"type:text" json:"system_prompt"`               // 系统提示词
	
	// 扩展字段
	Metadata     string `gorm:"type:text" json:"metadata"`                    // 元数据（JSON格式）
	
	// 关联关系
	Messages []Message `gorm:"foreignKey:SessionID;references:SessionID" json:"messages,omitempty"` // 会话消息
}

// TableName 指定表名
func (Session) TableName() string {
	return "sessions"
}

// BeforeCreate GORM钩子：创建前处理
// 思路：设置默认值和初始化时间
func (s *Session) BeforeCreate(tx *gorm.DB) error {
	if s.Status == "" {
		s.Status = "active"
	}
	if s.StartedAt.IsZero() {
		s.StartedAt = time.Now()
	}
	if s.LastActiveAt.IsZero() {
		s.LastActiveAt = time.Now()
	}
	return nil
}

// IsActive 检查会话是否活跃
// 思路：根据状态判断会话是否可用
// 使用例子：if session.IsActive() { ... }
func (s *Session) IsActive() bool {
	return s.Status == "active"
}

// UpdateLastActive 更新最后活跃时间
// 思路：用户发送消息时更新会话活跃时间
// 使用例子：session.UpdateLastActive(db)
func (s *Session) UpdateLastActive(db *gorm.DB) error {
	s.LastActiveAt = time.Now()
	return db.Model(s).Update("last_active_at", s.LastActiveAt).Error
}

// IncrementMessageCount 增加消息计数
// 思路：新消息时增加计数器
// 使用例子：session.IncrementMessageCount(db)
func (s *Session) IncrementMessageCount(db *gorm.DB) error {
	return db.Model(s).Update("message_count", gorm.Expr("message_count + ?", 1)).Error
}

// AddTokens 增加token使用量
// 思路：AI回复时累加token使用量
// 使用例子：session.AddTokens(db, 150)
func (s *Session) AddTokens(db *gorm.DB, tokens int) error {
	s.TotalTokens += tokens
	return db.Model(s).Update("total_tokens", s.TotalTokens).Error
}

// Close 关闭会话
// 思路：结束会话并计算持续时间
// 使用例子：session.Close(db)
func (s *Session) Close(db *gorm.DB) error {
	now := time.Now()
	s.Status = "closed"
	s.EndedAt = &now
	s.Duration = int(now.Sub(s.StartedAt).Seconds())
	
	return db.Model(s).Updates(map[string]interface{}{
		"status":   s.Status,
		"ended_at": s.EndedAt,
		"duration": s.Duration,
	}).Error
}

// GetDurationMinutes 获取会话时长（分钟）
// 思路：将秒转换为分钟显示
// 使用例子：minutes := session.GetDurationMinutes()
func (s *Session) GetDurationMinutes() float64 {
	return float64(s.Duration) / 60.0
}

// IsExpired 检查会话是否过期
// 思路：根据最后活跃时间判断会话是否过期
// 使用例子：if session.IsExpired(30*time.Minute) { ... }
func (s *Session) IsExpired(timeout time.Duration) bool {
	return time.Since(s.LastActiveAt) > timeout
}

// SetContext 设置会话上下文
// 思路：保存会话的上下文信息，用于AI对话
// 使用例子：session.SetContext(db, contextData)
func (s *Session) SetContext(db *gorm.DB, context interface{}) error {
	// 这里应该将context转换为JSON字符串
	// 简化实现，实际需要使用json.Marshal
	return db.Model(s).Update("context", "{}").Error
}

// GetContext 获取会话上下文
// 思路：解析会话上下文信息
// 使用例子：context := session.GetContext()
func (s *Session) GetContext() (map[string]interface{}, error) {
	// 这里应该将JSON字符串转换为map
	// 简化实现，实际需要使用json.Unmarshal
	return map[string]interface{}{}, nil
}
