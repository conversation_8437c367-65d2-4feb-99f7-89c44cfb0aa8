package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// LogLevel 日志级别
// 思路：定义不同的日志级别，便于过滤和管理
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelInfo:
		return "INFO"
	case LogLevelWarn:
		return "WARN"
	case LogLevelError:
		return "ERROR"
	case LogLevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// LogEntry 日志条目结构体
// 思路：统一的日志格式，包含所有必要信息
type LogEntry struct {
	Timestamp    time.Time              `json:"timestamp"`     // 时间戳
	Level        string                 `json:"level"`         // 日志级别
	Message      string                 `json:"message"`       // 日志消息
	Method       string                 `json:"method"`        // HTTP方法
	Path         string                 `json:"path"`          // 请求路径
	StatusCode   int                    `json:"status_code"`   // 响应状态码
	Duration     time.Duration          `json:"duration"`      // 请求耗时
	ClientIP     string                 `json:"client_ip"`     // 客户端IP
	UserAgent    string                 `json:"user_agent"`    // 用户代理
	RequestID    string                 `json:"request_id"`    // 请求ID
	UserID       string                 `json:"user_id"`       // 用户ID
	Platform     string                 `json:"platform"`      // 平台
	RequestBody  string                 `json:"request_body"`  // 请求体（敏感信息已脱敏）
	ResponseBody string                 `json:"response_body"` // 响应体（敏感信息已脱敏）
	Error        string                 `json:"error"`         // 错误信息
	Extra        map[string]interface{} `json:"extra"`         // 额外信息
}

// Logger 日志记录器
// 思路：封装日志功能，支持文件输出和格式化
type Logger struct {
	level      LogLevel
	output     io.Writer
	enableFile bool
	logDir     string
}

// NewLogger 创建新的日志记录器
// 思路：初始化日志记录器，支持控制台和文件输出
// 使用例子：logger := NewLogger(LogLevelInfo, true, "logs")
func NewLogger(level LogLevel, enableFile bool, logDir string) *Logger {
	logger := &Logger{
		level:      level,
		output:     os.Stdout,
		enableFile: enableFile,
		logDir:     logDir,
	}

	if enableFile {
		if err := os.MkdirAll(logDir, 0755); err != nil {
			log.Printf("创建日志目录失败: %v", err)
		}
	}

	return logger
}

// LoggerMiddleware 日志中间件
// 思路：记录所有HTTP请求的详细信息
// 使用例子：router.Use(middleware.LoggerMiddleware())
func LoggerMiddleware() gin.HandlerFunc {
	logger := NewLogger(LogLevelInfo, true, "logs")

	return func(c *gin.Context) {
		start := time.Now()
		requestID := generateRequestID()
		
		// 设置请求ID到上下文
		c.Set("request_id", requestID)

		// 读取请求体
		var requestBody string
		if c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				requestBody = sanitizeRequestBody(string(bodyBytes))
				// 重新设置请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// 创建响应写入器包装器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算耗时
		duration := time.Since(start)

		// 获取响应体
		responseBody := sanitizeResponseBody(writer.body.String())

		// 获取错误信息
		var errorMsg string
		if len(c.Errors) > 0 {
			errorMsg = c.Errors.String()
		}

		// 创建日志条目
		entry := LogEntry{
			Timestamp:    start,
			Level:        getLogLevel(c.Writer.Status()).String(),
			Message:      fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path),
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			StatusCode:   c.Writer.Status(),
			Duration:     duration,
			ClientIP:     c.ClientIP(),
			UserAgent:    c.Request.UserAgent(),
			RequestID:    requestID,
			UserID:       getUserID(c),
			Platform:     getPlatform(c),
			RequestBody:  requestBody,
			ResponseBody: responseBody,
			Error:        errorMsg,
			Extra:        getExtraInfo(c),
		}

		// 记录日志
		logger.LogEntry(entry)
	}
}

// responseWriter 响应写入器包装器
// 思路：捕获响应体内容用于日志记录
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// LogEntry 记录日志条目
// 思路：将日志条目写入文件和控制台
func (l *Logger) LogEntry(entry LogEntry) {
	// 检查日志级别
	entryLevel := parseLogLevel(entry.Level)
	if entryLevel < l.level {
		return
	}

	// 格式化日志
	logLine := l.formatLogEntry(entry)

	// 输出到控制台
	fmt.Fprint(l.output, logLine)

	// 输出到文件
	if l.enableFile {
		l.writeToFile(entry, logLine)
	}
}

// formatLogEntry 格式化日志条目
// 思路：根据环境选择JSON或文本格式
func (l *Logger) formatLogEntry(entry LogEntry) string {
	// 开发环境使用可读格式
	if gin.Mode() == gin.DebugMode {
		return fmt.Sprintf("[%s] %s | %s | %d | %v | %s | %s %s\n",
			entry.Timestamp.Format("2006-01-02 15:04:05"),
			entry.Level,
			entry.ClientIP,
			entry.StatusCode,
			entry.Duration,
			entry.RequestID,
			entry.Method,
			entry.Path,
		)
	}

	// 生产环境使用JSON格式
	jsonBytes, err := json.Marshal(entry)
	if err != nil {
		return fmt.Sprintf("日志序列化失败: %v\n", err)
	}
	return string(jsonBytes) + "\n"
}

// writeToFile 写入日志文件
// 思路：按日期分割日志文件，便于管理
func (l *Logger) writeToFile(entry LogEntry, logLine string) {
	date := entry.Timestamp.Format("2006-01-02")
	filename := filepath.Join(l.logDir, fmt.Sprintf("app-%s.log", date))

	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Printf("打开日志文件失败: %v", err)
		return
	}
	defer file.Close()

	if _, err := file.WriteString(logLine); err != nil {
		log.Printf("写入日志文件失败: %v", err)
	}
}

// 辅助函数

// generateRequestID 生成请求ID
// 思路：生成唯一的请求标识符
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// getLogLevel 根据状态码确定日志级别
// 思路：不同的HTTP状态码对应不同的日志级别
func getLogLevel(statusCode int) LogLevel {
	switch {
	case statusCode >= 500:
		return LogLevelError
	case statusCode >= 400:
		return LogLevelWarn
	case statusCode >= 300:
		return LogLevelInfo
	default:
		return LogLevelInfo
	}
}

// parseLogLevel 解析日志级别字符串
func parseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return LogLevelDebug
	case "INFO":
		return LogLevelInfo
	case "WARN":
		return LogLevelWarn
	case "ERROR":
		return LogLevelError
	case "FATAL":
		return LogLevelFatal
	default:
		return LogLevelInfo
	}
}

// getUserID 从上下文获取用户ID
// 思路：从请求上下文中提取用户信息
func getUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

// getPlatform 从上下文获取平台信息
// 思路：从请求路径或参数中提取平台信息
func getPlatform(c *gin.Context) string {
	// 从路径参数获取
	if platform := c.Param("platform"); platform != "" {
		return platform
	}
	
	// 从查询参数获取
	if platform := c.Query("platform"); platform != "" {
		return platform
	}
	
	return ""
}

// getExtraInfo 获取额外信息
// 思路：收集请求的额外上下文信息
func getExtraInfo(c *gin.Context) map[string]interface{} {
	extra := make(map[string]interface{})
	
	// 添加查询参数
	if len(c.Request.URL.RawQuery) > 0 {
		extra["query"] = c.Request.URL.RawQuery
	}
	
	// 添加重要的请求头
	if referer := c.GetHeader("Referer"); referer != "" {
		extra["referer"] = referer
	}
	
	if contentType := c.GetHeader("Content-Type"); contentType != "" {
		extra["content_type"] = contentType
	}
	
	return extra
}

// sanitizeRequestBody 脱敏请求体
// 思路：移除敏感信息如密码、token等
func sanitizeRequestBody(body string) string {
	if len(body) > 1000 {
		body = body[:1000] + "...[truncated]"
	}
	
	// 简单的敏感信息脱敏
	sensitiveFields := []string{"password", "token", "secret", "key"}
	for _, field := range sensitiveFields {
		if strings.Contains(strings.ToLower(body), field) {
			return "[SENSITIVE_DATA_REMOVED]"
		}
	}
	
	return body
}

// sanitizeResponseBody 脱敏响应体
// 思路：移除响应中的敏感信息
func sanitizeResponseBody(body string) string {
	if len(body) > 1000 {
		body = body[:1000] + "...[truncated]"
	}
	
	// 检查是否包含敏感信息
	if strings.Contains(strings.ToLower(body), "token") ||
		strings.Contains(strings.ToLower(body), "secret") ||
		strings.Contains(strings.ToLower(body), "password") {
		return "[SENSITIVE_DATA_REMOVED]"
	}
	
	return body
}
