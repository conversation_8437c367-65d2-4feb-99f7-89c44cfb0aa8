-- 聊天记录存储管理器
-- 思路：通过Lua脚本管理聊天记录的存储、查询和配置
-- 类型：message_handler
-- 优先级：50

-- 存储AI回复消息的全局函数
-- 思路：供其他脚本调用，用于存储AI生成的回复消息
function store_ai_reply_message(user_id, platform, reply_content, ai_model, tokens_used, parent_message_id, is_from_kb,
                                knowledge_id)
    -- 获取会话ID
    local reply_session_id = get_var("session_id_" .. user_id) or (platform .. "_" .. user_id)

    -- 构建AI回复消息数据
    local reply_data = {
        -- 基本信息
        id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
        user_id = user_id,
        platform = platform,
        content = reply_content,
        message_type = "text",
        direction = "outgoing",

        -- 会话和关联信息
        session_id = reply_session_id,
        group_id = "",
        reply_to_id = "",

        -- AI相关信息
        status = "sent",
        is_ai_generated = true,
        ai_model = ai_model or "unknown",
        tokens_used = tokens_used or 0,
        parent_id = parent_message_id or "",

        -- 扩展元数据
        metadata = {
            is_from_knowledge_base = is_from_kb or false,
            knowledge_id = knowledge_id or "",
            generation_time = os.time(),
            message_source = "ai_generated",
            processing_time = os.time(),
            filter_passed = true,
            auto_reply = true
        }
    }

    -- 存储AI回复
    local reply_store_success, reply_store_message = store_message(reply_data)
    if reply_store_success then
        log("info", "AI回复已存储: " .. reply_data.id .. " (用户: " .. user_id .. ", 模型: " .. (ai_model or "unknown") .. ")")

        -- 更新最后回复信息
        set_var("last_reply_id_" .. user_id, reply_data.id)
        set_var("last_reply_time_" .. user_id, os.time())

        return true, reply_data.id
    else
        log("warn", "AI回复存储失败: " .. (reply_store_message or "未知错误"))
        return false, reply_store_message
    end
end

-- 检查是否是语法验证模式，如果是则跳过实际处理
-- 调试：检查语法验证模式标志
log("info", "聊天存储脚本开始执行，检查语法验证模式: " .. tostring(__syntax_check_mode))
if __syntax_check_mode then
    log("info", "语法验证模式，跳过聊天存储处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content:lower()
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

log("info", "聊天存储管理器处理消息: " .. content .. " 来自用户: " .. user_id)

-- 1. 检查是否是插件管理命令
if content:find("^/plugin") or content:find("^!plugin") then
    local command_parts = {}
    for word in content:gmatch("%S+") do
        table.insert(command_parts, word)
    end

    if #command_parts < 2 then
        send_message(user_id, [[📋 插件管理命令帮助：
• /plugin list - 查看所有插件
• /plugin enable <name> - 启用插件
• /plugin disable <name> - 禁用插件
• /plugin config <name> - 查看插件配置
• /plugin stats - 查看插件统计
• /plugin storage stats - 查看存储统计]])
        return "已发送插件帮助信息"
    end

    local action = command_parts[2]

    -- 列出所有插件
    if action == "list" then
        local plugins = list_plugins()
        if plugins and #plugins > 0 then
            local plugin_list = "🔌 已安装的插件：\n"
            for i, plugin in ipairs(plugins) do
                local status = plugin.enabled and "✅" or "❌"
                plugin_list = plugin_list .. string.format("%s %s v%s - %s\n",
                    status, plugin.name, plugin.version, plugin.description)
            end
            send_message(user_id, plugin_list)
        else
            send_message(user_id, "❌ 没有找到任何插件")
        end
        return "已发送插件列表"
    end

    -- 启用插件
    if action == "enable" and #command_parts >= 3 then
        local plugin_name = command_parts[3]
        local success, message_text = enable_plugin(plugin_name)
        if success then
            send_message(user_id, "✅ " .. message_text)
        else
            send_message(user_id, "❌ " .. message_text)
        end
        return "插件启用操作完成"
    end

    -- 禁用插件
    if action == "disable" and #command_parts >= 3 then
        local plugin_name = command_parts[3]
        local success, message_text = disable_plugin(plugin_name)
        if success then
            send_message(user_id, "✅ " .. message_text)
        else
            send_message(user_id, "❌ " .. message_text)
        end
        return "插件禁用操作完成"
    end

    -- 查看插件配置
    if action == "config" and #command_parts >= 3 then
        local plugin_name = command_parts[3]
        local config = get_plugin_config(plugin_name)
        if config then
            local config_text = "⚙️ 插件 " .. plugin_name .. " 配置：\n"
            for key, value in pairs(config) do
                config_text = config_text .. "• " .. key .. ": " .. tostring(value) .. "\n"
            end
            send_message(user_id, config_text)
        else
            send_message(user_id, "❌ 无法获取插件配置")
        end
        return "已发送插件配置"
    end

    -- 查看插件统计
    if action == "stats" then
        local stats = get_plugin_stats()
        if stats then
            local stats_text = string.format([[📊 插件统计信息：
• 总插件数: %d
• 启用插件数: %d
• 系统状态: 正常运行]],
                stats.total_plugins or 0,
                stats.enabled_plugins or 0)
            send_message(user_id, stats_text)
        else
            send_message(user_id, "❌ 无法获取插件统计信息")
        end
        return "已发送插件统计"
    end

    -- 存储相关命令
    if action == "storage" and #command_parts >= 3 then
        local storage_action = command_parts[3]

        if storage_action == "stats" then
            local storage_stats = get_storage_stats()
            if storage_stats then
                local stats_text = string.format([[💾 存储统计信息：
• 总消息数: %d
• 总用户数: %d
• 总会话数: %d
• 存储大小: %.2f MB]],
                    storage_stats.total_messages or 0,
                    storage_stats.total_users or 0,
                    storage_stats.total_sessions or 0,
                    (storage_stats.storage_size or 0) / 1024 / 1024)
                send_message(user_id, stats_text)
            else
                send_message(user_id, "❌ 无法获取存储统计信息")
            end
            return "已发送存储统计"
        end

        if storage_action == "backup" then
            local backup_path = "/tmp/chat_backup_" .. os.date("%Y%m%d_%H%M%S") .. ".json"
            local success, message_text = backup_messages(backup_path)
            if success then
                send_message(user_id, "✅ 聊天记录备份完成: " .. backup_path)
            else
                send_message(user_id, "❌ 备份失败: " .. message_text)
            end
            return "备份操作完成"
        end
    end

    -- 未知命令
    send_message(user_id, "❌ 未知的插件命令，使用 /plugin 查看帮助")
    return "未知插件命令"
end

-- 2. 检查是否是聊天记录查询命令
if content:find("^/history") or content:find("^!history") then
    local command_parts = {}
    for word in content:gmatch("%S+") do
        table.insert(command_parts, word)
    end

    local limit = 10 -- 默认显示10条
    if #command_parts >= 2 then
        local num = tonumber(command_parts[2])
        if num and num > 0 and num <= 50 then
            limit = num
        end
    end

    -- 获取用户的对话历史
    local conversation = get_conversation(user_id, platform, limit)
    if conversation and #conversation > 0 then
        local history_text = "📜 最近 " .. #conversation .. " 条对话记录：\n\n"

        for i = #conversation, 1, -1 do -- 倒序显示，最新的在下面
            local msg = conversation[i]
            local time_str = msg.timestamp or msg.created_at or "未知时间"
            local direction_icon = msg.direction == "incoming" and "👤" or "🤖"
            local content_preview = msg.content

            -- 限制内容长度
            if string.len(content_preview) > 50 then
                content_preview = string.sub(content_preview, 1, 50) .. "..."
            end

            history_text = history_text .. string.format("%s %s: %s\n",
                direction_icon, time_str, content_preview)
        end

        send_message(user_id, history_text)
    else
        send_message(user_id, "📜 没有找到历史对话记录")
    end
    return "已发送对话历史"
end

-- 3. 检查是否是消息搜索命令
if content:find("^/search") or content:find("^!search") then
    local command_parts = {}
    for word in content:gmatch("%S+") do
        table.insert(command_parts, word)
    end

    if #command_parts < 2 then
        send_message(user_id, "🔍 使用方法: /search <关键词> [数量]")
        return "搜索命令格式错误"
    end

    local keyword = command_parts[2]
    local limit = 10
    if #command_parts >= 3 then
        local num = tonumber(command_parts[3])
        if num and num > 0 and num <= 20 then
            limit = num
        end
    end

    -- 搜索消息
    local search_results = search_messages(keyword, {
        platform = platform,
        limit = limit
    })

    if search_results and #search_results > 0 then
        local results_text = "🔍 搜索 \"" .. keyword .. "\" 找到 " .. #search_results .. " 条结果：\n\n"

        for i, msg in ipairs(search_results) do
            local time_str = msg.timestamp or msg.created_at or "未知时间"
            local direction_icon = msg.direction == "incoming" and "👤" or "🤖"

            -- 高亮关键词
            local highlighted_content = msg.content:gsub(keyword, "【" .. keyword .. "】")

            results_text = results_text .. string.format("%d. %s %s: %s\n\n",
                i, direction_icon, time_str, highlighted_content)
        end

        send_message(user_id, results_text)
    else
        send_message(user_id, "🔍 没有找到包含 \"" .. keyword .. "\" 的消息")
    end
    return "已发送搜索结果"
end

-- 4. 自动存储当前消息（完善的存储逻辑）
-- 思路：包含所有重要字段，与原 ProcessMessage 中的存储逻辑保持一致

-- 获取或生成会话ID
local session_id = get_var("session_id_" .. user_id)
if not session_id then
    session_id = platform .. "_" .. user_id
    set_var("session_id_" .. user_id, session_id)
end

-- 构建完整的消息数据
local message_data = {
    -- 基本信息
    id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
    user_id = user_id,
    platform = platform,
    content = message.content,
    message_type = message.message_type or "text",
    direction = "incoming",

    -- 会话和关联信息
    session_id = session_id,
    group_id = message.metadata and message.metadata.group_id or "",
    reply_to_id = message.metadata and message.metadata.reply_to_id or "",

    -- 状态和元数据
    status = "received",
    is_ai_generated = false,
    ai_model = "",
    tokens_used = 0,
    parent_id = "",

    -- 扩展元数据
    metadata = {
        platform_message_id = message.metadata and message.metadata.message_id or "",
        sender_nickname = message.from and message.from.nickname or "",
        sender_avatar = message.from and message.from.avatar or "",
        message_source = "napcat_websocket",
        processing_time = os.time(),
        filter_passed = true,
        auto_reply = false
    }
}

-- 尝试存储消息
local store_success, store_message_text = store_message(message_data)
if store_success then
    log("info", "消息已自动存储: " .. message_data.id .. " (用户: " .. user_id .. ", 平台: " .. platform .. ")")

    -- 存储成功后，设置消息ID供后续脚本使用
    set_var("last_message_id_" .. user_id, message_data.id)
    set_var("last_message_time_" .. user_id, os.time())
else
    log("warn", "消息存储失败: " .. (store_message_text or "未知错误"))
end

-- 5. 如果不是特殊命令，返回nil让其他脚本处理
log("info", "聊天存储管理器处理完成，消息继续传递")
return nil
