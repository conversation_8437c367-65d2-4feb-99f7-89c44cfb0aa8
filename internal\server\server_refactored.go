package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"aike_go/internal/adapters"
	"aike_go/internal/server/handlers"
	"aike_go/internal/services"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// RefactoredServer 重构后的服务器
// 思路：将原来的单体服务器拆分为多个模块，提高代码可维护性
// 使用例子：server := NewRefactoredServer(config, deps)
type RefactoredServer struct {
	// 基础组件
	config *ServerConfig
	engine *gin.Engine
	server *http.Server
	
	// 核心服务
	adapterManager   *adapters.AdapterManager
	chatService      *services.ChatService
	knowledgeService *services.KnowledgeService
	scriptService    *services.ScriptService
	wsService        *services.WebSocketService
	
	// 处理器
	messageHandlers   *handlers.MessageHandlers
	platformHandlers  *handlers.PlatformHandlers
	knowledgeHandlers *handlers.KnowledgeHandlers
	websocketHandlers *handlers.WebSocketHandlers
	monitorHandlers   *handlers.MonitorHandlers
	pageHandlers      *handlers.PageHandlers
	
	// 路由管理器
	routeManager *RouteManager
	
	// 运行时信息
	startTime time.Time
	ctx       context.Context
	cancel    context.CancelFunc
}

// ServerDependencies 服务器依赖项
// 思路：通过依赖注入的方式传递服务依赖，便于测试和扩展
// 使用例子：deps := &ServerDependencies{AdapterManager: manager, ...}
type ServerDependencies struct {
	AdapterManager   *adapters.AdapterManager
	ChatService      *services.ChatService
	KnowledgeService *services.KnowledgeService
	ScriptService    *services.ScriptService
	WSService        *services.WebSocketService
}

// NewRefactoredServer 创建重构后的服务器实例
// 思路：使用依赖注入模式初始化服务器，提高可测试性
// 使用例子：server := NewRefactoredServer(config, deps)
func NewRefactoredServer(config *ServerConfig, deps *ServerDependencies) *RefactoredServer {
	// 验证配置
	if err := config.Validate(); err != nil {
		log.Fatalf("服务器配置验证失败: %v", err)
	}
	
	// 设置Gin模式
	gin.SetMode(config.Mode)
	
	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	
	// 创建服务器实例
	server := &RefactoredServer{
		config:           config,
		engine:           gin.New(),
		adapterManager:   deps.AdapterManager,
		chatService:      deps.ChatService,
		knowledgeService: deps.KnowledgeService,
		scriptService:    deps.ScriptService,
		wsService:        deps.WSService,
		startTime:        time.Now(),
		ctx:              ctx,
		cancel:           cancel,
	}
	
	// 初始化处理器
	server.initHandlers()
	
	// 初始化中间件
	server.initMiddleware()
	
	// 初始化路由
	server.initRoutes()
	
	// 创建HTTP服务器
	server.server = &http.Server{
		Addr:         config.GetAddress(),
		Handler:      server.engine,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
	}
	
	return server
}

// initHandlers 初始化所有处理器
// 思路：集中初始化所有HTTP处理器，便于管理
func (s *RefactoredServer) initHandlers() {
	s.messageHandlers = handlers.NewMessageHandlers(s.chatService)
	s.platformHandlers = handlers.NewPlatformHandlers(s.adapterManager, s.chatService)
	s.knowledgeHandlers = handlers.NewKnowledgeHandlers(s.knowledgeService)
	s.websocketHandlers = handlers.NewWebSocketHandlers(s.wsService, nil) // NapCat处理器可以后续添加
	s.monitorHandlers = handlers.NewMonitorHandlers(s.adapterManager, s.wsService, s.startTime)
	s.pageHandlers = handlers.NewPageHandlers()
}

// initMiddleware 初始化中间件
// 思路：根据配置启用相应的中间件，提供灵活的配置选项
func (s *RefactoredServer) initMiddleware() {
	// 基础中间件
	s.engine.Use(gin.Logger())
	s.engine.Use(gin.Recovery())
	
	// CORS中间件
	if s.config.CORS.Enabled {
		corsConfig := cors.Config{
			AllowOrigins:     s.config.CORS.AllowOrigins,
			AllowMethods:     s.config.CORS.AllowMethods,
			AllowHeaders:     s.config.CORS.AllowHeaders,
			ExposeHeaders:    s.config.CORS.ExposeHeaders,
			AllowCredentials: s.config.CORS.AllowCredentials,
			MaxAge:           time.Duration(s.config.CORS.MaxAge) * time.Second,
		}
		s.engine.Use(cors.New(corsConfig))
	}
	
	// 可以在这里添加更多中间件：
	// - 限流中间件
	// - 认证中间件
	// - 监控中间件
	// - 日志中间件
}

// initRoutes 初始化路由
// 思路：使用路由管理器统一管理所有路由
func (s *RefactoredServer) initRoutes() {
	s.routeManager = NewRouteManager(s)
	s.routeManager.SetupRoutes()
}

// Start 启动服务器
// 思路：启动所有服务组件，包括适配器管理器和HTTP服务器
// 使用例子：err := server.Start(ctx)
func (s *RefactoredServer) Start(ctx context.Context) error {
	log.Printf("正在启动重构后的服务器...")
	
	// 启动适配器管理器
	if s.adapterManager != nil {
		if err := s.adapterManager.Start(ctx); err != nil {
			return fmt.Errorf("启动适配器管理器失败: %w", err)
		}
	}
	
	// 启动HTTP服务器
	log.Printf("HTTP服务器启动在 %s", s.config.GetAddress())
	
	if s.config.TLS.Enabled {
		return s.server.ListenAndServeTLS(s.config.TLS.CertFile, s.config.TLS.KeyFile)
	}
	
	return s.server.ListenAndServe()
}

// Stop 停止服务器
// 思路：优雅地关闭所有服务组件
// 使用例子：err := server.Stop()
func (s *RefactoredServer) Stop() error {
	log.Printf("正在停止服务器...")
	
	// 创建关闭上下文
	shutdownCtx, cancel := context.WithTimeout(context.Background(), s.config.ShutdownTimeout)
	defer cancel()
	
	// 停止HTTP服务器
	if err := s.server.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP服务器关闭失败: %v", err)
	}
	
	// 停止适配器管理器
	if s.adapterManager != nil {
		if err := s.adapterManager.Stop(); err != nil {
			log.Printf("适配器管理器停止失败: %v", err)
		}
	}
	
	// 取消上下文
	s.cancel()
	
	log.Printf("服务器已停止")
	return nil
}

// GetEngine 获取Gin引擎
// 思路：提供对Gin引擎的访问，便于测试和扩展
// 使用例子：engine := server.GetEngine()
func (s *RefactoredServer) GetEngine() *gin.Engine {
	return s.engine
}

// GetConfig 获取服务器配置
// 思路：提供对配置的只读访问
// 使用例子：config := server.GetConfig()
func (s *RefactoredServer) GetConfig() *ServerConfig {
	return s.config
}

// GetStartTime 获取服务器启动时间
// 思路：提供启动时间信息，用于监控和统计
// 使用例子：startTime := server.GetStartTime()
func (s *RefactoredServer) GetStartTime() time.Time {
	return s.startTime
}

// healthCheck 健康检查处理器
// 思路：提供简单的健康检查端点
// 使用例子：GET /health
func (s *RefactoredServer) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"uptime":    time.Since(s.startTime).Seconds(),
	})
}

// handleWebSocket WebSocket处理器
// 思路：委托给WebSocket处理器处理连接
func (s *RefactoredServer) handleWebSocket(c *gin.Context) {
	s.websocketHandlers.HandleWebSocket(c)
}
