package handlers

import (
	"net/http"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

// PageHandlers 页面处理器
// 思路：将页面渲染相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewPageHandlers()
type PageHandlers struct {
	// 可以添加模板引擎或其他页面渲染相关的依赖
}

// NewPageHandlers 创建页面处理器
// 思路：初始化页面处理器
// 使用例子：handlers := NewPageHandlers()
func NewPageHandlers() *PageHandlers {
	return &PageHandlers{}
}

// IndexPage 首页处理器
// 思路：渲染系统首页，提供系统概览和导航
// 使用例子：GET /
func (h *PageHandlers) IndexPage(c *gin.Context) {
	// 检查是否存在静态首页文件
	indexPath := filepath.Join("web", "static", "index.html")
	if _, err := filepath.Abs(indexPath); err == nil {
		c.File(indexPath)
		return
	}

	// 如果没有静态文件，返回简单的HTML页面
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .nav-card {
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .nav-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }
        .nav-card h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .nav-card p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能客服系统</h1>
            <p>多平台智能客服解决方案</p>
            <span class="status online">系统运行中</span>
        </div>
        
        <div class="nav-grid">
            <a href="/admin" class="nav-card">
                <h3>📊 管理后台</h3>
                <p>系统管理、配置设置、数据统计</p>
            </a>
            
            <a href="/amis" class="nav-card">
                <h3>🎛️ Amis管理界面</h3>
                <p>基于Amis的现代化管理界面</p>
            </a>
            
            <a href="/api/v1/platforms/" class="nav-card">
                <h3>🔌 平台接口</h3>
                <p>查看平台适配器状态和统计信息</p>
            </a>
            
            <a href="/health" class="nav-card">
                <h3>💚 健康检查</h3>
                <p>系统健康状态和运行指标</p>
            </a>
            
            <a href="/api/v1/monitor/metrics" class="nav-card">
                <h3>📈 系统监控</h3>
                <p>性能指标、内存使用、连接统计</p>
            </a>
            
            <a href="/ws" class="nav-card">
                <h3>🔗 WebSocket测试</h3>
                <p>WebSocket连接测试和调试</p>
            </a>
        </div>
    </div>
</body>
</html>`

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// AdminPage 管理页面处理器
// 思路：渲染管理后台页面，提供系统管理功能
// 使用例子：GET /admin
func (h *PageHandlers) AdminPage(c *gin.Context) {
	// 检查是否存在静态管理页面文件
	adminPath := filepath.Join("web", "static", "admin.html")
	if _, err := filepath.Abs(adminPath); err == nil {
		c.File(adminPath)
		return
	}

	// 如果没有静态文件，返回简单的管理页面
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - AI客服系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .back-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .admin-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 20px;
        }
        .admin-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .admin-section ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .admin-section li {
            margin-bottom: 8px;
        }
        .admin-section a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }
        .admin-section a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-link">← 返回首页</a>
            <h1>📊 管理后台</h1>
            <p>系统管理和配置中心</p>
        </div>
        
        <div class="admin-grid">
            <div class="admin-section">
                <h3>🔌 平台管理</h3>
                <ul>
                    <li><a href="/api/v1/platforms/">平台列表</a></li>
                    <li><a href="/api/v1/platforms/stats">平台统计</a></li>
                </ul>
            </div>
            
            <div class="admin-section">
                <h3>📚 知识库管理</h3>
                <ul>
                    <li><a href="/api/v1/knowledge/">知识列表</a></li>
                    <li><a href="/api/v1/knowledge/search">知识搜索</a></li>
                </ul>
            </div>
            
            <div class="admin-section">
                <h3>📜 脚本管理</h3>
                <ul>
                    <li><a href="/api/v1/scripts/">脚本列表</a></li>
                    <li><a href="/api/v1/scripts/types">脚本类型</a></li>
                </ul>
            </div>
            
            <div class="admin-section">
                <h3>🔗 WebSocket管理</h3>
                <ul>
                    <li><a href="/api/v1/ws/clients">客户端列表</a></li>
                    <li><a href="/ws">WebSocket测试</a></li>
                </ul>
            </div>
            
            <div class="admin-section">
                <h3>📈 系统监控</h3>
                <ul>
                    <li><a href="/api/v1/monitor/metrics">系统指标</a></li>
                    <li><a href="/api/v1/monitor/health/detailed">健康检查</a></li>
                </ul>
            </div>
            
            <div class="admin-section">
                <h3>💬 消息管理</h3>
                <ul>
                    <li><a href="/api/v1/messages/history">消息历史</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>`

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// AmisAdminPage Amis管理页面处理器
// 思路：渲染基于Amis的现代化管理界面
// 使用例子：GET /amis
func (h *PageHandlers) AmisAdminPage(c *gin.Context) {
	// 检查是否存在Amis页面文件
	amisPath := filepath.Join("web", "static", "amis.html")
	if _, err := filepath.Abs(amisPath); err == nil {
		c.File(amisPath)
		return
	}

	// 如果没有静态文件，返回基本的Amis页面框架
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amis管理界面 - AI客服系统</title>
    <link rel="stylesheet" href="https://unpkg.com/amis@latest/lib/themes/cxd.css" />
    <link rel="stylesheet" href="https://unpkg.com/amis@latest/lib/helper.css" />
    <link rel="stylesheet" href="https://unpkg.com/amis@latest/sdk/iconfont.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载Amis管理界面...</div>
    </div>
    
    <script src="https://unpkg.com/amis@latest/sdk/sdk.js"></script>
    <script>
        (function () {
            let amis = amisRequire('amis/embed');
            
            // 基本的Amis配置
            const schema = {
                type: 'page',
                title: 'AI客服系统管理',
                body: [
                    {
                        type: 'alert',
                        level: 'info',
                        body: '欢迎使用AI客服系统Amis管理界面！'
                    },
                    {
                        type: 'nav',
                        stacked: true,
                        links: [
                            {
                                label: '平台管理',
                                to: '/amis/platforms',
                                icon: 'fa fa-plug'
                            },
                            {
                                label: '知识库',
                                to: '/amis/knowledge',
                                icon: 'fa fa-book'
                            },
                            {
                                label: '脚本管理',
                                to: '/amis/scripts',
                                icon: 'fa fa-code'
                            },
                            {
                                label: '系统监控',
                                to: '/amis/monitor',
                                icon: 'fa fa-chart-line'
                            }
                        ]
                    }
                ]
            };
            
            let amisInstance = amis.embed('#root', schema, {
                // 可以在这里添加全局配置
            });
        })();
    </script>
</body>
</html>`

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}
