# AI客服系统 (aike_go)

基于Go语言开发的智能客服系统，集成OpenAI API、知识库管理、多平台消息适配器和Lua脚本引擎，支持QQ、微信、Telegram等平台。

## 🎯 功能特性

- 🤖 **AI对话** - 集成OpenAI GPT模型，提供智能对话服务
- 📚 **知识库** - 支持文档上传、向量化存储和语义搜索
- 💬 **多平台支持** - 支持QQ、微信、Telegram、千牛等主流平台
- 🔌 **Lua脚本引擎** - 支持自定义消息处理逻辑和业务规则
- 📊 **数据统计** - 提供详细的对话数据分析和统计
- 🔒 **安全可靠** - 完善的权限控制和数据安全保护
- 🌐 **WebSocket支持** - 实时消息推送和状态同步

## 支持的平台

- **QQ** - 基于NapCat API
- **微信** - 微信公众号/企业微信
- **Telegram** - Telegram Bot API
- **千牛** - 阿里千牛客服平台

## 快速开始

### 1. 环境要求

- Go 1.19+
- SQLite/MySQL/PostgreSQL（可选）

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：

```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here

# QQ平台配置（如需要）
QQ_ENABLED=true
QQ_BASE_URL=http://localhost:3000
QQ_TOKEN=your_qq_token_here
```

### 4. 启动服务

```bash
go run cmd/server/main.go
```

服务将在 `http://localhost:8080` 启动。

## API文档

### 健康检查

```http
GET /health
```

### 消息相关

```http
POST /api/v1/messages/send      # 发送消息
GET  /api/v1/messages/history   # 获取历史消息
```

### 知识库管理

```http
POST   /api/v1/knowledge/       # 创建知识条目
GET    /api/v1/knowledge/       # 获取知识列表
PUT    /api/v1/knowledge/:id    # 更新知识条目
DELETE /api/v1/knowledge/:id    # 删除知识条目
POST   /api/v1/knowledge/search # 搜索知识库
```

### 平台管理

```http
GET  /api/v1/platforms/                    # 获取平台列表
POST /api/v1/platforms/:platform/webhook  # 平台回调处理
```

### WebSocket

```
ws://localhost:8080/ws
```

## 项目结构

```
aike_go/
├── cmd/
│   └── server/          # 服务器入口
├── internal/
│   ├── config/          # 配置管理
│   ├── server/          # HTTP服务器
│   ├── models/          # 数据模型
│   ├── services/        # 业务逻辑
│   ├── handlers/        # 请求处理器
│   ├── adapters/        # 平台适配器
│   └── middleware/      # 中间件
├── pkg/                 # 公共包
├── docs/               # 文档
└── tests/              # 测试文件
```

## 开发计划

- [x] 项目初始化和基础架构
- [ ] 数据库设计和模型定义
- [ ] 配置管理系统
- [ ] OpenAI API集成
- [ ] 知识库系统
- [ ] 聊天记录存储
- [ ] HTTP API接口
- [ ] WebSocket实时通信
- [ ] 平台适配器框架
- [ ] QQ平台适配器
- [ ] 其他平台适配器
- [ ] 中间件和工具
- [ ] 测试和文档

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
