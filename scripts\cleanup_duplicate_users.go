package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 批量清理所有数据库文件中的重复用户数据
// 思路：遍历所有.db文件，检查并清理重复的platform_id记录
func main() {
	baseDir := "../data/distributed"

	// 遍历所有数据库文件
	err := filepath.Walk(baseDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.db文件
		if !strings.HasSuffix(path, ".db") {
			return nil
		}

		// 跳过空文件
		if info.Size() == 0 {
			log.Printf("跳过空文件: %s", path)
			return nil
		}

		log.Printf("处理数据库文件: %s", path)

		// 清理该数据库文件中的重复数据
		if err := cleanupDatabaseFile(path); err != nil {
			log.Printf("清理数据库文件 %s 失败: %v", path, err)
		} else {
			log.Printf("清理数据库文件 %s 成功", path)
		}

		return nil
	})
	if err != nil {
		log.Fatalf("遍历数据库文件失败: %v", err)
	}

	log.Println("所有数据库文件清理完成")
}

// cleanupDatabaseFile 清理单个数据库文件中的重复用户数据并触发表结构迁移
func cleanupDatabaseFile(dbPath string) error {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // 静默模式，减少日志输出
	})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 检查users表是否存在
	if !db.Migrator().HasTable("users") {
		log.Printf("数据库 %s 中没有users表，跳过", dbPath)
		return nil
	}

	// 1. 清理重复数据
	if err := cleanupDuplicateUsers(db, dbPath); err != nil {
		log.Printf("清理重复数据失败: %v", err)
	}

	// 2. 触发表结构迁移
	if err := migrateTableStructure(db, dbPath); err != nil {
		log.Printf("表结构迁移失败: %v", err)
	}

	return nil
}

// cleanupDuplicateUsers 清理重复的用户数据
func cleanupDuplicateUsers(db *gorm.DB, dbPath string) error {
	// 查找重复的platform_id
	var duplicates []struct {
		PlatformID string `gorm:"column:platform_id"`
		Count      int    `gorm:"column:count"`
	}

	err := db.Raw(`
		SELECT platform_id, COUNT(*) as count
		FROM users
		GROUP BY platform_id
		HAVING COUNT(*) > 1
	`).Scan(&duplicates).Error
	if err != nil {
		return fmt.Errorf("查找重复用户失败: %w", err)
	}

	if len(duplicates) == 0 {
		log.Printf("数据库 %s 中没有重复数据", dbPath)
		return nil
	}

	log.Printf("数据库 %s 中发现 %d 个重复的platform_id，开始清理...", dbPath, len(duplicates))

	totalCleaned := 0
	for _, dup := range duplicates {
		// 保留最新的记录（ID最大的），删除其他重复记录
		// 先获取最大ID
		var maxID uint
		err = db.Raw("SELECT MAX(id) FROM users WHERE platform_id = ?", dup.PlatformID).Scan(&maxID).Error
		if err != nil {
			log.Printf("获取用户 %s 最大ID失败: %v", dup.PlatformID, err)
			continue
		}

		// 删除非最大ID的记录
		result := db.Exec("DELETE FROM users WHERE platform_id = ? AND id != ?", dup.PlatformID, maxID)
		if result.Error != nil {
			log.Printf("清理重复用户 %s 失败: %v", dup.PlatformID, result.Error)
		} else {
			cleaned := int(result.RowsAffected)
			totalCleaned += cleaned
			log.Printf("清理重复用户 %s 成功，删除了 %d 条重复记录", dup.PlatformID, cleaned)
		}
	}

	log.Printf("数据库 %s 重复数据清理完成，总共删除了 %d 条重复记录", dbPath, totalCleaned)
	return nil
}

// migrateTableStructure 触发表结构迁移
func migrateTableStructure(db *gorm.DB, dbPath string) error {
	// 检查是否需要迁移
	needsMigration, err := checkIfMigrationNeeded(db)
	if err != nil {
		return fmt.Errorf("检查迁移需求失败: %w", err)
	}

	if !needsMigration {
		log.Printf("数据库 %s 已经是最新结构，无需迁移", dbPath)
		return nil
	}

	log.Printf("数据库 %s 需要表结构迁移，开始迁移...", dbPath)

	// 执行时间戳格式迁移
	if err := migrateTimestampFormat(db); err != nil {
		return fmt.Errorf("时间戳格式迁移失败: %w", err)
	}

	// 执行表结构迁移
	if err := migrateToOptimizedStructure(db); err != nil {
		return fmt.Errorf("表结构迁移失败: %w", err)
	}

	log.Printf("数据库 %s 表结构迁移完成", dbPath)
	return nil
}

// checkIfMigrationNeeded 检查是否需要迁移
func checkIfMigrationNeeded(db *gorm.DB) (bool, error) {
	// 检查messages表是否存在
	if !db.Migrator().HasTable("messages") {
		return false, nil // 没有messages表，不需要迁移
	}

	// 检查是否有旧的字段（如platform_msg_id）
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM pragma_table_info('messages') WHERE name = 'platform_msg_id'").Scan(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil // 如果有platform_msg_id字段，说明需要迁移
}

// migrateTimestampFormat 迁移时间戳格式
func migrateTimestampFormat(db *gorm.DB) error {
	// 迁移用户表的时间戳
	if db.Migrator().HasTable("users") {
		var userCount int64
		err := db.Raw("SELECT COUNT(*) FROM users WHERE typeof(created_at) = 'text'").Scan(&userCount).Error
		if err == nil && userCount > 0 {
			log.Printf("发现 %d 条用户记录需要时间戳格式迁移", userCount)
			err = db.Exec(`
				UPDATE users
				SET created_at = CAST(strftime('%s', created_at) AS INTEGER)
				WHERE typeof(created_at) = 'text'
			`).Error
			if err != nil {
				return fmt.Errorf("用户表时间戳迁移失败: %w", err)
			}
			log.Printf("用户表时间戳迁移完成，共迁移 %d 条记录", userCount)
		}
	}

	// 迁移消息表的时间戳
	if db.Migrator().HasTable("messages") {
		var msgCount int64
		err := db.Raw("SELECT COUNT(*) FROM messages WHERE typeof(created_at) = 'text'").Scan(&msgCount).Error
		if err == nil && msgCount > 0 {
			log.Printf("发现 %d 条消息记录需要时间戳格式迁移", msgCount)
			err = db.Exec(`
				UPDATE messages
				SET created_at = CAST(strftime('%s', created_at) AS INTEGER)
				WHERE typeof(created_at) = 'text'
			`).Error
			if err != nil {
				return fmt.Errorf("消息表时间戳迁移失败: %w", err)
			}
			log.Printf("消息表时间戳迁移完成，共迁移 %d 条记录", msgCount)
		}
	}

	return nil
}

// migrateToOptimizedStructure 迁移到优化的表结构
func migrateToOptimizedStructure(db *gorm.DB) error {
	if !db.Migrator().HasTable("messages") {
		return nil // 没有messages表，不需要迁移
	}

	log.Printf("检测到旧表结构，开始迁移到优化结构...")

	// 创建新的优化表结构（完全移除platform_msg_id等冗余字段）
	err := db.Exec(`
		CREATE TABLE IF NOT EXISTS messages_new (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			created_at INTEGER NOT NULL,
			user_id TEXT NOT NULL,
			content TEXT,
			type INTEGER NOT NULL DEFAULT 1,
			direction INTEGER NOT NULL,
			status INTEGER DEFAULT 1,
			parent_id TEXT
		)
	`).Error
	if err != nil {
		return fmt.Errorf("创建新表失败: %w", err)
	}

	// 迁移数据到新表
	err = db.Exec(`
		INSERT INTO messages_new (id, created_at, user_id, content, type, direction, status, parent_id)
		SELECT
			id,
			created_at,
			user_id,
			content,
			CASE
				WHEN typeof(type) = 'text' THEN
					CASE type
						WHEN 'text' THEN 1
						WHEN 'image' THEN 2
						WHEN 'file' THEN 3
						WHEN 'audio' THEN 4
						WHEN 'video' THEN 5
						ELSE 1
					END
				ELSE type
			END as type,
			CASE
				WHEN typeof(direction) = 'text' THEN
					CASE direction
						WHEN 'incoming' THEN 1
						WHEN 'outgoing' THEN 2
						ELSE 1
					END
				ELSE direction
			END as direction,
			CASE
				WHEN typeof(status) = 'text' THEN
					CASE status
						WHEN 'sent' THEN 1
						WHEN 'delivered' THEN 2
						WHEN 'read' THEN 3
						WHEN 'failed' THEN 4
						WHEN 'received' THEN 1
						ELSE 1
					END
				ELSE status
			END as status,
			parent_id
		FROM messages
	`).Error
	if err != nil {
		return fmt.Errorf("数据迁移失败: %w", err)
	}

	// 删除旧表
	err = db.Exec("DROP TABLE messages").Error
	if err != nil {
		return fmt.Errorf("删除旧表失败: %w", err)
	}

	// 重命名新表
	err = db.Exec("ALTER TABLE messages_new RENAME TO messages").Error
	if err != nil {
		return fmt.Errorf("重命名新表失败: %w", err)
	}

	// 创建优化的索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_messages_user_created ON messages(user_id, created_at)",
		"CREATE INDEX IF NOT EXISTS idx_messages_created ON messages(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type)",
		"CREATE INDEX IF NOT EXISTS idx_messages_direction ON messages(direction)",
		"CREATE INDEX IF NOT EXISTS idx_messages_parent ON messages(parent_id)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			log.Printf("创建索引失败: %v", err)
		}
	}

	log.Printf("表结构迁移完成，已移除冗余字段")
	return nil
}
