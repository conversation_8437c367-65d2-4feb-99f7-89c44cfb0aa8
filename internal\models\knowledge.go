package models

import (
	"time"
	"gorm.io/gorm"
)

// Knowledge 知识库模型
// 思路：存储知识库条目，支持向量搜索和分类管理
// 使用例子：kb := &Knowledge{Title: "常见问题", Content: "这是答案", Category: "FAQ"}
type Knowledge struct {
	ID        uint           `gorm:"primarykey" json:"id"`                     // 主键ID
	CreatedAt time.Time      `json:"created_at"`                               // 创建时间
	UpdatedAt time.Time      `json:"updated_at"`                               // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`        // 软删除时间
	
	// 知识内容
	Title       string `gorm:"size:200;not null" json:"title"`               // 知识标题
	Content     string `gorm:"type:text;not null" json:"content"`            // 知识内容
	Summary     string `gorm:"size:500" json:"summary"`                      // 内容摘要
	Keywords    string `gorm:"size:500" json:"keywords"`                     // 关键词（逗号分隔）
	
	// 分类和标签
	Category    string `gorm:"size:50" json:"category"`                      // 知识分类
	Tags        string `gorm:"size:200" json:"tags"`                         // 标签（逗号分隔）
	
	// 状态和权限
	Status      string `gorm:"size:20;default:active" json:"status"`         // 状态：active, inactive, draft
	Priority    int    `gorm:"default:0" json:"priority"`                    // 优先级（数字越大优先级越高）
	IsPublic    bool   `gorm:"default:true" json:"is_public"`                // 是否公开
	
	// 使用统计
	ViewCount   int `gorm:"default:0" json:"view_count"`                     // 查看次数
	UseCount    int `gorm:"default:0" json:"use_count"`                      // 使用次数
	LikeCount   int `gorm:"default:0" json:"like_count"`                     // 点赞次数
	
	// 向量搜索相关
	Embedding   string `gorm:"type:text" json:"embedding,omitempty"`         // 向量嵌入（JSON格式）
	EmbeddingModel string `gorm:"size:50" json:"embedding_model"`            // 嵌入模型
	
	// 创建者信息
	CreatedBy   uint   `gorm:"index" json:"created_by"`                      // 创建者ID
	UpdatedBy   uint   `gorm:"index" json:"updated_by"`                      // 更新者ID
	
	// 扩展字段
	Metadata    string `gorm:"type:text" json:"metadata"`                    // 元数据（JSON格式）
	Source      string `gorm:"size:200" json:"source"`                       // 知识来源
	SourceURL   string `gorm:"size:500" json:"source_url"`                   // 来源URL
}

// TableName 指定表名
func (Knowledge) TableName() string {
	return "knowledge"
}

// BeforeCreate GORM钩子：创建前处理
// 思路：设置默认值和生成摘要
func (k *Knowledge) BeforeCreate(tx *gorm.DB) error {
	if k.Status == "" {
		k.Status = "active"
	}
	if k.Summary == "" && len(k.Content) > 100 {
		// 自动生成摘要（取前100个字符）
		k.Summary = k.Content[:100] + "..."
	}
	return nil
}

// IsActive 检查知识条目是否激活
// 思路：根据状态判断知识是否可用
// 使用例子：if kb.IsActive() { ... }
func (k *Knowledge) IsActive() bool {
	return k.Status == "active"
}

// IncrementViewCount 增加查看次数
// 思路：用户查看知识时调用
// 使用例子：kb.IncrementViewCount(db)
func (k *Knowledge) IncrementViewCount(db *gorm.DB) error {
	return db.Model(k).Update("view_count", gorm.Expr("view_count + ?", 1)).Error
}

// IncrementUseCount 增加使用次数
// 思路：AI使用该知识回答问题时调用
// 使用例子：kb.IncrementUseCount(db)
func (k *Knowledge) IncrementUseCount(db *gorm.DB) error {
	return db.Model(k).Update("use_count", gorm.Expr("use_count + ?", 1)).Error
}

// IncrementLikeCount 增加点赞次数
// 思路：用户对知识点赞时调用
// 使用例子：kb.IncrementLikeCount(db)
func (k *Knowledge) IncrementLikeCount(db *gorm.DB) error {
	return db.Model(k).Update("like_count", gorm.Expr("like_count + ?", 1)).Error
}

// GetKeywordsList 获取关键词列表
// 思路：将逗号分隔的关键词字符串转换为切片
// 使用例子：keywords := kb.GetKeywordsList()
func (k *Knowledge) GetKeywordsList() []string {
	if k.Keywords == "" {
		return []string{}
	}
	// 这里可以添加字符串分割逻辑
	return []string{k.Keywords} // 简化实现，实际应该用strings.Split
}

// GetTagsList 获取标签列表
// 思路：将逗号分隔的标签字符串转换为切片
// 使用例子：tags := kb.GetTagsList()
func (k *Knowledge) GetTagsList() []string {
	if k.Tags == "" {
		return []string{}
	}
	// 这里可以添加字符串分割逻辑
	return []string{k.Tags} // 简化实现，实际应该用strings.Split
}

// SetEmbedding 设置向量嵌入
// 思路：将向量数组转换为JSON字符串存储
// 使用例子：kb.SetEmbedding([]float64{0.1, 0.2, 0.3}, "text-embedding-ada-002")
func (k *Knowledge) SetEmbedding(embedding []float64, model string) error {
	// 这里应该将embedding转换为JSON字符串
	// 简化实现，实际需要使用json.Marshal
	k.EmbeddingModel = model
	return nil
}

// GetEmbedding 获取向量嵌入
// 思路：将JSON字符串转换为向量数组
// 使用例子：embedding := kb.GetEmbedding()
func (k *Knowledge) GetEmbedding() ([]float64, error) {
	// 这里应该将JSON字符串转换为float64切片
	// 简化实现，实际需要使用json.Unmarshal
	return []float64{}, nil
}
