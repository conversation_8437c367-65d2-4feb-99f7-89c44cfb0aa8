# AI客服系统部署指南

## 概述

本指南将帮助您在不同环境中部署AI客服系统，包括开发环境、测试环境和生产环境。

## 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 18.04+), Windows 10+, macOS 10.15+
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **Go版本**: 1.19+

### 推荐配置
- **CPU**: 4核心+
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **网络**: 稳定的互联网连接

## 快速开始

### 1. 下载和安装

#### 从源码编译
```bash
# 克隆仓库
git clone https://github.com/example/aike_go.git
cd aike_go

# 安装依赖
go mod download

# 编译
go build -o bin/aike_server cmd/server/main.go
```

#### 使用预编译二进制文件
```bash
# 下载最新版本
wget https://github.com/example/aike_go/releases/latest/download/aike_server-linux-amd64.tar.gz

# 解压
tar -xzf aike_server-linux-amd64.tar.gz
cd aike_server
```

### 2. 配置环境变量

创建 `.env` 文件：
```bash
# 服务器配置
HOST=0.0.0.0
PORT=8080
GIN_MODE=release

# 数据库配置
DB_TYPE=sqlite
DB_DATABASE=aike_go.db

# 或使用MySQL
# DB_TYPE=mysql
# DB_HOST=localhost
# DB_PORT=3306
# DB_USERNAME=aike_user
# DB_PASSWORD=your_password
# DB_DATABASE=aike_go

# OpenAI配置（可选）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# QQ平台配置（可选）
QQ_ENABLED=false
QQ_BASE_URL=http://localhost:3000
QQ_TOKEN=your_qq_token

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_DIR=logs

# 安全配置
AUTH_ENABLED=false
AUTH_SECRET_KEY=your_secret_key
```

### 3. 启动服务

#### 直接启动
```bash
./bin/aike_server
```

#### 使用启动脚本
```bash
# Linux/macOS
./scripts/start_with_qq.sh

# Windows
scripts\start_with_qq.bat
```

## 生产环境部署

### 1. 使用Systemd (Linux)

创建服务文件 `/etc/systemd/system/aike-server.service`：
```ini
[Unit]
Description=AI Customer Service Server
After=network.target

[Service]
Type=simple
User=aike
Group=aike
WorkingDirectory=/opt/aike_go
ExecStart=/opt/aike_go/bin/aike_server
Restart=always
RestartSec=5
Environment=GIN_MODE=release
EnvironmentFile=/opt/aike_go/.env

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/aike_go/logs /opt/aike_go/data

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable aike-server

# 启动服务
sudo systemctl start aike-server

# 查看状态
sudo systemctl status aike-server

# 查看日志
sudo journalctl -u aike-server -f
```

### 2. 使用Docker

#### Dockerfile
```dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o aike_server cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/aike_server .
COPY --from=builder /app/web ./web
COPY --from=builder /app/configs ./configs

EXPOSE 8080
CMD ["./aike_server"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  aike-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_TYPE=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=aike_user
      - DB_PASSWORD=your_password
      - DB_DATABASE=aike_go
    volumes:
      - ./logs:/root/logs
      - ./data:/root/data
    depends_on:
      - mysql
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=aike_go
      - MYSQL_USER=aike_user
      - MYSQL_PASSWORD=your_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

构建和启动：
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f aike-server

# 停止服务
docker-compose down
```

### 3. 使用Kubernetes

#### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aike-server
  labels:
    app: aike-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aike-server
  template:
    metadata:
      labels:
        app: aike-server
    spec:
      containers:
      - name: aike-server
        image: aike/server:latest
        ports:
        - containerPort: 8080
        env:
        - name: GIN_MODE
          value: "release"
        - name: DB_TYPE
          value: "mysql"
        - name: DB_HOST
          value: "mysql-service"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: aike-server-service
spec:
  selector:
    app: aike-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

部署：
```bash
# 应用配置
kubectl apply -f deployment.yaml

# 查看状态
kubectl get pods
kubectl get services

# 查看日志
kubectl logs -f deployment/aike-server
```

## 反向代理配置

### Nginx配置
```nginx
upstream aike_backend {
    server 127.0.0.1:8080;
    # 如果有多个实例
    # server 127.0.0.1:8081;
    # server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 通用配置
    client_max_body_size 10M;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # API代理
    location /api/ {
        proxy_pass http://aike_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://aike_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static/ {
        proxy_pass http://aike_backend;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        proxy_pass http://aike_backend;
        access_log off;
    }
}
```

## 数据库配置

### MySQL配置
```sql
-- 创建数据库
CREATE DATABASE aike_go CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'aike_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON aike_go.* TO 'aike_user'@'%';
FLUSH PRIVILEGES;
```

### PostgreSQL配置
```sql
-- 创建数据库
CREATE DATABASE aike_go;

-- 创建用户
CREATE USER aike_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE aike_go TO aike_user;
```

## 监控和日志

### 1. 日志配置

#### 日志轮转 (logrotate)
创建 `/etc/logrotate.d/aike-server`：
```
/opt/aike_go/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 aike aike
    postrotate
        systemctl reload aike-server
    endscript
}
```

### 2. 监控配置

#### Prometheus监控
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'aike-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/api/v1/monitor/metrics'
    scrape_interval: 15s
```

#### Grafana仪表板
导入预配置的仪表板模板，监控：
- 请求QPS和响应时间
- 错误率和状态码分布
- 系统资源使用情况
- WebSocket连接数
- 知识库使用统计

## 安全配置

### 1. 防火墙配置
```bash
# 只允许必要的端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. 应用安全
- 启用认证中间件
- 配置限流规则
- 设置IP白名单
- 使用HTTPS
- 定期更新依赖

### 3. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 启用SSL连接
- 定期备份数据

## 备份和恢复

### 1. 数据备份
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u aike_user -p aike_go > $BACKUP_DIR/aike_go_$DATE.sql

# 备份配置文件
cp /opt/aike_go/.env $BACKUP_DIR/env_$DATE

# 备份日志文件
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /opt/aike_go/logs/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 2. 自动备份
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/scripts/backup.sh
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口是否被占用
   - 验证配置文件格式
   - 查看系统日志

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连接

3. **内存使用过高**
   - 检查并发连接数
   - 优化数据库查询
   - 调整垃圾回收参数

4. **响应时间过长**
   - 检查数据库性能
   - 优化API查询
   - 增加缓存

### 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 优化查询语句
   - 使用连接池

2. **应用优化**
   - 启用gzip压缩
   - 使用缓存
   - 优化并发处理

3. **系统优化**
   - 调整文件描述符限制
   - 优化网络参数
   - 使用SSD存储

## 升级指南

### 1. 准备升级
```bash
# 备份当前版本
cp /opt/aike_go/bin/aike_server /opt/aike_go/bin/aike_server.backup

# 备份数据
./backup.sh
```

### 2. 执行升级
```bash
# 停止服务
sudo systemctl stop aike-server

# 替换二进制文件
cp new_aike_server /opt/aike_go/bin/aike_server

# 更新配置（如需要）
# 运行数据库迁移（如需要）

# 启动服务
sudo systemctl start aike-server

# 验证服务状态
sudo systemctl status aike-server
curl http://localhost:8080/health
```

### 3. 回滚（如需要）
```bash
# 停止服务
sudo systemctl stop aike-server

# 恢复旧版本
cp /opt/aike_go/bin/aike_server.backup /opt/aike_go/bin/aike_server

# 恢复数据库（如需要）
# mysql -u aike_user -p aike_go < backup.sql

# 启动服务
sudo systemctl start aike-server
```

## 支持

如需帮助，请参考：
- [API文档](api_documentation.md)
- [开发指南](development_guide.md)
- [故障排除指南](troubleshooting.md)
- GitHub Issues: https://github.com/example/aike_go/issues
