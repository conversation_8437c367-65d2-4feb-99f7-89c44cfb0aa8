package services

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/interfaces"
	"aike_go/internal/models"

	"gorm.io/gorm"
)

// ChatService 聊天服务结构体
// 思路：整合OpenAI API、知识库和数据库，提供完整的聊天功能
// 使用例子：service := NewChatService(cfg); response := service.ProcessMessage(ctx, request)
type ChatService struct {
	openaiService    *OpenAIService
	knowledgeService *KnowledgeService
	db               *gorm.DB
	config           *config.Config
}

// ProcessMessageRequest 处理消息请求
// 思路：统一的消息处理请求格式
type ProcessMessageRequest struct {
	UserID      uint   `json:"user_id"`      // 用户ID
	Platform    string `json:"platform"`     // 平台类型
	PlatformID  string `json:"platform_id"`  // 平台用户ID
	Content     string `json:"content"`      // 消息内容
	MessageType string `json:"message_type"` // 消息类型
	SessionID   string `json:"session_id"`   // 会话ID（可选）
}

// ProcessMessageResponse 处理消息响应
// 思路：包含AI回复和相关统计信息
type ProcessMessageResponse struct {
	Reply       string `json:"reply"`        // AI回复内容
	SessionID   string `json:"session_id"`   // 会话ID
	TokensUsed  int    `json:"tokens_used"`  // 使用的token数
	MessageID   uint   `json:"message_id"`   // 消息ID
	ReplyID     uint   `json:"reply_id"`     // 回复消息ID
	IsFromKB    bool   `json:"is_from_kb"`   // 是否来自知识库
	KnowledgeID uint   `json:"knowledge_id"` // 知识库条目ID
}

// NewChatService 创建聊天服务实例
// 思路：初始化所有依赖的服务
// 使用例子：service := NewChatService(cfg)
func NewChatService(cfg *config.Config) *ChatService {
	return &ChatService{
		openaiService:    NewOpenAIService(cfg),
		knowledgeService: NewKnowledgeService(),
		db:               database.GetDB(),
		config:           cfg,
	}
}

// ProcessMessage 处理用户消息
// 思路：智能消息处理流程：消息过滤 -> 保存消息 -> 条件性回复生成
// 使用例子：response, err := service.ProcessMessage(ctx, request)
func (s *ChatService) ProcessMessage(ctx context.Context, req ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 0. 消息过滤检查 - 判断是否需要回复
	shouldReply, filterReason := s.shouldReplyToMessage(req)
	log.Printf("消息过滤结果: shouldReply=%v, reason=%s", shouldReply, filterReason)
	// 1. 获取或创建用户
	user, err := s.getOrCreateUser(req.Platform, req.PlatformID)
	if err != nil {
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}

	// 2. 会话管理已移至分布式存储插件
	// 注释：原会话管理逻辑已不再需要，因为：
	// - 消息存储使用分布式存储插件
	// - 会话上下文由AI脚本管理
	// session, err := s.getOrCreateSession(user.ID, req.Platform, req.SessionID)
	// if err != nil {
	//     return nil, fmt.Errorf("获取会话失败: %w", err)
	// }

	// 3. 保存用户消息 - 已改为插件化存储
	// 注释：原来的直接数据库存储已被插件化存储系统替代
	// 消息现在通过 Lua 脚本的 store_message() 函数自动存储到插件系统中
	// 参见：scripts/plugins/chat_storage_manager.lua

	// 创建消息对象用于后续处理（不直接保存到数据库）
	// 注释：SessionID 现在由分布式存储插件自动生成
	sessionID := fmt.Sprintf("%s_%s_%s", req.Platform, req.PlatformID, time.Now().Format("20060102"))
	userMessage := &models.Message{
		UserID:    user.ID,
		SessionID: sessionID,
		Content:   req.Content,
		Type:      req.MessageType,
		Direction: "incoming",
		Platform:  req.Platform,
		Status:    "received",
	}

	// 原来的数据库保存逻辑已注释，现在使用插件化存储
	// if err := s.db.Create(userMessage).Error; err != nil {
	//     return nil, fmt.Errorf("保存用户消息失败: %w", err)
	// }

	// 4. 更新用户活跃时间
	user.UpdateLastActive(s.db)
	user.IncrementMessageCount(s.db)
	// 注释：会话统计已移至分布式存储插件
	// session.UpdateLastActive(s.db)
	// session.IncrementMessageCount(s.db)

	// 5. 初始化回复相关变量
	var reply string
	var tokensUsed int
	var isFromKB bool
	var knowledgeID uint
	var replyMessage *models.Message

	// 6. 只有在需要回复时才生成回复
	if !shouldReply {
		log.Printf("根据过滤规则，不回复此消息: %s", filterReason)
		return &ProcessMessageResponse{
			Reply:       "",
			SessionID:   sessionID,
			TokensUsed:  0,
			MessageID:   userMessage.ID,
			ReplyID:     0,
			IsFromKB:    false,
			KnowledgeID: 0,
		}, nil
	}

	// 7. 搜索知识库

	knowledgeResults, err := s.knowledgeService.SearchKnowledge(req.Content, 3)
	if err == nil && len(knowledgeResults) > 0 {
		// 检查是否有高相似度的知识库条目
		bestMatch := knowledgeResults[0]
		if bestMatch.Score > 0.8 { // 相似度阈值
			reply = bestMatch.Knowledge.Content
			isFromKB = true
			knowledgeID = bestMatch.Knowledge.ID

			// 增加知识库使用次数
			bestMatch.Knowledge.IncrementUseCount(s.db)
		}
	}

	// 6. 如果知识库没有合适答案，调用OpenAI
	if reply == "" {
		// 检查是否配置了OpenAI API密钥
		if s.config.OpenAI.APIKey == "" {
			// 没有配置API密钥，使用默认回复
			reply = "抱歉，我暂时无法回答您的问题。请联系人工客服获取帮助。"
		} else {
			aiReply, tokens, err := s.generateAIReply(ctx, sessionID, req.Content)
			if err != nil {
				// OpenAI调用失败，使用默认回复
				reply = "抱歉，我暂时无法回答您的问题。请联系人工客服获取帮助。"
			} else {
				reply = aiReply
				tokensUsed = tokens
			}
		}
	}

	// 8. 保存AI回复 - 已改为插件化存储
	// 注释：原来的直接数据库存储已被插件化存储系统替代
	// AI回复现在通过 Lua 脚本的 store_message() 函数自动存储到插件系统中
	// 参见：scripts/plugins/chat_storage_manager.lua

	// 创建回复消息对象用于后续处理（不直接保存到数据库）
	replyMessage = &models.Message{
		UserID:        user.ID,
		SessionID:     sessionID,
		Content:       reply,
		Type:          "text",
		Direction:     "outgoing",
		Platform:      req.Platform,
		Status:        "sent",
		IsAIGenerated: !isFromKB,
		AIModel:       s.config.OpenAI.Model,
		TokensUsed:    tokensUsed,
		ParentID:      &userMessage.ID,
	}

	// 原来的数据库保存逻辑已注释，现在使用插件化存储
	// if err := s.db.Create(replyMessage).Error; err != nil {
	//     return nil, fmt.Errorf("保存AI回复失败: %w", err)
	// }

	// 8. Token统计已移至分布式存储插件
	// 注释：会话token统计现在由分布式存储插件处理
	// if tokensUsed > 0 {
	//     session.AddTokens(s.db, tokensUsed)
	// }

	response := &ProcessMessageResponse{
		Reply:       reply,
		SessionID:   sessionID,
		TokensUsed:  tokensUsed,
		MessageID:   userMessage.ID,
		ReplyID:     replyMessage.ID,
		IsFromKB:    isFromKB,
		KnowledgeID: knowledgeID,
	}

	// 如果生成了回复，设置回复ID
	if shouldReply {
		response.ReplyID = replyMessage.ID
	}

	return response, nil
}

// ShouldReplyToMessage 判断是否应该回复消息（公开方法，用于测试）
// 思路：基于消息内容、用户类型、时间等因素智能判断
func (s *ChatService) ShouldReplyToMessage(req ProcessMessageRequest) (bool, string) {
	return s.shouldReplyToMessage(req)
}

// shouldReplyToMessage 判断是否应该回复消息（内部方法）
// 思路：基于消息内容、用户类型、时间等因素智能判断
func (s *ChatService) shouldReplyToMessage(req ProcessMessageRequest) (bool, string) {
	content := strings.ToLower(req.Content)

	// 1. 基本内容过滤
	if len(req.Content) < 2 {
		return false, "消息过短"
	}

	// 2. 过滤纯表情和符号
	if matched, _ := regexp.MatchString(`^[😀-🙏\s]*$`, req.Content); matched {
		return false, "纯表情消息"
	}

	if matched, _ := regexp.MatchString(`^[。，！？\s]*$`, req.Content); matched {
		return false, "纯标点符号"
	}

	if matched, _ := regexp.MatchString(`^[0-9\s]*$`, req.Content); matched {
		return false, "纯数字消息"
	}

	// 3. 忽略常见闲聊词汇
	ignoreKeywords := []string{
		"哈哈", "呵呵", "666", "牛逼", "厉害", "赞", "好的", "嗯", "哦", "啊",
		"👍", "👌", "😂", "😄", "😊", "🤣", "😁", "😃", "😆", "😅",
	}

	for _, keyword := range ignoreKeywords {
		if strings.Contains(content, keyword) {
			return false, fmt.Sprintf("包含忽略关键词: %s", keyword)
		}
	}

	// 4. 检查响应关键词
	responseKeywords := []string{
		"客服", "帮助", "help", "问题", "咨询", "售后", "投诉", "bug", "故障",
		"怎么", "如何", "为什么", "什么", "哪里", "谁", "when", "where", "why", "how", "what",
	}

	for _, keyword := range responseKeywords {
		if strings.Contains(content, keyword) {
			return true, fmt.Sprintf("包含响应关键词: %s", keyword)
		}
	}

	// 5. 检查命令前缀
	if len(req.Content) > 0 {
		firstChar := req.Content[0:1]
		commandPrefixes := []string{"/", "!", "@", "？", "?"}
		for _, prefix := range commandPrefixes {
			if firstChar == prefix {
				return true, fmt.Sprintf("命令前缀: %s", prefix)
			}
		}
	}

	// 6. 检查问句（包含问号）
	if strings.Contains(req.Content, "?") || strings.Contains(req.Content, "？") {
		return true, "包含问号，可能是问题"
	}

	// 7. 时间规则 - 工作时间内更宽松
	now := time.Now()
	hour := now.Hour()
	weekday := now.Weekday()
	isWorkHours := weekday >= time.Monday && weekday <= time.Friday && hour >= 9 && hour < 18

	if isWorkHours {
		// 工作时间内，对较长的消息也响应
		if len(req.Content) >= 5 {
			return true, "工作时间内，消息长度足够"
		}
	} else {
		// 非工作时间，检查紧急关键词
		urgentKeywords := []string{"紧急", "urgent", "故障", "bug", "问题", "坏了", "不能用"}
		for _, keyword := range urgentKeywords {
			if strings.Contains(content, keyword) {
				return true, fmt.Sprintf("非工作时间紧急关键词: %s", keyword)
			}
		}
	}

	// 8. 默认不响应
	return false, "未满足响应条件"
}

// HandleIncomingMessage 处理传入的消息（用于NapCat等平台集成）
// 思路：消息存储已插件化，由Lua脚本的存储插件自动处理
func (s *ChatService) HandleIncomingMessage(ctx context.Context, msg *interfaces.IncomingMessage) error {
	// 1. 获取或创建用户（保留用户管理功能）
	user, err := s.getOrCreateUser(msg.Platform, msg.From.PlatformUserID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	// 2. 会话管理已移至分布式存储插件
	// 注释：原会话管理逻辑已不再需要，因为：
	// - 消息存储使用分布式存储插件
	// - 会话上下文由AI脚本管理
	// - 主数据库的sessions表实际未被使用
	// sessionID := fmt.Sprintf("%s_%s", msg.Platform, msg.From.PlatformUserID)
	// session, err := s.getOrCreateSession(user.ID, msg.Platform, sessionID)
	// if err != nil {
	//     return fmt.Errorf("获取会话失败: %w", err)
	// }

	// 3. 消息存储已插件化
	// 注释：原来的直接数据库存储已被插件化存储系统替代
	// 消息现在通过 Lua 脚本的 store_message() 函数自动存储到插件系统中
	// 参见：scripts/plugins/chat_storage_manager.lua

	// 原来的消息保存逻辑已注释，现在使用插件化存储
	// userMessage := &models.Message{
	//     UserID:    user.ID,
	//     SessionID: session.SessionID,
	//     Content:   msg.Content,
	//     Type:      msg.MessageType,
	//     Direction: "incoming",
	//     Platform:  msg.Platform,
	//     Status:    "received",
	// }
	// if err := s.db.Create(userMessage).Error; err != nil {
	//     return fmt.Errorf("保存用户消息失败: %w", err)
	// }

	// 4. 更新用户活跃时间（保留统计功能）
	user.UpdateLastActive(s.db)
	user.IncrementMessageCount(s.db)
	// 注释：会话统计已移至分布式存储插件
	// session.UpdateLastActive(s.db)
	// session.IncrementMessageCount(s.db)

	// log.Printf("消息已接收，将由插件化存储系统处理: 用户=%s, 内容=%s", msg.From.PlatformUserID, msg.Content)
	return nil
}

// getOrCreateUser 获取或创建用户
// 思路：根据平台和平台ID查找用户，不存在则创建
func (s *ChatService) getOrCreateUser(platform, platformID string) (*models.User, error) {
	var user models.User

	// 先尝试查找现有用户
	err := s.db.Where("platform = ? AND platform_id = ?", platform, platformID).First(&user).Error
	if err == nil {
		return &user, nil
	}

	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// 创建新用户
	user = models.User{
		Platform:   platform,
		PlatformID: platformID,
		Status:     "active",
	}

	if err := s.db.Create(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

// getOrCreateSession 获取或创建会话
// 思路：获取活跃会话或创建新会话
func (s *ChatService) getOrCreateSession(userID uint, platform, sessionID string) (*models.Session, error) {
	var session models.Session

	// 如果提供了sessionID，尝试查找
	if sessionID != "" {
		err := s.db.Where("session_id = ? AND status = ?", sessionID, "active").First(&session).Error
		if err == nil {
			return &session, nil
		}
	}

	// 查找用户最近的活跃会话
	err := s.db.Where("user_id = ? AND platform = ? AND status = ?", userID, platform, "active").
		Order("last_active_at DESC").First(&session).Error
	if err == nil {
		// 检查会话是否过期（超过1小时）
		if !session.IsExpired(time.Hour) {
			return &session, nil
		}
		// 会话过期，关闭它
		session.Close(s.db)
	}

	// 创建新会话
	now := time.Now()
	session = models.Session{
		UserID:       userID,
		SessionID:    generateSessionID(platform, userID),
		Platform:     platform,
		Status:       "active",
		StartedAt:    now,
		LastActiveAt: now,
		AIModel:      s.config.OpenAI.Model,
	}

	if err := s.db.Create(&session).Error; err != nil {
		return nil, err
	}

	return &session, nil
}

// generateAIReply 生成AI回复
// 思路：获取会话历史，调用OpenAI API生成回复
func (s *ChatService) generateAIReply(ctx context.Context, sessionID, userMessage string) (string, int, error) {
	// 获取会话历史
	var messages []models.Message
	err := s.db.Where("session_id = ?", sessionID).
		Order("created_at ASC").
		Limit(10). // 限制历史消息数量
		Find(&messages).Error
	if err != nil {
		return "", 0, err
	}

	// 构建OpenAI消息
	chatMessages := s.openaiService.BuildMessagesFromHistory(messages, s.openaiService.GetDefaultSystemPrompt())

	// 调用OpenAI API
	req := ChatCompletionRequest{
		Messages:    chatMessages,
		Temperature: &[]float32{0.7}[0], // 设置温度参数
		MaxTokens:   500,                // 限制回复长度
	}

	response, err := s.openaiService.ChatCompletion(ctx, req)
	if err != nil {
		return "", 0, err
	}

	return response.Content, response.TokensUsed, nil
}

// generateSessionID 生成会话ID
// 思路：生成唯一的会话标识符
func generateSessionID(platform string, userID uint) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d_%d", platform, userID, timestamp)
}
