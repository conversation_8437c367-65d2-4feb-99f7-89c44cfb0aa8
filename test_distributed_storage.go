package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// 测试消息结构
type TestMessage struct {
	Platform    string                 `json:"platform"`
	UserID      string                 `json:"user_id"`
	Content     string                 `json:"content"`
	MessageType string                 `json:"message_type"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

func main() {
	fmt.Println("🧪 开始测试分布式存储系统...")

	// 测试消息列表
	testMessages := []TestMessage{
		// 私聊消息
		{
			Platform:    "test",
			UserID:      "user001",
			Content:     "测试私聊消息1",
			MessageType: "text",
		},
		{
			Platform:    "test",
			UserID:      "user002",
			Content:     "测试私聊消息2",
			MessageType: "text",
		},
		// 群聊消息
		{
			Platform:    "test",
			UserID:      "user003",
			Content:     "测试群聊消息1",
			MessageType: "text",
			Metadata: map[string]interface{}{
				"group_id": "group123456",
			},
		},
		{
			Platform:    "test",
			UserID:      "user004",
			Content:     "测试群聊消息2",
			MessageType: "text",
			Metadata: map[string]interface{}{
				"group_id": "group789012",
			},
		},
		// QQ平台消息
		{
			Platform:    "qq",
			UserID:      "qq_user001",
			Content:     "测试QQ私聊消息",
			MessageType: "text",
		},
		{
			Platform:    "qq",
			UserID:      "qq_user002",
			Content:     "测试QQ群聊消息",
			MessageType: "text",
			Metadata: map[string]interface{}{
				"group_id": "qq_group123",
			},
		},
	}

	// 发送测试消息
	for i, msg := range testMessages {
		fmt.Printf("📤 发送测试消息 %d: %s (平台: %s, 用户: %s)\n", 
			i+1, msg.Content, msg.Platform, msg.UserID)
		
		if msg.Metadata != nil && msg.Metadata["group_id"] != nil {
			fmt.Printf("   群聊ID: %s\n", msg.Metadata["group_id"])
		}

		err := sendMessage(msg)
		if err != nil {
			fmt.Printf("❌ 发送失败: %v\n", err)
		} else {
			fmt.Printf("✅ 发送成功\n")
		}

		// 等待一秒，避免发送过快
		time.Sleep(1 * time.Second)
	}

	fmt.Println("\n🎉 测试完成！请检查分布式存储目录结构：")
	fmt.Println("data/distributed/")
	fmt.Println("├── test/")
	fmt.Println("│   ├── private/")
	fmt.Println("│   │   └── private_001.db (应该包含私聊消息)")
	fmt.Println("│   └── groups/")
	fmt.Println("│       ├── group_group123456.db")
	fmt.Println("│       └── group_group789012.db")
	fmt.Println("└── qq/")
	fmt.Println("    ├── private/")
	fmt.Println("    │   └── private_001.db")
	fmt.Println("    └── groups/")
	fmt.Println("        └── group_qq_group123.db")
}

func sendMessage(msg TestMessage) error {
	// 构建请求体
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("JSON编码失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(
		"http://localhost:8080/api/v1/messages/send",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	return nil
}
