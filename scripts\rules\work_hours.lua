-- 工作时间检查业务规则
-- 检查当前是否在工作时间内

local current_hour = tonumber(os.date("%H"))
local current_day = tonumber(os.date("%w")) -- 0=周日, 1=周一, ..., 6=周六

--log("info", "当前时间: " .. current_hour .. "点, 星期" .. current_day)

-- 工作时间配置: 周一到周五 9:00-18:00
local work_start_hour = 9
local work_end_hour = 18
local work_days = { 1, 2, 3, 4, 5 } -- 周一到周五

-- 检查是否为工作日
local is_workday = false
for _, day in ipairs(work_days) do
    if current_day == day then
        is_workday = true
        break
    end
end

-- 检查是否在工作时间内
local is_workhour = current_hour >= work_start_hour and current_hour < work_end_hour

if not (is_workday and is_workhour) then
    -- 非工作时间，发送自动回复
    if message and message.from and message.from.platform_user_id then
        local user_id = message.from.platform_user_id
        local off_work_msg = [[您好！现在是非工作时间。
工作时间：周一至周五 9:00-18:00
您的消息我们已收到，工作时间会及时回复您。]]

        send_message(user_id, off_work_msg)
        log("info", "已发送非工作时间自动回复")

        -- 设置变量标记已处理
        set_var("handled_by_workhour_check", true)
        return "非工作时间自动回复"
    else
        -- 没有消息对象时，只记录日志
        log("info", "非工作时间，但无消息对象可处理")
        return "非工作时间检查完成"
    end
end

--log("info", "当前在工作时间内")
return "工作时间内，继续处理"
