-- 自动回复处理器
-- 根据关键词自动回复

-- 检查是否是语法验证模式，如果是则跳过实际处理
if __syntax_check_mode then
    log("info", "语法验证模式，跳过自动回复处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id

log("info", "处理用户 " .. user_id .. " 的消息: " .. content)

-- 问候语回复
if content == "你好" or content == "hi" or content == "hello" then
    send_message(user_id, "您好！欢迎使用AI客服系统，我是您的智能助手。")
    return "已发送问候回复"
end

-- 帮助信息
if content == "帮助" or content == "help" then
    local help_text = [[可用命令:
• 你好 - 问候
• 帮助 - 显示此帮助
• 时间 - 获取当前时间
• 状态 - 查看系统状态]]

    send_message(user_id, help_text)
    return "已发送帮助信息"
end

-- 时间查询
if content == "时间" or content == "现在几点" then
    local current_time = os.date("%Y-%m-%d %H:%M:%S")
    send_message(user_id, "当前时间: " .. current_time)
    return "已发送时间信息"
end

-- 状态查询
if content == "状态" or content == "status" then
    send_message(user_id, "系统运行正常 ✅\n平台: " .. message.platform)
    return "已发送状态信息"
end

-- log("info", "未匹配到自动回复规则")
return nil
