package handlers

import (
	"log"
	"net/http"
	"time"

	"aike_go/internal/adapters"
	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
)

// PlatformHandlers 平台处理器
// 思路：将平台相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewPlatformHandlers(adapterManager, chatService)
type PlatformHandlers struct {
	adapterManager *adapters.AdapterManager
	chatService    *services.ChatService
}

// NewPlatformHandlers 创建平台处理器
// 思路：初始化平台处理器，注入适配器管理器和聊天服务依赖
// 使用例子：handlers := NewPlatformHandlers(adapterManager, chatService)
func NewPlatformHandlers(adapterManager *adapters.AdapterManager, chatService *services.ChatService) *PlatformHandlers {
	return &PlatformHandlers{
		adapterManager: adapterManager,
		chatService:    chatService,
	}
}

// ListPlatforms 获取平台列表处理器
// 思路：返回所有注册的平台适配器信息和统计数据
// 使用例子：GET /api/v1/platforms/
func (h *PlatformHandlers) ListPlatforms(c *gin.Context) {
	// 获取适配器统计信息
	stats := h.adapterManager.GetAdapterStats()
	if stats == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取适配器统计信息失败",
		})
		return
	}

	// 构建平台列表
	platforms := make([]map[string]interface{}, 0)
	for platform, adapterStats := range stats.AdapterStats {
		platformInfo := map[string]interface{}{
			"platform":          platform,
			"status":            adapterStats.Status,
			"connection_status": adapterStats.ConnectionStatus,
			"start_time":        adapterStats.StartTime.Unix(),
			"last_message_time": adapterStats.LastMessageTime.Unix(),
			"messages_received": adapterStats.MessagesReceived,
			"messages_sent":     adapterStats.MessagesSent,
			"error_count":       adapterStats.ErrorCount,
			"last_error":        adapterStats.LastError,
		}
		platforms = append(platforms, platformInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取平台列表成功",
		"data": gin.H{
			"platforms":        platforms,
			"total_adapters":   stats.TotalAdapters,
			"running_adapters": stats.RunningAdapters,
			"total_messages":   stats.TotalMessages,
			"total_errors":     stats.TotalErrors,
			"last_update":      stats.LastUpdateTime.Unix(),
		},
	})
}

// GetPlatformStats 获取平台统计信息处理器
// 思路：返回详细的平台统计信息，包括性能指标
// 使用例子：GET /api/v1/platforms/stats
func (h *PlatformHandlers) GetPlatformStats(c *gin.Context) {
	// 获取适配器统计信息
	stats := h.adapterManager.GetAdapterStats()
	if stats == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取适配器统计信息失败",
		})
		return
	}

	// 构建详细统计信息
	detailedStats := map[string]interface{}{
		"overview": gin.H{
			"total_adapters":   stats.TotalAdapters,
			"running_adapters": stats.RunningAdapters,
			"total_messages":   stats.TotalMessages,
			"total_errors":     stats.TotalErrors,
			"last_update":      stats.LastUpdateTime.Unix(),
			"uptime_seconds":   time.Since(stats.LastUpdateTime).Seconds(),
		},
		"adapters": make(map[string]interface{}),
	}

	// 添加每个适配器的详细信息
	for platform, adapterStats := range stats.AdapterStats {
		detailedStats["adapters"].(map[string]interface{})[platform] = gin.H{
			"platform":          platform,
			"status":            adapterStats.Status,
			"connection_status": adapterStats.ConnectionStatus,
			"start_time":        adapterStats.StartTime.Unix(),
			"last_message_time": adapterStats.LastMessageTime.Unix(),
			"messages_received": adapterStats.MessagesReceived,
			"messages_sent":     adapterStats.MessagesSent,
			"error_count":       adapterStats.ErrorCount,
			"last_error":        adapterStats.LastError,
			"uptime_seconds":    time.Since(adapterStats.StartTime).Seconds(),
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取平台统计信息成功",
		"data":    detailedStats,
	})
}

// HandleWebhook 处理平台回调处理器
// 思路：处理来自各个平台的Webhook回调请求，暂时返回成功状态
// 使用例子：POST /api/v1/platforms/qq/webhook
func (h *PlatformHandlers) HandleWebhook(c *gin.Context) {
	platform := c.Param("platform")
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "平台参数不能为空",
		})
		return
	}

	// 读取请求体
	body, err := c.GetRawData()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "读取请求体失败",
			"details": err.Error(),
		})
		return
	}

	// 获取请求头信息
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// TODO: 实现真正的Webhook处理逻辑
	// 暂时记录日志并返回成功状态
	log.Printf("收到 %s 平台的Webhook请求，数据长度: %d", platform, len(body))

	c.JSON(http.StatusOK, gin.H{
		"message": "Webhook处理成功",
		"data": gin.H{
			"platform":     platform,
			"body_length":  len(body),
			"headers":      len(headers),
			"processed_at": time.Now().Unix(),
		},
	})
}

// RestartPlatform 重启平台适配器处理器
// 思路：重启指定的平台适配器，用于故障恢复
// 使用例子：POST /api/v1/platforms/qq/restart
func (h *PlatformHandlers) RestartPlatform(c *gin.Context) {
	platform := c.Param("platform")
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "平台参数不能为空",
		})
		return
	}

	// 重启适配器
	err := h.adapterManager.RestartAdapter(c.Request.Context(), platform)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "重启平台适配器失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "平台适配器重启成功",
		"data": gin.H{
			"platform":     platform,
			"restarted_at": time.Now().Unix(),
		},
	})
}

// GetPlatformConfig 获取平台配置处理器
// 思路：返回指定平台的配置信息
// 使用例子：GET /api/v1/platforms/qq/config
func (h *PlatformHandlers) GetPlatformConfig(c *gin.Context) {
	platform := c.Param("platform")
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "平台参数不能为空",
		})
		return
	}

	// 获取平台配置
	config := h.adapterManager.GetAdapterInfo(platform)
	if config == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "平台不存在或未配置",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取平台配置成功",
		"data": gin.H{
			"platform": platform,
			"config":   config,
		},
	})
}
