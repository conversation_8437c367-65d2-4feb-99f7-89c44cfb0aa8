# NapCat 集成配置指南

## 📋 概述

本文档介绍如何将NapCat QQ机器人框架集成到AI客服系统中，通过WebSocket反向连接实现QQ消息的接收和发送。

## 🔧 系统架构

```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│                 │    反向连接       │                 │
│     NapCat      │ ◄──────────────► │   AI客服系统     │
│   (QQ机器人)     │                  │                 │
└─────────────────┘                  └─────────────────┘
        │                                      │
        │                                      │
        ▼                                      ▼
┌─────────────────┐                  ┌─────────────────┐
│                 │                  │                 │
│   QQ服务器       │                  │   Lua脚本引擎    │
│                 │                  │   OpenAI API    │
└─────────────────┘                  │   知识库        │
                                     └─────────────────┘
```

## 🚀 快速开始

### 1. 启动AI客服系统

```bash
# 启动服务器
go run cmd/server/main.go

# 服务器将在以下端口监听：
# HTTP API: http://localhost:8080
# WebSocket: ws://localhost:8080/api/v1/ws/napcat
```

### 2. 配置NapCat

在NapCat的配置文件中添加反向WebSocket连接：

```json
{
  "http": {
    "enable": true,
    "host": "0.0.0.0",
    "port": 3000,
    "secret": "",
    "enableHeart": true,
    "enablePost": true,
    "enableCors": true
  },
  "ws": {
    "enable": true,
    "host": "0.0.0.0",
    "port": 3001
  },
  "reverseWs": {
    "enable": true,
    "urls": [
      "ws://localhost:8080/api/v1/ws/napcat"
    ]
  },
  "GroupLocalTime": 12,
  "debug": false,
  "reportSelfMessage": false,
  "token": ""
}
```

### 3. 启动NapCat

```bash
# 启动NapCat（具体命令根据你的安装方式）
./napcat
```

## 📡 WebSocket 连接

### 连接端点

```
ws://localhost:8080/api/v1/ws/napcat
```

### 消息格式

#### 接收消息（NapCat → AI客服系统）

```json
{
  "post_type": "message",
  "message_type": "private",
  "sub_type": "friend",
  "message_id": 123456,
  "user_id": 987654321,
  "message": "你好，我需要帮助",
  "raw_message": "你好，我需要帮助",
  "font": 0,
  "sender": {
    "user_id": 987654321,
    "nickname": "用户昵称",
    "sex": "unknown",
    "age": 0
  },
  "time": 1642147200
}
```

#### 发送消息（AI客服系统 → NapCat）

```json
{
  "action": "send_private_msg",
  "params": {
    "user_id": 987654321,
    "message": "您好！我是AI客服，很高兴为您服务。"
  },
  "echo": "send_1642147200123"
}
```

## 🎯 支持的功能

### 消息类型

- ✅ **私聊消息** - 一对一聊天
- ✅ **群聊消息** - 群组聊天
- ✅ **文本消息** - 纯文本内容
- 🔄 **图片消息** - 计划支持
- 🔄 **语音消息** - 计划支持

### API功能

- ✅ **发送私聊消息** - `send_private_msg`
- ✅ **发送群消息** - `send_group_msg`
- ✅ **获取用户信息** - `get_stranger_info`
- ✅ **获取群信息** - `get_group_info`
- ✅ **撤回消息** - `delete_msg`
- ✅ **禁言用户** - `set_group_ban`

## 📝 Lua脚本集成

### 发送QQ消息

在Lua脚本中可以直接调用`send_message`函数发送QQ消息：

```lua
-- 发送私聊消息
send_message("987654321", "您好！这是AI客服的回复。")

-- 发送群消息
send_message("987654321", "群消息内容", "123456789")
```

### 消息处理示例

```lua
-- QQ消息自动回复脚本
if not message or not message.content then
    return nil
end

local content = message.content:lower()
local user_id = message.from.platform_user_id

-- 检查是否为QQ平台
if message.platform == "qq" then
    -- 简单关键词回复
    if content:find("你好") or content:find("hello") then
        send_message(user_id, "您好！我是AI客服，很高兴为您服务！有什么可以帮助您的吗？")
        return "已发送问候回复"
    end
    
    -- 工作时间检查
    local hour = tonumber(os.date("%H"))
    if hour < 9 or hour > 18 then
        send_message(user_id, "您好！现在是非工作时间（9:00-18:00），您的消息我们已收到，工作时间会及时回复。")
        return "已发送非工作时间提醒"
    end
end

return nil
```

## 🔍 调试和监控

### 查看连接状态

```bash
# 查看WebSocket连接
curl http://localhost:8080/api/v1/ws/clients
```

### 日志监控

系统会输出详细的日志信息：

```
2025/07/14 20:00:00 NapCat连接已建立: napcat_1642147200123
2025/07/14 20:00:01 收到NapCat消息: {PostType:message MessageType:private ...}
2025/07/14 20:00:01 [Lua] 发送消息给 987654321: 您好！我是AI客服
2025/07/14 20:00:01 [Lua] QQ消息发送成功
```

## ⚠️ 注意事项

### 安全配置

1. **生产环境** - 建议配置访问令牌和IP白名单
2. **防火墙** - 确保WebSocket端口可访问
3. **SSL/TLS** - 生产环境建议使用wss://协议

### 性能优化

1. **连接池** - 系统支持多个NapCat实例连接
2. **消息队列** - 高并发时建议添加消息队列
3. **限流** - 避免触发QQ的频率限制

### 故障排除

#### 连接失败

```bash
# 检查服务器状态
curl http://localhost:8080/health

# 检查WebSocket端点
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" \
     http://localhost:8080/api/v1/ws/napcat
```

#### 消息发送失败

1. 检查QQ账号是否正常登录
2. 确认目标用户/群组存在
3. 检查消息内容是否符合QQ规范

## 📚 相关文档

- [NapCat官方文档](https://napcat.napneko.com/)
- [OneBot协议标准](https://onebot.dev/)
- [Lua脚本开发指南](./lua_scripting.md)
- [API接口文档](./api_reference.md)

## 🤝 技术支持

如有问题，请查看：

1. 系统日志输出
2. NapCat日志
3. 网络连接状态
4. QQ账号状态

---

*最后更新：2025年7月14日*
