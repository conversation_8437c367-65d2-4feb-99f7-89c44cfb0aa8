#!/bin/bash

# QQ客服系统启动脚本
# 使用方法：./scripts/start_with_qq.sh

set -e

echo "🚀 启动QQ客服系统"
echo "=================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误：未找到Go环境，请先安装Go"
    exit 1
fi

# 设置环境变量
export GIN_MODE=debug
export LOG_LEVEL=info

# QQ平台配置
export QQ_ENABLED=true
export QQ_BASE_URL=http://localhost:3000
export QQ_TOKEN=""

# 数据库配置
export DB_TYPE=sqlite
export DB_DATABASE=aike_go.db

# OpenAI配置（可选）
# export OPENAI_API_KEY=your_api_key_here
# export OPENAI_BASE_URL=https://api.openai.com/v1
# export OPENAI_MODEL=gpt-3.5-turbo

echo "📋 当前配置："
echo "  - QQ平台: ${QQ_ENABLED}"
echo "  - NapCat地址: ${QQ_BASE_URL}"
echo "  - 数据库: ${DB_TYPE} (${DB_DATABASE})"
echo "  - 服务器端口: 8080"
echo ""

# 检查NapCat连接
echo "🔍 检查NapCat连接..."
if curl -s --connect-timeout 3 "${QQ_BASE_URL}/get_status" > /dev/null 2>&1; then
    echo "✅ NapCat连接正常"
else
    echo "⚠️  警告：无法连接到NapCat (${QQ_BASE_URL})"
    echo "   请确保NapCat已启动并配置正确"
    echo ""
fi

# 构建项目
echo "🔨 构建项目..."
go build -o bin/aike_server cmd/server/main.go
if [ $? -eq 0 ]; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    exit 1
fi

# 启动服务器
echo ""
echo "🎯 启动客服服务器..."
echo "   - HTTP服务: http://localhost:8080"
echo "   - WebSocket测试: http://localhost:8080/static/websocket_test.html"
echo "   - API文档: http://localhost:8080/health"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务器
./bin/aike_server

echo ""
echo "👋 服务器已停止"
