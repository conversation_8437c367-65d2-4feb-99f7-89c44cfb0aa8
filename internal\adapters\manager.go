package adapters

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/interfaces"
)

// AdapterManager 适配器管理器
// 思路：管理所有平台适配器的生命周期和消息路由
// 使用例子：manager := NewAdapterManager(cfg, chatService); manager.Start(ctx)
type AdapterManager struct {
	config   *config.Config
	adapters map[string]PlatformAdapter
	handler  interfaces.MessageHandler
	mutex    sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	stats    *ManagerStats
}

// ManagerStats 管理器统计信息
// 思路：整体的适配器管理统计
type ManagerStats struct {
	StartTime       time.Time                `json:"start_time"`
	TotalAdapters   int                      `json:"total_adapters"`
	RunningAdapters int                      `json:"running_adapters"`
	TotalMessages   int64                    `json:"total_messages"`
	TotalErrors     int64                    `json:"total_errors"`
	AdapterStats    map[string]*AdapterStats `json:"adapter_stats"`
	LastUpdateTime  time.Time                `json:"last_update_time"`
}

// 移除DefaultMessageHandler，现在通过依赖注入处理消息

// NewAdapterManager 创建适配器管理器
// 思路：初始化管理器和消息处理器
// 使用例子：manager := NewAdapterManager(cfg, handler)
func NewAdapterManager(cfg *config.Config, handler interfaces.MessageHandler) *AdapterManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &AdapterManager{
		config:   cfg,
		adapters: make(map[string]PlatformAdapter),
		handler:  handler,
		ctx:      ctx,
		cancel:   cancel,
		stats: &ManagerStats{
			StartTime:    time.Now(),
			AdapterStats: make(map[string]*AdapterStats),
		},
	}

	return manager
}

// GetHandler 获取消息处理器
// 思路：返回消息处理器实例，用于创建适配器
// 使用例子：handler := manager.GetHandler()
func (m *AdapterManager) GetHandler() MessageHandler {
	return m.handler
}

// registerAdapters 注册所有适配器
// 思路：在这里注册所有可用的平台适配器
func (m *AdapterManager) registerAdapters() {
	// 注册QQ适配器
	qqAdapter := NewQQAdapter()
	qqAdapter.SetMessageHandler(m.handler)
	if err := m.RegisterAdapter(qqAdapter); err != nil {
		log.Printf("注册QQ适配器失败: %v", err)
	}

	// 注册Telegram适配器
	telegramAdapter := NewTelegramAdapter()
	telegramAdapter.SetMessageHandler(m.handler)
	if err := m.RegisterAdapter(telegramAdapter); err != nil {
		log.Printf("注册Telegram适配器失败: %v", err)
	}

	log.Printf("已注册 %d 个平台适配器", len(m.adapters))
}

// RegisterAdapter 注册平台适配器
// 思路：将适配器添加到管理器中
// 使用例子：manager.RegisterAdapter(qqAdapter)
func (m *AdapterManager) RegisterAdapter(adapter PlatformAdapter) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	platform := adapter.GetPlatformName()
	if _, exists := m.adapters[platform]; exists {
		return fmt.Errorf("平台适配器已存在: %s", platform)
	}

	m.adapters[platform] = adapter
	m.stats.TotalAdapters++

	log.Printf("注册平台适配器: %s", platform)
	return nil
}

// Start 启动所有适配器
// 思路：初始化并启动所有已注册的适配器，避免在锁内阻塞
// 使用例子：err := manager.Start(ctx)
func (m *AdapterManager) Start(ctx context.Context) error {
	log.Println("启动适配器管理器...")

	// 在锁内注册适配器，暂时禁用统计更新协程用于调试

	// m.registerAdapters()

	m.mutex.Lock()
	// 暂时注释掉统计更新循环，用于调试死锁问题
	// go m.updateStatsLoop()

	// 获取适配器列表的副本，避免在锁内启动适配器
	adapters := make(map[string]PlatformAdapter, len(m.adapters))
	for platform, adapter := range m.adapters {
		adapters[platform] = adapter
	}
	m.mutex.Unlock()

	// 在锁外启动所有适配器，避免阻塞其他操作
	for platform, adapter := range adapters {
		if err := m.startAdapter(platform, adapter); err != nil {
			log.Printf("启动适配器失败 %s: %v", platform, err)
			continue
		}
	}

	// 更新统计信息
	m.mutex.Lock()
	log.Printf("适配器管理器启动完成，运行中的适配器: %d/%d", m.stats.RunningAdapters, m.stats.TotalAdapters)
	m.mutex.Unlock()

	return nil
}

// startAdapter 启动单个适配器
// 思路：初始化并启动指定的适配器
func (m *AdapterManager) startAdapter(platform string, adapter PlatformAdapter) error {
	// 获取平台配置
	platformConfig, err := m.getPlatformConfig(platform)
	if err != nil {
		return fmt.Errorf("获取平台配置失败: %w", err)
	}

	if !platformConfig.IsEnabled() {
		log.Printf("平台适配器未启用: %s", platform)
		return nil
	}

	// 初始化适配器
	if err := adapter.Initialize(platformConfig); err != nil {
		return fmt.Errorf("初始化适配器失败: %w", err)
	}

	// 启动适配器
	go func() {
		if err := adapter.Start(m.ctx); err != nil {
			log.Printf("适配器运行错误 %s: %v", platform, err)
			m.stats.TotalErrors++
		}
	}()

	m.stats.RunningAdapters++
	log.Printf("适配器启动成功: %s", platform)
	return nil
}

// Stop 停止所有适配器
// 思路：优雅关闭所有适配器
// 使用例子：err := manager.Stop(ctx)
func (m *AdapterManager) Stop(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	log.Println("停止适配器管理器...")

	// 取消上下文
	m.cancel()

	// 停止所有适配器
	var wg sync.WaitGroup
	for platform, adapter := range m.adapters {
		wg.Add(1)
		go func(p string, a PlatformAdapter) {
			defer wg.Done()
			if err := a.Stop(ctx); err != nil {
				log.Printf("停止适配器失败 %s: %v", p, err)
			} else {
				log.Printf("适配器已停止: %s", p)
			}
		}(platform, adapter)
	}

	// 等待所有适配器停止
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Println("所有适配器已停止")
	case <-time.After(30 * time.Second):
		log.Println("适配器停止超时")
	}

	return nil
}

// SendMessage 通过适配器发送消息
// 思路：根据平台路由消息到对应的适配器
// 使用例子：err := manager.SendMessage(ctx, message)
func (m *AdapterManager) SendMessage(ctx context.Context, message *OutgoingMessage) error {
	m.mutex.RLock()
	adapter, exists := m.adapters[message.Platform]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("平台适配器不存在: %s", message.Platform)
	}

	if !adapter.IsHealthy() {
		return fmt.Errorf("平台适配器不健康: %s", message.Platform)
	}

	err := adapter.SendMessage(ctx, message)
	if err != nil {
		m.stats.TotalErrors++
		return fmt.Errorf("发送消息失败: %w", err)
	}

	m.stats.TotalMessages++
	return nil
}

// GetAdapterStats 获取适配器统计信息
// 思路：临时完全绕过锁机制，用于调试死锁问题
// 使用例子：stats := manager.GetAdapterStats()
func (m *AdapterManager) GetAdapterStats() *ManagerStats {
	log.Printf("GetAdapterStats 开始执行")

	m.mutex.RLock()
	defer m.mutex.RUnlock()
	// 临时版本：完全绕过锁机制，直接访问适配器
	log.Printf("绕过锁机制，直接访问适配器")

	// 直接访问适配器映射，不使用锁
	adapters := m.adapters
	if adapters == nil {
		adapters = make(map[string]PlatformAdapter)
	}

	log.Printf("获取到 %d 个适配器", len(adapters))

	// 第二步：在锁外收集适配器统计信息，使用超时机制防止阻塞
	adapterStats := make(map[string]*AdapterStats, len(adapters))
	for platform, adapter := range adapters {
		log.Printf("开始获取适配器 %s 的统计信息", platform)

		// 使用超时机制防止某个适配器阻塞整个统计收集
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		stats := m.getAdapterStatsWithTimeout(ctx, adapter)
		cancel()

		if stats != nil {
			log.Printf("成功获取适配器 %s 的统计信息", platform)
			adapterStats[platform] = stats
		} else {
			log.Printf("获取适配器 %s 的统计信息失败或超时", platform)
			// 使用默认统计信息
			adapterStats[platform] = &AdapterStats{
				Platform:         platform,
				Status:           "unknown",
				ConnectionStatus: "timeout",
				StartTime:        time.Time{},
				LastMessageTime:  time.Time{},
				MessagesReceived: 0,
				MessagesSent:     0,
				ErrorCount:       1,
				LastError:        "获取统计信息超时",
			}
		}
	}

	log.Printf("完成所有适配器统计信息收集")

	// 第三步：更新管理器统计信息

	m.stats.AdapterStats = adapterStats
	m.stats.TotalAdapters = len(adapters)
	m.stats.LastUpdateTime = time.Now()

	// 计算运行中的适配器数量
	runningCount := 0
	for _, stats := range adapterStats {
		if stats.Status == "running" || stats.ConnectionStatus == "connected" {
			runningCount++
		}
	}
	m.stats.RunningAdapters = runningCount

	// 返回统计信息的副本，避免外部修改
	statsCopy := *m.stats
	statsCopy.AdapterStats = make(map[string]*AdapterStats, len(m.stats.AdapterStats))
	for platform, stats := range m.stats.AdapterStats {
		// 创建统计信息的副本
		statsCopy.AdapterStats[platform] = &AdapterStats{
			Platform:         stats.Platform,
			Status:           stats.Status,
			ConnectionStatus: stats.ConnectionStatus,
			StartTime:        stats.StartTime,
			LastMessageTime:  stats.LastMessageTime,
			MessagesReceived: stats.MessagesReceived,
			MessagesSent:     stats.MessagesSent,
			ErrorCount:       stats.ErrorCount,
			LastError:        stats.LastError,
		}
	}

	log.Printf("GetAdapterStats 执行完成，返回真实统计信息")
	return &statsCopy
}

// getAdapterStatsWithTimeout 带超时的获取适配器统计信息
// 思路：防止某个适配器的GetStats方法阻塞整个统计收集过程
func (m *AdapterManager) getAdapterStatsWithTimeout(ctx context.Context, adapter PlatformAdapter) *AdapterStats {
	type result struct {
		stats *AdapterStats
		err   error
	}

	resultChan := make(chan result, 1)

	// 在goroutine中调用GetStats，避免阻塞
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("获取适配器 %s 统计信息时发生panic: %v", adapter.GetPlatformName(), r)
				resultChan <- result{stats: nil, err: fmt.Errorf("panic: %v", r)}
			}
		}()

		// 获取适配器统计信息
		stats := adapter.GetStats()
		if stats != nil {
			// 确保平台名称正确设置
			if stats.Platform == "" {
				stats.Platform = adapter.GetPlatformName()
			}
			log.Printf("适配器 %s 统计信息: 状态=%s, 连接=%s, 接收=%d, 发送=%d, 错误=%d",
				stats.Platform, stats.Status, stats.ConnectionStatus,
				stats.MessagesReceived, stats.MessagesSent, stats.ErrorCount)
		}

		resultChan <- result{stats: stats, err: nil}
	}()

	// 等待结果或超时
	select {
	case res := <-resultChan:
		if res.err != nil {
			log.Printf("获取适配器 %s 统计信息失败: %v", adapter.GetPlatformName(), res.err)
			return nil
		}
		return res.stats
	case <-ctx.Done():
		log.Printf("获取适配器 %s 统计信息超时", adapter.GetPlatformName())
		return nil
	}
}

// GetAdapter 获取指定平台的适配器
// 思路：返回指定平台的适配器实例
// 使用例子：adapter := manager.GetAdapter("qq")
func (m *AdapterManager) GetAdapter(platform string) (PlatformAdapter, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	adapter, exists := m.adapters[platform]
	return adapter, exists
}

// GetHealthSummary 获取适配器健康状态摘要
// 思路：快速获取所有适配器的健康状态，用于监控和告警
// 使用例子：summary := manager.GetHealthSummary()
func (m *AdapterManager) GetHealthSummary() map[string]bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	summary := make(map[string]bool, len(m.adapters))
	for platform, adapter := range m.adapters {
		summary[platform] = adapter.IsHealthy()
	}

	return summary
}

// GetUnhealthyAdapters 获取不健康的适配器列表
// 思路：快速识别有问题的适配器，便于故障排查
// 使用例子：unhealthy := manager.GetUnhealthyAdapters()
func (m *AdapterManager) GetUnhealthyAdapters() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var unhealthy []string
	for platform, adapter := range m.adapters {
		if !adapter.IsHealthy() {
			unhealthy = append(unhealthy, platform)
		}
	}

	return unhealthy
}

// RestartAdapter 重启指定的适配器
// 思路：当适配器出现问题时，提供重启功能
// 使用例子：err := manager.RestartAdapter(ctx, "qq")
func (m *AdapterManager) RestartAdapter(ctx context.Context, platform string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	adapter, exists := m.adapters[platform]
	if !exists {
		return fmt.Errorf("平台适配器不存在: %s", platform)
	}

	log.Printf("重启适配器: %s", platform)

	// 停止适配器
	if err := adapter.Stop(ctx); err != nil {
		log.Printf("停止适配器失败 %s: %v", platform, err)
	}

	// 重新启动适配器
	if err := m.startAdapter(platform, adapter); err != nil {
		return fmt.Errorf("重启适配器失败: %w", err)
	}

	log.Printf("适配器重启成功: %s", platform)
	return nil
}

// updateStatsLoop 更新统计信息循环
// 思路：定期更新管理器统计信息，包含错误处理和健康检查
func (m *AdapterManager) updateStatsLoop() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// 健康检查定时器，更频繁地检查适配器健康状态
	healthTicker := time.NewTicker(10 * time.Second)
	defer healthTicker.Stop()

	for {
		select {
		case <-ticker.C:
			// 更新完整统计信息
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("更新统计信息时发生panic: %v", r)
					}
				}()
				m.updateStats()
			}()

		case <-healthTicker.C:
			// 快速健康检查
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("健康检查时发生panic: %v", r)
					}
				}()
				m.performHealthCheck()
			}()

		case <-m.ctx.Done():
			log.Println("统计信息更新循环已停止")
			return
		}
	}
}

// performHealthCheck 执行健康检查
// 思路：快速检查所有适配器的健康状态，记录异常情况
func (m *AdapterManager) performHealthCheck() {
	unhealthyAdapters := m.GetUnhealthyAdapters()

	if len(unhealthyAdapters) > 0 {
		log.Printf("发现不健康的适配器: %v", unhealthyAdapters)

		// 获取不健康适配器的详细信息，避免在循环中重复获取锁
		m.mutex.RLock()
		unhealthyAdapterMap := make(map[string]PlatformAdapter)
		for _, platform := range unhealthyAdapters {
			if adapter, exists := m.adapters[platform]; exists {
				unhealthyAdapterMap[platform] = adapter
			}
		}
		m.mutex.RUnlock()

		// 在锁外获取错误信息
		for platform, adapter := range unhealthyAdapterMap {
			stats := adapter.GetStats()
			if stats != nil && stats.LastError != "" {
				log.Printf("适配器 %s 最后错误: %s", platform, stats.LastError)
			}
		}
	}
}

// updateStats 更新统计信息
// 思路：统计运行中的适配器数量，使用超时机制避免阻塞
func (m *AdapterManager) updateStats() {
	// 先获取适配器列表的副本，避免在锁内调用外部方法
	m.mutex.RLock()
	adapters := make([]PlatformAdapter, 0, len(m.adapters))
	for _, adapter := range m.adapters {
		adapters = append(adapters, adapter)
	}
	m.mutex.RUnlock()

	// 在锁外统计健康的适配器数量，使用超时机制
	runningCount := 0
	for _, adapter := range adapters {
		// 使用超时机制检查适配器健康状态，避免阻塞
		healthy := m.checkAdapterHealthWithTimeout(adapter, 2*time.Second)
		if healthy {
			runningCount++
		}
	}

	// 更新统计信息
	m.mutex.Lock()
	m.stats.RunningAdapters = runningCount
	m.stats.LastUpdateTime = time.Now()
	m.mutex.Unlock()
}

// checkAdapterHealthWithTimeout 带超时的适配器健康检查
// 思路：防止某个适配器的IsHealthy方法阻塞整个统计更新过程
func (m *AdapterManager) checkAdapterHealthWithTimeout(adapter PlatformAdapter, timeout time.Duration) bool {
	type result struct {
		healthy bool
	}

	resultChan := make(chan result, 1)

	// 在goroutine中调用IsHealthy，避免阻塞
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("检查适配器 %s 健康状态时发生panic: %v", adapter.GetPlatformName(), r)
				resultChan <- result{healthy: false}
			}
		}()

		healthy := adapter.IsHealthy()
		resultChan <- result{healthy: healthy}
	}()

	// 等待结果或超时
	select {
	case res := <-resultChan:
		return res.healthy
	case <-time.After(timeout):
		log.Printf("检查适配器 %s 健康状态超时", adapter.GetPlatformName())
		return false
	}
}

// GetAdapterInfo 获取适配器详细信息
// 思路：为API接口提供适配器的详细信息，包括配置和状态
// 使用例子：info := manager.GetAdapterInfo("qq")
func (m *AdapterManager) GetAdapterInfo(platform string) map[string]interface{} {
	m.mutex.RLock()
	adapter, exists := m.adapters[platform]
	m.mutex.RUnlock()

	if !exists {
		return nil
	}

	// 获取平台配置
	config, err := m.getPlatformConfig(platform)
	if err != nil {
		log.Printf("获取平台配置失败: %v", err)
	}

	// 获取适配器统计信息
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	stats := m.getAdapterStatsWithTimeout(ctx, adapter)
	cancel()

	info := map[string]interface{}{
		"platform":   platform,
		"is_healthy": adapter.IsHealthy(),
		"stats":      stats,
	}

	// 添加配置信息（不包含敏感信息）
	if config != nil {
		info["enabled"] = config.IsEnabled()
		// 可以添加其他非敏感的配置信息
	}

	return info
}

// GetAllAdaptersInfo 获取所有适配器的详细信息
// 思路：为API接口提供所有适配器的信息列表
// 使用例子：infos := manager.GetAllAdaptersInfo()
func (m *AdapterManager) GetAllAdaptersInfo() []map[string]interface{} {
	m.mutex.RLock()
	platforms := make([]string, 0, len(m.adapters))
	for platform := range m.adapters {
		platforms = append(platforms, platform)
	}
	m.mutex.RUnlock()

	var infos []map[string]interface{}
	for _, platform := range platforms {
		if info := m.GetAdapterInfo(platform); info != nil {
			infos = append(infos, info)
		}
	}

	return infos
}

// getPlatformConfig 获取平台配置
// 思路：根据平台名称返回对应的配置
func (m *AdapterManager) getPlatformConfig(platform string) (PlatformConfig, error) {
	switch platform {
	case "qq":
		return &QQConfig{Config: m.config.Platforms.QQ}, nil
	case "wechat":
		return &WeChatConfig{Config: m.config.Platforms.WeChat}, nil
	case "telegram":
		return &TelegramConfig{Config: m.config.Platforms.Telegram}, nil
	case "qianniu":
		return &QianniuConfig{Config: m.config.Platforms.Qianniu}, nil
	default:
		return nil, fmt.Errorf("不支持的平台: %s", platform)
	}
}

// 移除DefaultMessageHandler的方法，现在通过依赖注入的MessageHandler处理消息

// 平台配置实现

// QQConfig QQ平台配置
type QQConfig struct {
	Config config.QQConfig
}

func (c *QQConfig) GetPlatform() string { return "qq" }
func (c *QQConfig) IsEnabled() bool     { return c.Config.Enabled }

func (c *QQConfig) GetSetting(key string) (string, bool) {
	switch key {
	case "base_url":
		return c.Config.BaseURL, true
	case "token":
		return c.Config.Token, true
	case "enabled":
		if c.Config.Enabled {
			return "true", true
		}
		return "false", true
	default:
		return "", false
	}
}

func (c *QQConfig) GetAllSettings() map[string]string {
	return map[string]string{
		"base_url": c.Config.BaseURL,
		"token":    c.Config.Token,
		"enabled":  fmt.Sprintf("%t", c.Config.Enabled),
	}
}

func (c *QQConfig) Validate() error {
	if c.Config.Enabled && (c.Config.BaseURL == "" || c.Config.Token == "") {
		return fmt.Errorf("QQ平台配置不完整")
	}
	return nil
}

// WeChatConfig 微信平台配置
type WeChatConfig struct {
	Config config.WeChatConfig
}

func (c *WeChatConfig) GetPlatform() string { return "wechat" }
func (c *WeChatConfig) IsEnabled() bool     { return c.Config.Enabled }

func (c *WeChatConfig) GetSetting(key string) (string, bool) {
	switch key {
	case "app_id":
		return c.Config.AppID, true
	case "secret":
		return c.Config.Secret, true
	case "enabled":
		if c.Config.Enabled {
			return "true", true
		}
		return "false", true
	default:
		return "", false
	}
}

func (c *WeChatConfig) GetAllSettings() map[string]string {
	return map[string]string{
		"app_id":  c.Config.AppID,
		"secret":  c.Config.Secret,
		"enabled": fmt.Sprintf("%t", c.Config.Enabled),
	}
}

func (c *WeChatConfig) Validate() error {
	if c.Config.Enabled && (c.Config.AppID == "" || c.Config.Secret == "") {
		return fmt.Errorf("微信平台配置不完整")
	}
	return nil
}

// TelegramConfig Telegram平台配置
type TelegramConfig struct {
	Config config.TelegramConfig
}

func (c *TelegramConfig) GetPlatform() string { return "telegram" }
func (c *TelegramConfig) IsEnabled() bool     { return c.Config.Enabled }

func (c *TelegramConfig) GetSetting(key string) (string, bool) {
	switch key {
	case "token":
		return c.Config.Token, true
	case "enabled":
		if c.Config.Enabled {
			return "true", true
		}
		return "false", true
	default:
		return "", false
	}
}

func (c *TelegramConfig) GetAllSettings() map[string]string {
	return map[string]string{
		"token":   c.Config.Token,
		"enabled": fmt.Sprintf("%t", c.Config.Enabled),
	}
}

func (c *TelegramConfig) Validate() error {
	if c.Config.Enabled && c.Config.Token == "" {
		return fmt.Errorf("Telegram平台配置不完整")
	}
	return nil
}

// QianniuConfig 千牛平台配置
type QianniuConfig struct {
	Config config.QianniuConfig
}

func (c *QianniuConfig) GetPlatform() string { return "qianniu" }
func (c *QianniuConfig) IsEnabled() bool     { return c.Config.Enabled }

func (c *QianniuConfig) GetSetting(key string) (string, bool) {
	switch key {
	case "app_key":
		return c.Config.AppKey, true
	case "secret":
		return c.Config.Secret, true
	case "enabled":
		if c.Config.Enabled {
			return "true", true
		}
		return "false", true
	default:
		return "", false
	}
}

func (c *QianniuConfig) GetAllSettings() map[string]string {
	return map[string]string{
		"app_key": c.Config.AppKey,
		"secret":  c.Config.Secret,
		"enabled": fmt.Sprintf("%t", c.Config.Enabled),
	}
}

func (c *QianniuConfig) Validate() error {
	if c.Config.Enabled && (c.Config.AppKey == "" || c.Config.Secret == "") {
		return fmt.Errorf("千牛平台配置不完整")
	}
	return nil
}
