package server

import (
	"context"
	"fmt"
	"time"
)

// ServerConfig 服务器配置
// 思路：集中管理服务器的所有配置项，便于维护和扩展
// 使用例子：config := &ServerConfig{Port: 8080, Host: "0.0.0.0"}
type ServerConfig struct {
	// 基本配置
	Host string `json:"host" yaml:"host"` // 服务器监听地址
	Port int    `json:"port" yaml:"port"` // 服务器监听端口
	Mode string `json:"mode" yaml:"mode"` // 运行模式 (debug, release, test)

	// 超时配置
	ReadTimeout     time.Duration `json:"read_timeout" yaml:"read_timeout"`         // 读取超时
	WriteTimeout    time.Duration `json:"write_timeout" yaml:"write_timeout"`       // 写入超时
	IdleTimeout     time.Duration `json:"idle_timeout" yaml:"idle_timeout"`         // 空闲超时
	ShutdownTimeout time.Duration `json:"shutdown_timeout" yaml:"shutdown_timeout"` // 关闭超时

	// TLS配置
	TLS struct {
		Enabled  bool   `json:"enabled" yaml:"enabled"`     // 是否启用TLS
		CertFile string `json:"cert_file" yaml:"cert_file"` // 证书文件路径
		KeyFile  string `json:"key_file" yaml:"key_file"`   // 私钥文件路径
	} `json:"tls" yaml:"tls"`

	// CORS配置
	CORS struct {
		Enabled          bool     `json:"enabled" yaml:"enabled"`                     // 是否启用CORS
		AllowOrigins     []string `json:"allow_origins" yaml:"allow_origins"`         // 允许的源
		AllowMethods     []string `json:"allow_methods" yaml:"allow_methods"`         // 允许的方法
		AllowHeaders     []string `json:"allow_headers" yaml:"allow_headers"`         // 允许的头部
		ExposeHeaders    []string `json:"expose_headers" yaml:"expose_headers"`       // 暴露的头部
		AllowCredentials bool     `json:"allow_credentials" yaml:"allow_credentials"` // 是否允许凭证
		MaxAge           int      `json:"max_age" yaml:"max_age"`                     // 预检请求缓存时间
	} `json:"cors" yaml:"cors"`

	// 限流配置
	RateLimit struct {
		Enabled bool          `json:"enabled" yaml:"enabled"` // 是否启用限流
		Rate    float64       `json:"rate" yaml:"rate"`       // 每秒请求数
		Burst   int           `json:"burst" yaml:"burst"`     // 突发请求数
		Window  time.Duration `json:"window" yaml:"window"`   // 时间窗口
	} `json:"rate_limit" yaml:"rate_limit"`

	// 日志配置
	Logging struct {
		Level      string `json:"level" yaml:"level"`             // 日志级别
		Format     string `json:"format" yaml:"format"`           // 日志格式 (json, text)
		Output     string `json:"output" yaml:"output"`           // 输出目标 (stdout, file)
		Filename   string `json:"filename" yaml:"filename"`       // 日志文件名
		MaxSize    int    `json:"max_size" yaml:"max_size"`       // 最大文件大小(MB)
		MaxBackups int    `json:"max_backups" yaml:"max_backups"` // 最大备份数
		MaxAge     int    `json:"max_age" yaml:"max_age"`         // 最大保存天数
		Compress   bool   `json:"compress" yaml:"compress"`       // 是否压缩
	} `json:"logging" yaml:"logging"`

	// 监控配置
	Monitoring struct {
		Enabled        bool          `json:"enabled" yaml:"enabled"`                 // 是否启用监控
		MetricsPath    string        `json:"metrics_path" yaml:"metrics_path"`       // 指标路径
		HealthPath     string        `json:"health_path" yaml:"health_path"`         // 健康检查路径
		UpdateInterval time.Duration `json:"update_interval" yaml:"update_interval"` // 更新间隔
	} `json:"monitoring" yaml:"monitoring"`

	// WebSocket配置
	WebSocket struct {
		Enabled          bool          `json:"enabled" yaml:"enabled"`                     // 是否启用WebSocket
		Path             string        `json:"path" yaml:"path"`                           // WebSocket路径
		ReadBufferSize   int           `json:"read_buffer_size" yaml:"read_buffer_size"`   // 读缓冲区大小
		WriteBufferSize  int           `json:"write_buffer_size" yaml:"write_buffer_size"` // 写缓冲区大小
		HandshakeTimeout time.Duration `json:"handshake_timeout" yaml:"handshake_timeout"` // 握手超时
		PingInterval     time.Duration `json:"ping_interval" yaml:"ping_interval"`         // Ping间隔
		PongTimeout      time.Duration `json:"pong_timeout" yaml:"pong_timeout"`           // Pong超时
		MaxMessageSize   int64         `json:"max_message_size" yaml:"max_message_size"`   // 最大消息大小
	} `json:"websocket" yaml:"websocket"`

	// 静态文件配置
	Static struct {
		Enabled bool   `json:"enabled" yaml:"enabled"` // 是否启用静态文件服务
		Path    string `json:"path" yaml:"path"`       // 静态文件路径
		Root    string `json:"root" yaml:"root"`       // 静态文件根目录
	} `json:"static" yaml:"static"`
}

// DefaultServerConfig 返回默认的服务器配置
// 思路：提供合理的默认配置值，减少配置复杂度
// 使用例子：config := DefaultServerConfig()
func DefaultServerConfig() *ServerConfig {
	config := &ServerConfig{
		Host:            "0.0.0.0",
		Port:            8080,
		Mode:            "debug",
		ReadTimeout:     30 * time.Second,
		WriteTimeout:    30 * time.Second,
		IdleTimeout:     60 * time.Second,
		ShutdownTimeout: 10 * time.Second,
	}

	// CORS默认配置
	config.CORS.Enabled = true
	config.CORS.AllowOrigins = []string{"*"}
	config.CORS.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.CORS.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"}
	config.CORS.ExposeHeaders = []string{"Content-Length"}
	config.CORS.AllowCredentials = true
	config.CORS.MaxAge = 86400

	// 限流默认配置
	config.RateLimit.Enabled = false
	config.RateLimit.Rate = 100
	config.RateLimit.Burst = 200
	config.RateLimit.Window = time.Minute

	// 日志默认配置
	config.Logging.Level = "info"
	config.Logging.Format = "text"
	config.Logging.Output = "stdout"
	config.Logging.MaxSize = 100
	config.Logging.MaxBackups = 3
	config.Logging.MaxAge = 7
	config.Logging.Compress = true

	// 监控默认配置
	config.Monitoring.Enabled = true
	config.Monitoring.MetricsPath = "/metrics"
	config.Monitoring.HealthPath = "/health"
	config.Monitoring.UpdateInterval = 30 * time.Second

	// WebSocket默认配置
	config.WebSocket.Enabled = true
	config.WebSocket.Path = "/ws"
	config.WebSocket.ReadBufferSize = 1024
	config.WebSocket.WriteBufferSize = 1024
	config.WebSocket.HandshakeTimeout = 10 * time.Second
	config.WebSocket.PingInterval = 30 * time.Second
	config.WebSocket.PongTimeout = 10 * time.Second
	config.WebSocket.MaxMessageSize = 1024 * 1024 // 1MB

	// 静态文件默认配置
	config.Static.Enabled = true
	config.Static.Path = "/static"
	config.Static.Root = "./web/static"

	return config
}

// Validate 验证配置的有效性
// 思路：在启动前验证配置，避免运行时错误
// 使用例子：if err := config.Validate(); err != nil { ... }
func (c *ServerConfig) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return ErrInvalidPort
	}

	if c.Host == "" {
		return ErrInvalidHost
	}

	if c.ReadTimeout <= 0 {
		return ErrInvalidTimeout
	}

	if c.WriteTimeout <= 0 {
		return ErrInvalidTimeout
	}

	if c.TLS.Enabled {
		if c.TLS.CertFile == "" || c.TLS.KeyFile == "" {
			return ErrInvalidTLSConfig
		}
	}

	return nil
}

// GetAddress 获取服务器监听地址
// 思路：格式化主机和端口为标准地址格式
// 使用例子：addr := config.GetAddress() // "0.0.0.0:8080"
func (c *ServerConfig) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetContext 获取带有配置信息的上下文
// 思路：将配置信息注入到上下文中，便于其他组件使用
// 使用例子：ctx := config.GetContext(context.Background())
func (c *ServerConfig) GetContext(parent context.Context) context.Context {
	return context.WithValue(parent, "server_config", c)
}

// 配置相关错误定义
var (
	ErrInvalidPort      = fmt.Errorf("invalid port number")
	ErrInvalidHost      = fmt.Errorf("invalid host address")
	ErrInvalidTimeout   = fmt.Errorf("invalid timeout value")
	ErrInvalidTLSConfig = fmt.Errorf("invalid TLS configuration")
)
