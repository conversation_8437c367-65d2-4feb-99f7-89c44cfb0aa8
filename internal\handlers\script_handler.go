package handlers

import (
	"net/http"
	"strconv"
	"time"

	"aike_go/internal/interfaces"
	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
)

// ErrorResponse 错误响应
// 思路：统一的错误响应格式
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

// SuccessResponse 成功响应
// 思路：统一的成功响应格式
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ScriptHandler 脚本管理处理器
// 思路：提供脚本管理的HTTP API接口
type ScriptHandler struct {
	scriptService *services.ScriptService
}

// NewScriptHandler 创建脚本处理器
// 思路：初始化脚本处理器
// 使用例子：handler := NewScriptHandler(scriptService)
func NewScriptHandler(scriptService *services.ScriptService) *ScriptHandler {
	return &ScriptHandler{
		scriptService: scriptService,
	}
}

// CreateScript 创建脚本
// 思路：处理创建脚本的HTTP请求
// @Summary 创建脚本
// @Description 创建新的Lua脚本
// @Tags scripts
// @Accept json
// @Produce json
// @Param script body services.CreateScriptRequest true "脚本信息"
// @Success 201 {object} interfaces.Script
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts [post]
func (h *ScriptHandler) CreateScript(c *gin.Context) {
	var req services.CreateScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	script, err := h.scriptService.CreateScript(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "创建脚本失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, script)
}

// UpdateScript 更新脚本
// 思路：处理更新脚本的HTTP请求
// @Summary 更新脚本
// @Description 更新现有脚本
// @Tags scripts
// @Accept json
// @Produce json
// @Param id path string true "脚本ID"
// @Param script body services.UpdateScriptRequest true "更新信息"
// @Success 200 {object} interfaces.Script
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts/{id} [put]
func (h *ScriptHandler) UpdateScript(c *gin.Context) {
	scriptID := c.Param("id")
	if scriptID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "脚本ID不能为空",
			Message: "请提供有效的脚本ID",
		})
		return
	}

	var req services.UpdateScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	script, err := h.scriptService.UpdateScript(c.Request.Context(), scriptID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "更新脚本失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, script)
}

// DeleteScript 删除脚本
// 思路：处理删除脚本的HTTP请求
// @Summary 删除脚本
// @Description 删除指定脚本
// @Tags scripts
// @Produce json
// @Param id path string true "脚本ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts/{id} [delete]
func (h *ScriptHandler) DeleteScript(c *gin.Context) {
	scriptID := c.Param("id")
	if scriptID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "脚本ID不能为空",
			Message: "请提供有效的脚本ID",
		})
		return
	}

	err := h.scriptService.DeleteScript(c.Request.Context(), scriptID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "删除脚本失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "脚本删除成功",
	})
}

// GetScript 获取脚本详情
// 思路：处理获取单个脚本的HTTP请求
// @Summary 获取脚本详情
// @Description 根据ID获取脚本详情
// @Tags scripts
// @Produce json
// @Param id path string true "脚本ID"
// @Success 200 {object} interfaces.Script
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts/{id} [get]
func (h *ScriptHandler) GetScript(c *gin.Context) {
	scriptID := c.Param("id")
	if scriptID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "脚本ID不能为空",
			Message: "请提供有效的脚本ID",
		})
		return
	}

	script, err := h.scriptService.GetScript(c.Request.Context(), scriptID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "脚本不存在",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, script)
}

// ListScripts 列出脚本
// 思路：处理获取脚本列表的HTTP请求
// @Summary 列出脚本
// @Description 分页获取脚本列表
// @Tags scripts
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param type query string false "脚本类型"
// @Param enabled query bool false "是否启用"
// @Success 200 {object} services.ListScriptsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts [get]
func (h *ScriptHandler) ListScripts(c *gin.Context) {
	var req services.ListScriptsRequest

	// 解析查询参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		} else {
			req.Page = 1
		}
	} else {
		req.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			req.PageSize = pageSize
		} else {
			req.PageSize = 20
		}
	} else {
		req.PageSize = 20
	}

	req.Type = c.Query("type")

	if enabledStr := c.Query("enabled"); enabledStr != "" {
		if enabled, err := strconv.ParseBool(enabledStr); err == nil {
			req.Enabled = &enabled
		}
	}

	response, err := h.scriptService.ListScripts(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "获取脚本列表失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ExecuteScript 执行脚本
// 思路：处理执行脚本的HTTP请求
// @Summary 执行脚本
// @Description 执行指定的Lua脚本
// @Tags scripts
// @Accept json
// @Produce json
// @Param id path string true "脚本ID"
// @Param request body services.ExecuteScriptRequest true "执行参数"
// @Success 200 {object} interfaces.ScriptResult
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/scripts/{id}/execute [post]
func (h *ScriptHandler) ExecuteScript(c *gin.Context) {
	scriptID := c.Param("id")
	if scriptID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "脚本ID不能为空",
			Message: "请提供有效的脚本ID",
		})
		return
	}

	var req services.ExecuteScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	// 如果请求中没有指定脚本ID，使用路径参数
	if req.ScriptID == "" {
		req.ScriptID = scriptID
	}

	// 构建脚本上下文
	scriptCtx := &interfaces.ScriptContext{
		Variables: req.Variables,
		Platform:  "api",
		RequestID: c.GetHeader("X-Request-ID"),
		Timestamp: time.Now(),
	}

	// 添加请求上下文信息
	if req.Context != nil {
		for k, v := range req.Context {
			if scriptCtx.Variables == nil {
				scriptCtx.Variables = make(map[string]interface{})
			}
			scriptCtx.Variables[k] = v
		}
	}

	// 暂时禁用脚本执行功能，避免循环导入
	// result, err := h.scriptService.ExecuteScript(c.Request.Context(), req.ScriptID, scriptCtx)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, ErrorResponse{
	// 		Error:   "执行脚本失败",
	// 		Message: err.Error(),
	// 	})
	// 	return
	// }

	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "脚本执行功能暂时禁用",
		"context": scriptCtx,
	})
}

// GetScriptTypes 获取脚本类型列表
// 思路：返回支持的脚本类型
// @Summary 获取脚本类型
// @Description 获取支持的脚本类型列表
// @Tags scripts
// @Produce json
// @Success 200 {object} map[string]string
// @Router /api/v1/scripts/types [get]
func (h *ScriptHandler) GetScriptTypes(c *gin.Context) {
	types := map[string]string{
		"message_filter":  "消息过滤器 - 用于过滤和预处理接收到的消息",
		"message_handler": "消息处理器 - 用于处理消息并生成响应",
		"business_rule":   "业务规则 - 用于执行业务逻辑和规则验证",
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"types": types,
	})
}
