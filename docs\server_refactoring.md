# 服务器重构文档

## 🎯 重构目标

原来的 `internal/server/server.go` 文件过于庞大（800+行），包含了太多职责，导致代码难以维护和测试。本次重构的目标是：

1. **模块化分离**：将不同职责的代码分离到不同的模块中
2. **提高可维护性**：每个模块职责单一，便于理解和修改
3. **提高可测试性**：通过依赖注入，便于单元测试和集成测试
4. **提高可扩展性**：新功能可以通过添加新的处理器和服务来实现

## 📁 重构后的目录结构

```
internal/server/
├── server_refactored.go      # 重构后的主服务器文件
├── config.go                 # 服务器配置管理
├── routes.go                 # 路由管理器
└── handlers/                 # HTTP处理器目录
    ├── message_handlers.go    # 消息相关处理器
    ├── platform_handlers.go   # 平台相关处理器
    ├── knowledge_handlers.go  # 知识库相关处理器
    ├── websocket_handlers.go  # WebSocket相关处理器
    ├── monitor_handlers.go    # 监控相关处理器
    └── page_handlers.go       # 页面相关处理器

cmd/server/
└── main_refactored.go        # 重构后的启动文件示例
```

## 🏗️ 架构设计

### 1. 核心组件

#### RefactoredServer
- **职责**：服务器的核心协调器
- **功能**：管理所有处理器、服务和路由
- **特点**：通过依赖注入获取服务实例

#### ServerConfig
- **职责**：服务器配置管理
- **功能**：集中管理所有配置项
- **特点**：支持验证、默认值、环境变量覆盖

#### RouteManager
- **职责**：路由管理
- **功能**：统一管理所有HTTP路由
- **特点**：模块化路由设置，便于维护

### 2. 处理器模块

每个处理器负责特定领域的HTTP请求处理：

- **MessageHandlers**：消息发送、历史记录
- **PlatformHandlers**：平台管理、统计信息
- **KnowledgeHandlers**：知识库CRUD操作
- **WebSocketHandlers**：WebSocket连接管理
- **MonitorHandlers**：系统监控、健康检查
- **PageHandlers**：页面渲染

### 3. 依赖注入

通过 `ServerDependencies` 结构体注入所有服务依赖：

```go
type ServerDependencies struct {
    AdapterManager   *adapters.AdapterManager
    ChatService      *services.ChatService
    KnowledgeService *services.KnowledgeService
    ScriptService    *services.ScriptService
    WSService        *services.WebSocketService
}
```

## 🚀 使用方式

### 1. 基本使用

```go
// 创建配置
config := server.DefaultServerConfig()

// 初始化依赖
deps := &server.ServerDependencies{
    AdapterManager:   adapterManager,
    ChatService:      chatService,
    // ... 其他服务
}

// 创建服务器
srv := server.NewRefactoredServer(config, deps)

// 启动服务器
err := srv.Start(ctx)
```

### 2. 自定义配置

```go
config := server.DefaultServerConfig()
config.Port = 9090
config.Host = "127.0.0.1"
config.Mode = "release"

// 启用TLS
config.TLS.Enabled = true
config.TLS.CertFile = "cert.pem"
config.TLS.KeyFile = "key.pem"

// 配置CORS
config.CORS.AllowOrigins = []string{"https://example.com"}
```

### 3. 测试支持

```go
func TestAPI(t *testing.T) {
    // 创建测试配置
    config := server.DefaultServerConfig()
    config.Port = 0 // 随机端口
    
    // 模拟依赖
    deps := &server.ServerDependencies{
        AdapterManager: mockAdapterManager,
        ChatService:    mockChatService,
    }
    
    // 创建测试服务器
    srv := server.NewRefactoredServer(config, deps)
    engine := srv.GetEngine()
    
    // 测试API
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("GET", "/api/v1/platforms/", nil)
    engine.ServeHTTP(w, req)
    
    assert.Equal(t, 200, w.Code)
}
```

## 🔧 扩展指南

### 1. 添加新的处理器

1. 在 `internal/server/handlers/` 目录下创建新的处理器文件
2. 实现处理器结构体和方法
3. 在 `RefactoredServer` 中添加处理器字段
4. 在 `initHandlers()` 方法中初始化处理器
5. 在路由管理器中添加相应的路由

### 2. 添加新的服务

1. 在 `internal/services/` 目录下创建新的服务文件
2. 在 `ServerDependencies` 中添加服务字段
3. 在启动代码中初始化服务

### 3. 添加新的中间件

1. 在 `initMiddleware()` 方法中添加中间件
2. 可以根据配置条件性地启用中间件

### 4. 添加新的配置项

1. 在 `ServerConfig` 结构体中添加配置字段
2. 在 `DefaultServerConfig()` 中设置默认值
3. 在 `Validate()` 方法中添加验证逻辑

## 📊 重构效果对比

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| 文件大小 | 800+ 行 | 主文件 < 300 行 |
| 职责分离 | 单一文件多职责 | 多文件单一职责 |
| 可测试性 | 难以测试 | 易于单元测试 |
| 可维护性 | 难以维护 | 易于维护和扩展 |
| 代码复用 | 低 | 高 |

## 🎯 最佳实践

1. **单一职责原则**：每个处理器只负责特定领域的功能
2. **依赖注入**：通过构造函数注入依赖，便于测试
3. **配置管理**：集中管理配置，支持验证和默认值
4. **错误处理**：统一的错误处理和响应格式
5. **日志记录**：在关键位置添加日志，便于调试
6. **文档注释**：为每个方法添加详细的注释说明

## 🔄 迁移步骤

1. **逐步迁移**：可以逐个模块迁移，不需要一次性全部重构
2. **保持兼容**：在迁移过程中保持API兼容性
3. **测试验证**：每个模块迁移后都要进行充分测试
4. **性能监控**：监控重构后的性能表现

## 📝 注意事项

1. 当前的重构版本是一个架构示例，实际使用时需要根据具体的服务实现进行调整
2. 某些服务的构造函数可能需要特定的参数，需要在 `initializeDependencies` 中正确初始化
3. 路由中的某些处理器方法可能还未实现，需要根据实际需求补充
4. 建议在生产环境使用前进行充分的测试和验证

## 🚀 下一步计划

1. **完善处理器实现**：补充所有处理器方法的具体实现
2. **添加中间件**：实现认证、限流、监控等中间件
3. **配置文件支持**：支持从YAML/JSON文件加载配置
4. **性能优化**：优化路由性能和内存使用
5. **文档完善**：补充API文档和使用示例
