# 服务器配置
PORT=8080
HOST=0.0.0.0

# 数据库配置
DB_TYPE=sqlite
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=
DB_PASSWORD=
DB_DATABASE=aike_go.db

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 硅基流动配置（兼容OpenAI接口）
# 使用硅基流动时，将上面的OPENAI_BASE_URL改为：https://api.siliconflow.cn/v1
# 并将OPENAI_MODEL改为硅基流动支持的模型，例如：
# OPENAI_BASE_URL=https://api.siliconflow.cn/v1
# OPENAI_MODEL=Qwen/Qwen2.5-72B-Instruct
# OPENAI_MODEL=deepseek-ai/DeepSeek-V3
# OPENAI_MODEL=meta-llama/Llama-3.1-8B-Instruct

# QQ平台配置（基于NapCat）
QQ_ENABLED=false
QQ_BASE_URL=http://localhost:3000
QQ_TOKEN=your_qq_token_here

# 微信平台配置
WECHAT_ENABLED=false
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_SECRET=your_wechat_secret_here

# Telegram平台配置
TELEGRAM_ENABLED=false
TELEGRAM_TOKEN=your_telegram_bot_token_here

# 千牛平台配置
QIANNIU_ENABLED=false
QIANNIU_APP_KEY=your_qianniu_app_key_here
QIANNIU_SECRET=your_qianniu_secret_here
