# AI客服系统前端管理界面使用指南

## 概述

AI客服系统提供了一个基于Vue.js和Element Plus的现代化Web管理界面，用于管理和监控整个客服系统。

## 功能特性

### 🎯 核心功能模块

1. **仪表盘** - 系统概览和关键指标
2. **用户管理** - 多平台用户统一管理
3. **消息管理** - 消息记录查看和分析
4. **知识库管理** - 知识条目的增删改查
5. **脚本管理** - Lua脚本的配置和执行
6. **平台管理** - 各平台连接状态和配置
7. **系统监控** - 实时系统指标和性能监控

### 🚀 技术特点

- **响应式设计** - 支持桌面和移动设备
- **实时数据** - 自动刷新和实时更新
- **模块化架构** - 清晰的功能分离
- **用户友好** - 直观的操作界面

## 快速开始

### 1. 启动服务器

```bash
# 编译项目
go build -o bin/aike_server.exe cmd/server/main.go

# 启动服务器
./bin/aike_server.exe
```

### 2. 访问管理界面

- **系统首页**: http://localhost:8080/
- **管理后台**: http://localhost:8080/admin
- **WebSocket测试**: http://localhost:8080/static/websocket_test.html

### 3. 系统状态检查

- **基础健康检查**: http://localhost:8080/health
- **详细健康检查**: http://localhost:8080/api/v1/monitor/health/detailed

## 界面功能详解

### 仪表盘 (Dashboard)

显示系统关键指标：
- 总用户数、总消息数、总会话数、知识库条目数
- 系统健康状态、运行时间、数据库状态
- WebSocket连接数等实时信息

### 用户管理 (Users)

**功能特性：**
- 用户列表查看和搜索
- 按平台筛选用户
- 用户状态管理（活跃/非活跃/已封禁）
- VIP用户标识
- 用户活跃度统计

**操作说明：**
1. 使用搜索框按昵称或ID搜索用户
2. 选择平台过滤器筛选特定平台用户
3. 点击"查看"按钮查看用户详情
4. 点击"封禁"按钮管理用户状态

### 消息管理 (Messages)

**功能特性：**
- 消息记录查看和搜索
- 按平台和方向筛选消息
- AI生成消息标识
- Token使用量统计
- 消息详情查看

**筛选选项：**
- 平台：QQ、微信、Telegram等
- 方向：用户发送、机器人回复
- 内容搜索：支持消息内容关键词搜索

### 知识库管理 (Knowledge)

**功能特性：**
- 知识条目的增删改查
- 按分类筛选知识库
- 知识库状态管理
- 使用次数统计
- 优先级设置

**操作流程：**
1. 点击"新增知识"创建知识条目
2. 使用分类筛选器查找特定类型知识
3. 编辑或删除现有知识条目
4. 查看知识库使用统计

### 脚本管理 (Scripts)

**功能特性：**
- Lua脚本的配置和管理
- 脚本类型分类（消息处理器、业务规则、插件、过滤器）
- 脚本启用/禁用控制
- 执行次数统计
- 脚本执行功能

**脚本类型：**
- **消息处理器** - 处理用户消息
- **业务规则** - 自定义业务逻辑
- **插件** - 扩展功能模块
- **过滤器** - 消息过滤和预处理

### 平台管理 (Platforms)

**功能特性：**
- 多平台连接状态监控
- 平台启用/禁用控制
- 连接测试功能
- 心跳监控
- 消息统计

**支持平台：**
- QQ（通过NapCat）
- 微信
- Telegram
- 其他自定义平台

### 系统监控 (Monitor)

**监控指标：**
- 总请求数、活跃请求数
- 错误率、平均响应时间
- P95/P99响应时间
- 状态码分布
- 系统运行时间

## API接口说明

前端通过以下REST API与后端通信：

### 基础API
- `GET /health` - 基础健康检查
- `GET /api/v1/monitor/health/detailed` - 详细健康检查
- `GET /api/v1/monitor/metrics` - 系统指标

### 用户管理API
- `GET /api/v1/users` - 获取用户列表（开发中）
- `PUT /api/v1/users/:id` - 更新用户信息（开发中）

### 消息管理API
- `GET /api/v1/messages/history` - 获取消息历史
- `POST /api/v1/messages/send` - 发送消息

### 知识库API
- `GET /api/v1/knowledge/` - 获取知识库列表
- `POST /api/v1/knowledge/` - 创建知识条目
- `PUT /api/v1/knowledge/:id` - 更新知识条目
- `DELETE /api/v1/knowledge/:id` - 删除知识条目
- `POST /api/v1/knowledge/search` - 搜索知识库

### 脚本管理API
- `GET /api/v1/scripts/` - 获取脚本列表
- `POST /api/v1/scripts/` - 创建脚本
- `PUT /api/v1/scripts/:id` - 更新脚本
- `DELETE /api/v1/scripts/:id` - 删除脚本
- `POST /api/v1/scripts/:id/execute` - 执行脚本

### 平台管理API
- `GET /api/v1/platforms/` - 获取平台列表
- `POST /api/v1/platforms/:platform/test` - 测试平台连接

## 开发说明

### 技术栈
- **前端框架**: Vue.js 3
- **UI组件库**: Element Plus
- **HTTP客户端**: Axios
- **图标库**: Element Plus Icons

### 文件结构
```
web/
├── admin.html          # 管理后台主页面
├── index.html          # 系统首页
└── websocket_test.html # WebSocket测试页面
```

### 自定义开发

如需扩展前端功能，可以：

1. **添加新的视图模块**
   - 在HTML中添加新的视图div
   - 在Vue.js的data中添加对应的数据
   - 实现相应的方法和API调用

2. **修改样式**
   - 直接修改CSS样式
   - 使用Element Plus的主题定制

3. **添加新的API接口**
   - 在methods中添加新的API调用方法
   - 配置axios拦截器处理错误

## 故障排除

### 常见问题

1. **页面无法访问**
   - 检查服务器是否正常启动
   - 确认端口8080未被占用
   - 检查防火墙设置

2. **API调用失败**
   - 检查浏览器控制台错误信息
   - 确认API端点是否正确
   - 检查服务器日志

3. **数据不显示**
   - 检查API返回的数据格式
   - 确认前端数据绑定是否正确
   - 查看浏览器网络请求

### 调试技巧

1. **使用浏览器开发者工具**
   - F12打开开发者工具
   - 查看Console面板的错误信息
   - 检查Network面板的API请求

2. **查看服务器日志**
   - 检查终端输出的日志信息
   - 查看logs目录下的日志文件

## 更新日志

### v1.0.0 (2025-07-16)
- 初始版本发布
- 实现基础的管理界面
- 支持用户、消息、知识库、脚本、平台管理
- 集成系统监控功能
- 响应式设计支持

## 支持与反馈

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查GitHub Issues
3. 联系开发团队

---

**注意**: 部分功能仍在开发中，具体API接口请参考最新的API文档。
