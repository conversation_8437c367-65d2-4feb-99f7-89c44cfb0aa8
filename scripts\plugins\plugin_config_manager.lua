-- 插件配置管理器
-- 思路：通过Lua脚本动态配置插件参数
-- 类型：message_handler
-- 优先级：40

-- 检查消息和用户信息
if not message or not message.content then
    return nil
end

if not message.from or not message.from.platform_user_id then
    return nil
end

local content = message.content:lower()
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

-- 只处理配置相关命令
if not (content:find("^/config") or content:find("^!config")) then
    return nil
end

log("info", "插件配置管理器处理命令: " .. content)

-- 解析命令
local command_parts = {}
for word in message.content:gmatch("%S+") do
    table.insert(command_parts, word)
end

if #command_parts < 2 then
    send_message(user_id, [[⚙️ 插件配置管理帮助：
• /config list - 查看所有插件配置
• /config get <plugin> - 查看指定插件配置
• /config set <plugin> <key> <value> - 设置插件配置
• /config reset <plugin> - 重置插件配置
• /config reload <plugin> - 重新加载插件
• /config backup - 备份所有配置
• /config restore - 恢复配置]])
    return "已发送配置帮助信息"
end

local action = command_parts[2]

-- 1. 列出所有插件配置
if action == "list" then
    local plugins = list_plugins()
    if plugins and #plugins > 0 then
        local config_list = "⚙️ 所有插件配置概览：\n\n"

        for i, plugin in ipairs(plugins) do
            local status_icon = plugin.enabled and "🟢" or "🔴"
            config_list = config_list .. string.format("%s **%s** v%s\n",
                status_icon, plugin.name, plugin.version)

            local config = get_plugin_config(plugin.name)
            if config then
                local config_count = 0
                for _ in pairs(config) do
                    config_count = config_count + 1
                end
                config_list = config_list .. string.format("   📋 配置项: %d 个\n", config_count)
            else
                config_list = config_list .. "   📋 配置项: 无\n"
            end
            config_list = config_list .. "\n"
        end

        send_message(user_id, config_list)
    else
        send_message(user_id, "❌ 没有找到任何插件")
    end
    return "已发送配置列表"
end

-- 2. 查看指定插件配置
if action == "get" and #command_parts >= 3 then
    local plugin_name = command_parts[3]
    local config = get_plugin_config(plugin_name)

    if config then
        local config_text = "⚙️ 插件 **" .. plugin_name .. "** 的配置：\n\n"

        for key, value in pairs(config) do
            local value_str = tostring(value)
            local value_type = type(value)

            -- 格式化不同类型的值
            if value_type == "boolean" then
                value_str = value and "✅ true" or "❌ false"
            elseif value_type == "number" then
                value_str = "🔢 " .. value_str
            elseif value_type == "string" then
                value_str = "📝 \"" .. value_str .. "\""
            elseif value_type == "table" then
                value_str = "📋 [表格数据]"
            end

            config_text = config_text .. string.format("• **%s**: %s\n", key, value_str)
        end

        send_message(user_id, config_text)
    else
        send_message(user_id, "❌ 无法获取插件 " .. plugin_name .. " 的配置")
    end
    return "已发送插件配置"
end

-- 3. 设置插件配置
if action == "set" and #command_parts >= 5 then
    local plugin_name = command_parts[3]
    local config_key = command_parts[4]
    local config_value = command_parts[5]

    -- 尝试转换值类型
    local typed_value = config_value
    if config_value == "true" then
        typed_value = true
    elseif config_value == "false" then
        typed_value = false
    elseif tonumber(config_value) then
        typed_value = tonumber(config_value)
    end

    -- 获取当前配置
    local current_config = get_plugin_config(plugin_name)
    if not current_config then
        send_message(user_id, "❌ 插件 " .. plugin_name .. " 不存在或无法获取配置")
        return "插件不存在"
    end

    -- 更新配置
    current_config[config_key] = typed_value
    local success, message_text = set_plugin_config(plugin_name, current_config)

    if success then
        send_message(user_id, string.format("✅ 已设置 %s.%s = %s",
            plugin_name, config_key, tostring(typed_value)))
    else
        send_message(user_id, "❌ 设置失败: " .. message_text)
    end
    return "配置设置完成"
end

-- 4. 重置插件配置
if action == "reset" and #command_parts >= 3 then
    local plugin_name = command_parts[3]

    -- 这里应该有默认配置，简化处理
    local default_configs = {
        database_storage = {
            table_name = "plugin_chat_messages",
            auto_migrate = true,
            retention_days = 365,
            backup_enabled = true,
            backup_interval = "24h",
            compression = true,
            encryption = false,
        }
    }

    local default_config = default_configs[plugin_name]
    if default_config then
        local success, message_text = set_plugin_config(plugin_name, default_config)
        if success then
            send_message(user_id, "✅ 插件 " .. plugin_name .. " 配置已重置为默认值")
        else
            send_message(user_id, "❌ 重置失败: " .. message_text)
        end
    else
        send_message(user_id, "❌ 没有找到插件 " .. plugin_name .. " 的默认配置")
    end
    return "配置重置完成"
end

-- 5. 重新加载插件
if action == "reload" and #command_parts >= 3 then
    local plugin_name = command_parts[3]
    local success, message_text = reload_plugin(plugin_name)

    if success then
        send_message(user_id, "🔄 " .. message_text)
    else
        send_message(user_id, "❌ " .. message_text)
    end
    return "插件重载完成"
end

-- 6. 备份配置
if action == "backup" then
    local plugins = list_plugins()
    if plugins and #plugins > 0 then
        local backup_data = {
            timestamp = os.date("%Y-%m-%d %H:%M:%S"),
            plugins = {}
        }

        for _, plugin in ipairs(plugins) do
            local config = get_plugin_config(plugin.name)
            if config then
                backup_data.plugins[plugin.name] = {
                    enabled = plugin.enabled,
                    config = config
                }
            end
        end

        -- 这里应该保存到文件，简化处理只显示信息
        local backup_info = string.format([[💾 配置备份信息：
• 备份时间: %s
• 备份插件数: %d
• 备份状态: 完成

备份包含的插件:]],
            backup_data.timestamp,
            #plugins)

        for plugin_name, _ in pairs(backup_data.plugins) do
            backup_info = backup_info .. "\n• " .. plugin_name
        end

        send_message(user_id, backup_info)
    else
        send_message(user_id, "❌ 没有找到可备份的插件")
    end
    return "配置备份完成"
end

-- 7. 恢复配置
if action == "restore" then
    send_message(user_id, [[🔄 配置恢复功能：
• 此功能需要指定备份文件
• 使用方法: /config restore <backup_file>
• 恢复前会自动备份当前配置
• 恢复后需要重启相关插件

⚠️ 注意：配置恢复是危险操作，请谨慎使用]])
    return "配置恢复说明"
end

-- 8. 高级配置命令
if action == "advanced" then
    send_message(user_id, [[🔧 高级配置选项：

**存储插件配置:**
• retention_days: 数据保留天数
• auto_migrate: 自动数据库迁移
• backup_enabled: 启用自动备份
• compression: 启用数据压缩

**示例命令:**
/config set database_storage retention_days 30
/config set database_storage backup_enabled true

**批量操作:**
/config batch enable all
/config batch disable storage
/config batch reset all]])
    return "已发送高级配置帮助"
end

-- 9. 批量操作
if action == "batch" and #command_parts >= 4 then
    local batch_action = command_parts[3]
    local target = command_parts[4]

    if batch_action == "enable" and target == "all" then
        local plugins = list_plugins()
        local enabled_count = 0

        for _, plugin in ipairs(plugins) do
            if not plugin.enabled then
                local success, _ = enable_plugin(plugin.name)
                if success then
                    enabled_count = enabled_count + 1
                end
            end
        end

        send_message(user_id, string.format("✅ 批量启用完成，共启用 %d 个插件", enabled_count))
        return "批量启用完成"
    end

    if batch_action == "disable" and target == "all" then
        local plugins = list_plugins()
        local disabled_count = 0

        for _, plugin in ipairs(plugins) do
            if plugin.enabled then
                local success, _ = disable_plugin(plugin.name)
                if success then
                    disabled_count = disabled_count + 1
                end
            end
        end

        send_message(user_id, string.format("✅ 批量禁用完成，共禁用 %d 个插件", disabled_count))
        return "批量禁用完成"
    end
end

-- 未知配置命令
send_message(user_id, "❌ 未知的配置命令，使用 /config 查看帮助")
return "未知配置命令"
