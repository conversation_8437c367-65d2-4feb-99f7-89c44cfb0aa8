package examples

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"
	
	// 注意：这里的导入路径需要在生成 proto 代码后才能使用
	// aikev1 "aike_go/internal/proto/aike/v1"
)

// 注意：以下代码是示例，需要先生成 proto 代码才能编译

/*
// ProtoUsageExample 展示如何使用生成的 Protocol Buffers 代码
// 思路：提供完整的使用示例，包括数据模型创建、gRPC 服务实现和客户端调用
func ProtoUsageExample() {
	fmt.Println("=== AI客服系统 Protocol Buffers 使用示例 ===")
	
	// 1. 数据模型使用示例
	demonstrateDataModels()
	
	// 2. gRPC 服务示例
	demonstrateGRPCService()
	
	// 3. 客户端调用示例
	demonstrateGRPCClient()
}

// demonstrateDataModels 展示数据模型的创建和使用
func demonstrateDataModels() {
	fmt.Println("\n1. 数据模型使用示例")
	
	// 创建用户
	user := &aikev1.User{
		Id:         1,
		CreatedAt:  timestamppb.Now(),
		UpdatedAt:  timestamppb.Now(),
		PlatformId: "qq_123456789",
		Platform:   "qq",
		Nickname:   "张三",
		Avatar:     "https://example.com/avatar.jpg",
		Email:      "<EMAIL>",
		Status:     aikev1.UserStatus_USER_STATUS_ACTIVE,
		IsVip:      false,
		MessageCount: 0,
		LastActiveAt: timestamppb.Now(),
	}
	
	fmt.Printf("创建用户: %s (ID: %d, 平台: %s)\n", 
		user.Nickname, user.Id, user.Platform)
	
	// 创建消息
	message := &aikev1.Message{
		Id:        1,
		CreatedAt: timestamppb.Now(),
		UpdatedAt: timestamppb.Now(),
		UserId:    user.Id,
		SessionId: "qq_123456789_20240116",
		Content:   "你好，请问工作时间是什么？",
		Type:      aikev1.MessageType_MESSAGE_TYPE_TEXT,
		Direction: aikev1.MessageDirection_MESSAGE_DIRECTION_INCOMING,
		Platform:  "qq",
		Status:    aikev1.MessageStatus_MESSAGE_STATUS_SENT,
		IsAiGenerated: false,
		TokensUsed: 0,
	}
	
	fmt.Printf("创建消息: %s (类型: %s, 方向: %s)\n",
		message.Content, message.Type.String(), message.Direction.String())
	
	// 创建会话
	session := &aikev1.Session{
		Id:           1,
		CreatedAt:    timestamppb.Now(),
		UpdatedAt:    timestamppb.Now(),
		UserId:       user.Id,
		SessionId:    "qq_123456789_20240116",
		Platform:     "qq",
		Status:       aikev1.SessionStatus_SESSION_STATUS_ACTIVE,
		Title:        "客服咨询",
		StartedAt:    timestamppb.Now(),
		LastActiveAt: timestamppb.Now(),
		MessageCount: 1,
		AiModel:      "gpt-3.5-turbo",
		TotalTokens:  0,
		SystemPrompt: "你是一个专业的客服助手",
	}
	
	fmt.Printf("创建会话: %s (状态: %s, AI模型: %s)\n",
		session.Title, session.Status.String(), session.AiModel)
	
	// 创建知识库条目
	knowledge := &aikev1.Knowledge{
		Id:        1,
		CreatedAt: timestamppb.Now(),
		UpdatedAt: timestamppb.Now(),
		Title:     "工作时间说明",
		Content:   "我们的工作时间是周一至周五 9:00-18:00，周六 9:00-12:00，周日休息。",
		Summary:   "工作时间：周一至周五 9:00-18:00，周六上午",
		Category:  "常见问题",
		Keywords:  "工作时间,营业时间,上班时间",
		Tags:      "FAQ,时间",
		Status:    aikev1.KnowledgeStatus_KNOWLEDGE_STATUS_ACTIVE,
		Priority:  10,
		IsPublic:  true,
		ViewCount: 0,
		UseCount:  0,
		LikeCount: 0,
	}
	
	fmt.Printf("创建知识库: %s (分类: %s, 状态: %s)\n",
		knowledge.Title, knowledge.Category, knowledge.Status.String())
}

// MessageServiceServer gRPC 消息服务实现
type MessageServiceServer struct {
	aikev1.UnimplementedMessageServiceServer
}

// ProcessMessage 实现消息处理接口
func (s *MessageServiceServer) ProcessMessage(
	ctx context.Context,
	req *aikev1.ProcessMessageRequest,
) (*aikev1.ProcessMessageResponse, error) {
	fmt.Printf("处理消息: 用户=%s, 平台=%s, 内容=%s\n",
		req.PlatformId, req.Platform, req.Content)
	
	// 模拟消息处理逻辑
	var reply string
	var isFromKB bool
	var knowledgeID uint32
	
	// 简单的关键词匹配
	if contains(req.Content, []string{"工作时间", "营业时间", "上班时间"}) {
		reply = "我们的工作时间是周一至周五 9:00-18:00，周六 9:00-12:00，周日休息。"
		isFromKB = true
		knowledgeID = 1
	} else {
		reply = "感谢您的咨询，我是AI客服助手，很高兴为您服务！"
		isFromKB = false
		knowledgeID = 0
	}
	
	return &aikev1.ProcessMessageResponse{
		Reply:       reply,
		SessionId:   fmt.Sprintf("%s_%s_%s", req.Platform, req.PlatformId, time.Now().Format("20060102")),
		TokensUsed:  int32(len(reply) / 4), // 简单估算
		MessageId:   uint32(time.Now().Unix()),
		ReplyId:     uint32(time.Now().Unix() + 1),
		IsFromKb:    isFromKB,
		KnowledgeId: knowledgeID,
	}, nil
}

// GetMessageHistory 获取消息历史
func (s *MessageServiceServer) GetMessageHistory(
	ctx context.Context,
	req *aikev1.GetMessageHistoryRequest,
) (*aikev1.GetMessageHistoryResponse, error) {
	fmt.Printf("获取消息历史: 会话=%s\n", req.SessionId)
	
	// 模拟返回消息历史
	messages := []*aikev1.Message{
		{
			Id:        1,
			CreatedAt: timestamppb.Now(),
			UserId:    1,
			SessionId: req.SessionId,
			Content:   "你好，请问工作时间是什么？",
			Type:      aikev1.MessageType_MESSAGE_TYPE_TEXT,
			Direction: aikev1.MessageDirection_MESSAGE_DIRECTION_INCOMING,
			Platform:  "qq",
			Status:    aikev1.MessageStatus_MESSAGE_STATUS_READ,
		},
		{
			Id:        2,
			CreatedAt: timestamppb.Now(),
			UserId:    1,
			SessionId: req.SessionId,
			Content:   "我们的工作时间是周一至周五 9:00-18:00，周六 9:00-12:00，周日休息。",
			Type:      aikev1.MessageType_MESSAGE_TYPE_TEXT,
			Direction: aikev1.MessageDirection_MESSAGE_DIRECTION_OUTGOING,
			Platform:  "qq",
			Status:    aikev1.MessageStatus_MESSAGE_STATUS_SENT,
			IsAiGenerated: true,
			AiModel:      "gpt-3.5-turbo",
			TokensUsed:   25,
		},
	}
	
	return &aikev1.GetMessageHistoryResponse{
		Messages: messages,
		Pagination: &aikev1.PaginationResponse{
			Page:       1,
			PageSize:   10,
			TotalCount: int32(len(messages)),
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
		},
	}, nil
}

// demonstrateGRPCService 展示 gRPC 服务的实现
func demonstrateGRPCService() {
	fmt.Println("\n2. gRPC 服务实现示例")
	
	// 创建 gRPC 服务器
	server := grpc.NewServer()
	
	// 注册消息服务
	messageService := &MessageServiceServer{}
	aikev1.RegisterMessageServiceServer(server, messageService)
	
	fmt.Println("gRPC 服务器已创建并注册消息服务")
	
	// 注意：实际使用时需要监听端口
	// lis, err := net.Listen("tcp", ":9090")
	// if err != nil {
	//     log.Fatalf("failed to listen: %v", err)
	// }
	// 
	// fmt.Println("gRPC 服务器启动在端口 9090")
	// if err := server.Serve(lis); err != nil {
	//     log.Fatalf("failed to serve: %v", err)
	// }
}

// demonstrateGRPCClient 展示 gRPC 客户端的使用
func demonstrateGRPCClient() {
	fmt.Println("\n3. gRPC 客户端调用示例")
	
	// 注意：实际使用时需要连接到真实的服务器
	// conn, err := grpc.Dial("localhost:9090", grpc.WithInsecure())
	// if err != nil {
	//     log.Fatalf("连接失败: %v", err)
	// }
	// defer conn.Close()
	
	// 创建客户端
	// client := aikev1.NewMessageServiceClient(conn)
	
	// 调用消息处理服务
	// response, err := client.ProcessMessage(context.Background(), &aikev1.ProcessMessageRequest{
	//     Platform:    "qq",
	//     PlatformId:  "123456789",
	//     Content:     "你好，请问工作时间是什么？",
	//     MessageType: "text",
	// })
	// 
	// if err != nil {
	//     log.Fatalf("调用失败: %v", err)
	// }
	// 
	// fmt.Printf("AI回复: %s\n", response.Reply)
	// fmt.Printf("Token使用: %d\n", response.TokensUsed)
	// fmt.Printf("来自知识库: %t\n", response.IsFromKb)
	
	fmt.Println("gRPC 客户端调用示例（需要连接到实际服务器）")
}

// contains 检查字符串是否包含任何关键词
func contains(text string, keywords []string) bool {
	for _, keyword := range keywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	return false
}

// UserServiceExample 用户服务使用示例
func UserServiceExample() {
	fmt.Println("\n=== 用户服务使用示例 ===")
	
	// 创建用户请求
	createReq := &aikev1.CreateUserRequest{
		PlatformId: "qq_987654321",
		Platform:   "qq",
		Nickname:   "李四",
		Avatar:     "https://example.com/avatar2.jpg",
		Email:      "<EMAIL>",
	}
	
	fmt.Printf("创建用户请求: %+v\n", createReq)
	
	// 更新用户请求
	updateReq := &aikev1.UpdateUserRequest{
		Id:       1,
		Nickname: "李四（已更新）",
		Status:   aikev1.UserStatus_USER_STATUS_ACTIVE,
		IsVip:    true,
	}
	
	fmt.Printf("更新用户请求: %+v\n", updateReq)
	
	// 用户列表请求
	listReq := &aikev1.ListUsersRequest{
		Platform: "qq",
		Status:   aikev1.UserStatus_USER_STATUS_ACTIVE,
		Pagination: &aikev1.PaginationRequest{
			Page:     1,
			PageSize: 10,
			SortBy:   "created_at",
			SortOrder: "desc",
		},
	}
	
	fmt.Printf("用户列表请求: %+v\n", listReq)
}

// KnowledgeServiceExample 知识库服务使用示例
func KnowledgeServiceExample() {
	fmt.Println("\n=== 知识库服务使用示例 ===")
	
	// 创建知识库请求
	createReq := &aikev1.CreateKnowledgeRequest{
		Title:    "退款政策",
		Content:  "我们支持7天无理由退款，退款将在3-5个工作日内到账。",
		Summary:  "7天无理由退款政策",
		Category: "售后服务",
		Keywords: "退款,退货,售后",
		Tags:     "policy,refund",
		Priority: 20,
		IsPublic: true,
	}
	
	fmt.Printf("创建知识库请求: %+v\n", createReq)
	
	// 搜索知识库请求
	searchReq := &aikev1.SearchKnowledgeRequest{
		Query:    "退款",
		Category: "售后服务",
		Status:   aikev1.KnowledgeStatus_KNOWLEDGE_STATUS_ACTIVE,
		Limit:    10,
	}
	
	fmt.Printf("搜索知识库请求: %+v\n", searchReq)
}

// StatsServiceExample 统计服务使用示例
func StatsServiceExample() {
	fmt.Println("\n=== 统计服务使用示例 ===")
	
	// 系统统计
	systemStats := &aikev1.SystemStats{
		TotalUsers:     1000,
		TotalMessages:  5000,
		TotalSessions:  800,
		ActiveSessions: 50,
		TotalKnowledge: 100,
		LastUpdated:    timestamppb.Now(),
	}
	
	fmt.Printf("系统统计: %+v\n", systemStats)
	
	// 平台统计请求
	platformStatsReq := &aikev1.GetPlatformStatsRequest{
		Platform: "qq",
	}
	
	fmt.Printf("平台统计请求: %+v\n", platformStatsReq)
	
	// 用户统计请求
	userStatsReq := &aikev1.GetUserStatsRequest{
		Platform:  "qq",
		TimeRange: "week",
	}
	
	fmt.Printf("用户统计请求: %+v\n", userStatsReq)
}
*/

// 由于需要先生成 proto 代码，这里提供一个简化的示例函数
func ProtoUsageExampleSimplified() {
	fmt.Println("=== AI客服系统 Protocol Buffers 使用示例 ===")
	fmt.Println("注意：需要先运行 'make generate' 生成 proto 代码")
	fmt.Println("")
	fmt.Println("1. 进入 proto 目录: cd proto")
	fmt.Println("2. 安装依赖工具: make install-tools")
	fmt.Println("3. 检查工具: make check-tools")
	fmt.Println("4. 生成代码: make generate")
	fmt.Println("5. 生成文档: make docs")
	fmt.Println("")
	fmt.Println("生成代码后，可以使用以下方式导入：")
	fmt.Println(`import aikev1 "aike_go/internal/proto/aike/v1"`)
	fmt.Println("")
	fmt.Println("详细使用方法请参考 proto/README.md")
}
