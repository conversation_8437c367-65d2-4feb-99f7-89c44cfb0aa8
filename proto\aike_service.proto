syntax = "proto3";

package aike.v1;

option go_package = "aike_go/proto/aike/v1;aikev1";

import "google/protobuf/timestamp.proto";

// ============================================================================
// 数据库模型定义 (Database Models)
// ============================================================================

// 用户模型
// 思路：统一管理不同平台的用户信息
message User {
  uint32 id = 1;                                    // 主键ID
  google.protobuf.Timestamp created_at = 2;         // 创建时间
  google.protobuf.Timestamp updated_at = 3;         // 更新时间
  google.protobuf.Timestamp deleted_at = 4;         // 软删除时间（可选）
  
  // 平台相关信息
  string platform_id = 5;                           // 平台用户ID（如QQ号、微信openid等）
  string platform = 6;                              // 平台类型：qq, wechat, telegram, qianniu
  
  // 用户基本信息
  string nickname = 7;                               // 用户昵称
  string avatar = 8;                                 // 头像URL
  string email = 9;                                  // 邮箱
  string phone = 10;                                 // 手机号
  
  // 用户状态
  UserStatus status = 11;                            // 用户状态
  bool is_vip = 12;                                  // 是否VIP用户
  
  // 统计信息
  int32 message_count = 13;                          // 消息总数
  google.protobuf.Timestamp last_active_at = 14;    // 最后活跃时间
}

// 用户状态枚举
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_BLOCKED = 2;
  USER_STATUS_INACTIVE = 3;
}

// 消息模型
// 思路：存储所有平台的消息记录，支持不同消息类型
message Message {
  uint32 id = 1;                                    // 主键ID
  google.protobuf.Timestamp created_at = 2;         // 创建时间
  google.protobuf.Timestamp updated_at = 3;         // 更新时间
  google.protobuf.Timestamp deleted_at = 4;         // 软删除时间（可选）
  
  // 关联用户
  uint32 user_id = 5;                               // 用户ID
  
  // 会话信息
  string session_id = 6;                            // 会话ID（用于分组对话）
  
  // 消息内容
  string content = 7;                               // 消息内容
  MessageType type = 8;                             // 消息类型
  MessageDirection direction = 9;                   // 消息方向
  
  // 平台相关
  string platform = 10;                            // 平台类型
  string platform_msg_id = 11;                     // 平台消息ID
  
  // 消息状态
  MessageStatus status = 12;                        // 消息状态
  
  // AI相关
  bool is_ai_generated = 13;                        // 是否AI生成
  string ai_model = 14;                             // 使用的AI模型
  int32 tokens_used = 15;                           // 使用的token数量
  
  // 扩展字段
  string metadata = 16;                             // 元数据（JSON格式）
  
  // 关联关系
  uint32 parent_id = 17;                            // 父消息ID（用于回复）
  uint32 knowledge_id = 18;                         // 知识库条目ID
}

// 消息类型枚举
enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  MESSAGE_TYPE_TEXT = 1;
  MESSAGE_TYPE_IMAGE = 2;
  MESSAGE_TYPE_FILE = 3;
  MESSAGE_TYPE_AUDIO = 4;
  MESSAGE_TYPE_VIDEO = 5;
  MESSAGE_TYPE_LOCATION = 6;
}

// 消息方向枚举
enum MessageDirection {
  MESSAGE_DIRECTION_UNSPECIFIED = 0;
  MESSAGE_DIRECTION_INCOMING = 1;                   // 用户发送
  MESSAGE_DIRECTION_OUTGOING = 2;                   // 机器人回复
}

// 消息状态枚举
enum MessageStatus {
  MESSAGE_STATUS_UNSPECIFIED = 0;
  MESSAGE_STATUS_SENT = 1;
  MESSAGE_STATUS_DELIVERED = 2;
  MESSAGE_STATUS_READ = 3;
  MESSAGE_STATUS_FAILED = 4;
}

// 会话模型
// 思路：管理用户会话状态，支持上下文保持和会话统计
message Session {
  uint32 id = 1;                                    // 主键ID
  google.protobuf.Timestamp created_at = 2;         // 创建时间
  google.protobuf.Timestamp updated_at = 3;         // 更新时间
  google.protobuf.Timestamp deleted_at = 4;         // 软删除时间（可选）
  
  // 关联用户
  uint32 user_id = 5;                               // 用户ID
  
  // 会话标识
  string session_id = 6;                            // 会话唯一标识
  string platform = 7;                              // 平台类型
  
  // 会话状态
  SessionStatus status = 8;                         // 会话状态
  string title = 9;                                 // 会话标题
  
  // 时间信息
  google.protobuf.Timestamp started_at = 10;       // 会话开始时间
  google.protobuf.Timestamp last_active_at = 11;   // 最后活跃时间
  google.protobuf.Timestamp ended_at = 12;         // 会话结束时间（可选）
  
  // 统计信息
  int32 message_count = 13;                         // 消息总数
  int32 duration = 14;                              // 会话时长（秒）
  
  // AI相关
  string ai_model = 15;                             // 使用的AI模型
  int32 total_tokens = 16;                          // 总token使用量
  
  // 上下文信息
  string context = 17;                              // 会话上下文（JSON格式）
  string system_prompt = 18;                        // 系统提示词
  
  // 扩展字段
  string metadata = 19;                             // 元数据（JSON格式）
}

// 会话状态枚举
enum SessionStatus {
  SESSION_STATUS_UNSPECIFIED = 0;
  SESSION_STATUS_ACTIVE = 1;
  SESSION_STATUS_INACTIVE = 2;
  SESSION_STATUS_CLOSED = 3;
}

// 知识库模型
// 思路：存储知识库条目，支持向量搜索和分类管理
message Knowledge {
  uint32 id = 1;                                    // 主键ID
  google.protobuf.Timestamp created_at = 2;         // 创建时间
  google.protobuf.Timestamp updated_at = 3;         // 更新时间
  google.protobuf.Timestamp deleted_at = 4;         // 软删除时间（可选）
  
  // 内容信息
  string title = 5;                                 // 标题
  string content = 6;                               // 内容
  string summary = 7;                               // 摘要
  string category = 8;                              // 分类
  string keywords = 9;                              // 关键词（逗号分隔）
  string tags = 10;                                 // 标签（逗号分隔）
  
  // 状态和权限
  KnowledgeStatus status = 11;                      // 状态
  int32 priority = 12;                              // 优先级（数字越大优先级越高）
  bool is_public = 13;                              // 是否公开
  
  // 使用统计
  int32 view_count = 14;                            // 查看次数
  int32 use_count = 15;                             // 使用次数
  int32 like_count = 16;                            // 点赞次数
  
  // 向量搜索相关
  string embedding = 17;                            // 向量嵌入（JSON格式）
  string embedding_model = 18;                      // 嵌入模型
  
  // 扩展字段
  string metadata = 19;                             // 元数据（JSON格式）
}

// 知识库状态枚举
enum KnowledgeStatus {
  KNOWLEDGE_STATUS_UNSPECIFIED = 0;
  KNOWLEDGE_STATUS_ACTIVE = 1;
  KNOWLEDGE_STATUS_INACTIVE = 2;
  KNOWLEDGE_STATUS_DRAFT = 3;
}

// 脚本模型
// 思路：管理Lua脚本的配置和执行状态
message Script {
  uint32 id = 1;                                    // 主键ID
  google.protobuf.Timestamp created_at = 2;         // 创建时间
  google.protobuf.Timestamp updated_at = 3;         // 更新时间
  google.protobuf.Timestamp deleted_at = 4;         // 软删除时间（可选）
  
  // 脚本信息
  string name = 5;                                  // 脚本名称
  string description = 6;                           // 脚本描述
  string content = 7;                               // 脚本内容
  ScriptType type = 8;                              // 脚本类型
  
  // 配置信息
  bool enabled = 9;                                 // 是否启用
  int32 priority = 10;                              // 优先级
  string config = 11;                               // 配置参数（JSON格式）
  
  // 执行统计
  int32 execution_count = 12;                       // 执行次数
  google.protobuf.Timestamp last_executed_at = 13; // 最后执行时间
  
  // 扩展字段
  string metadata = 14;                             // 元数据（JSON格式）
}

// 脚本类型枚举
enum ScriptType {
  SCRIPT_TYPE_UNSPECIFIED = 0;
  SCRIPT_TYPE_MESSAGE_HANDLER = 1;                  // 消息处理器
  SCRIPT_TYPE_BUSINESS_RULE = 2;                    // 业务规则
  SCRIPT_TYPE_PLUGIN = 3;                           // 插件
  SCRIPT_TYPE_FILTER = 4;                           // 过滤器
}

// ============================================================================
// API 请求和响应定义 (API Request/Response)
// ============================================================================

// 处理消息请求
// 思路：统一的消息处理请求格式
message ProcessMessageRequest {
  uint32 user_id = 1;                               // 用户ID
  string platform = 2;                              // 平台类型
  string platform_id = 3;                           // 平台用户ID
  string content = 4;                                // 消息内容
  string message_type = 5;                           // 消息类型
  string session_id = 6;                             // 会话ID（可选）
  map<string, string> metadata = 7;                 // 元数据
}

// 处理消息响应
// 思路：包含AI回复和相关统计信息
message ProcessMessageResponse {
  string reply = 1;                                  // AI回复内容
  string session_id = 2;                             // 会话ID
  int32 tokens_used = 3;                             // 使用的token数
  uint32 message_id = 4;                             // 消息ID
  uint32 reply_id = 5;                               // 回复消息ID
  bool is_from_kb = 6;                               // 是否来自知识库
  uint32 knowledge_id = 7;                           // 知识库条目ID
  map<string, string> metadata = 8;                 // 元数据
}

// 发送消息请求
message SendMessageRequest {
  string platform = 1;                              // 平台类型
  string platform_user_id = 2;                      // 平台用户ID
  string platform_chat_id = 3;                      // 平台聊天ID（群聊时使用）
  string content = 4;                                // 消息内容
  string message_type = 5;                           // 消息类型
  map<string, string> metadata = 6;                 // 元数据
}

// 发送消息响应
message SendMessageResponse {
  bool success = 1;                                  // 是否成功
  string message_id = 2;                             // 消息ID
  string error_message = 3;                          // 错误信息
  map<string, string> metadata = 4;                 // 元数据
}

// 通用API响应
// 思路：标准化的API响应格式
message ApiResponse {
  bool success = 1;                                  // 是否成功
  string message = 2;                                // 响应消息
  string error_code = 3;                             // 错误代码
  google.protobuf.Timestamp timestamp = 4;          // 响应时间
  string request_id = 5;                             // 请求ID
  map<string, string> metadata = 6;                 // 元数据
}

// 分页请求
message PaginationRequest {
  int32 page = 1;                                    // 页码（从1开始）
  int32 page_size = 2;                               // 每页大小
  string sort_by = 3;                                // 排序字段
  string sort_order = 4;                             // 排序方向：asc, desc
}

// 分页响应
message PaginationResponse {
  int32 page = 1;                                    // 当前页码
  int32 page_size = 2;                               // 每页大小
  int32 total_count = 3;                             // 总记录数
  int32 total_pages = 4;                             // 总页数
  bool has_next = 5;                                 // 是否有下一页
  bool has_prev = 6;                                 // 是否有上一页
}

// ============================================================================
// 平台相关定义 (Platform Related)
// ============================================================================

// 平台用户信息
// 思路：统一的用户信息格式
message PlatformUserInfo {
  string platform_user_id = 1;                      // 平台用户ID
  string platform = 2;                              // 平台名称
  string nickname = 3;                               // 用户昵称
  string avatar = 4;                                 // 用户头像
  bool is_bot = 5;                                   // 是否为机器人
  map<string, string> metadata = 6;                 // 元数据
}

// 接收到的消息
// 思路：统一的消息格式，适用于所有平台
message IncomingMessage {
  string id = 1;                                     // 消息ID
  string platform = 2;                              // 平台名称
  string content = 3;                                // 消息内容
  string message_type = 4;                           // 消息类型
  google.protobuf.Timestamp timestamp = 5;          // 时间戳
  PlatformUserInfo from = 6;                        // 发送者信息
  map<string, string> metadata = 7;                 // 元数据
}

// 发送的消息
// 思路：统一的发送消息格式
message OutgoingMessage {
  string platform = 1;                              // 平台名称
  string platform_user_id = 2;                      // 平台用户ID
  string platform_chat_id = 3;                      // 平台聊天ID
  string content = 4;                                // 消息内容
  string message_type = 5;                           // 消息类型
  map<string, string> metadata = 6;                 // 元数据
}

// ============================================================================
// WebSocket 消息定义 (WebSocket Messages)
// ============================================================================

// WebSocket消息
// 思路：统一的WebSocket消息格式
message WSMessage {
  string type = 1;                                   // 消息类型
  string data = 2;                                   // 消息数据（JSON格式）
  google.protobuf.Timestamp timestamp = 3;          // 时间戳
  string from = 4;                                   // 发送者
  string to = 5;                                     // 接收者
  map<string, string> metadata = 6;                 // 元数据
}

// ============================================================================
// 统计和监控定义 (Statistics and Monitoring)
// ============================================================================

// 系统统计信息
message SystemStats {
  int32 total_users = 1;                             // 总用户数
  int32 total_messages = 2;                          // 总消息数
  int32 total_sessions = 3;                          // 总会话数
  int32 active_sessions = 4;                         // 活跃会话数
  int32 total_knowledge = 5;                         // 总知识库条目数
  google.protobuf.Timestamp last_updated = 6;       // 最后更新时间
}

// 平台统计信息
message PlatformStats {
  string platform = 1;                              // 平台名称
  int32 user_count = 2;                              // 用户数
  int32 message_count = 3;                           // 消息数
  int32 session_count = 4;                           // 会话数
  google.protobuf.Timestamp last_updated = 5;       // 最后更新时间
}
