package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config 应用程序配置结构体
// 思路：集中管理所有配置项，支持环境变量和默认值
// 使用例子：cfg := &Config{Port: 8080}
type Config struct {
	// 服务器配置
	Port int    `json:"port"`           // HTTP服务器端口
	Host string `json:"host"`           // 服务器绑定地址
	
	// 数据库配置
	Database DatabaseConfig `json:"database"`
	
	// OpenAI配置
	OpenAI OpenAIConfig `json:"openai"`
	
	// 各平台配置
	Platforms PlatformsConfig `json:"platforms"`
}

// DatabaseConfig 数据库配置
// 思路：支持多种数据库类型，默认使用SQLite便于开发
type DatabaseConfig struct {
	Type     string `json:"type"`      // 数据库类型：sqlite, mysql, postgres
	Host     string `json:"host"`      // 数据库主机
	Port     int    `json:"port"`      // 数据库端口
	Username string `json:"username"`  // 用户名
	Password string `json:"password"`  // 密码
	Database string `json:"database"`  // 数据库名
	DSN      string `json:"dsn"`       // 完整连接字符串
}

// OpenAIConfig OpenAI API配置
// 思路：支持自定义API地址，兼容其他OpenAI兼容的服务
type OpenAIConfig struct {
	APIKey  string `json:"api_key"`   // API密钥
	BaseURL string `json:"base_url"`  // API基础URL
	Model   string `json:"model"`     // 默认模型
}

// PlatformsConfig 各平台配置
// 思路：每个平台独立配置，便于扩展和管理
type PlatformsConfig struct {
	QQ       QQConfig       `json:"qq"`
	WeChat   WeChatConfig   `json:"wechat"`
	Telegram TelegramConfig `json:"telegram"`
	Qianniu  QianniuConfig  `json:"qianniu"`
}

// QQConfig QQ平台配置（基于NapCat）
type QQConfig struct {
	Enabled bool   `json:"enabled"`   // 是否启用
	BaseURL string `json:"base_url"`  // NapCat API地址
	Token   string `json:"token"`     // 访问令牌
}

// WeChatConfig 微信平台配置
type WeChatConfig struct {
	Enabled bool   `json:"enabled"`
	AppID   string `json:"app_id"`
	Secret  string `json:"secret"`
}

// TelegramConfig Telegram平台配置
type TelegramConfig struct {
	Enabled bool   `json:"enabled"`
	Token   string `json:"token"`     // Bot Token
}

// QianniuConfig 千牛平台配置
type QianniuConfig struct {
	Enabled bool   `json:"enabled"`
	AppKey  string `json:"app_key"`
	Secret  string `json:"secret"`
}

// Load 加载配置
// 思路：优先使用环境变量，然后使用默认值
// 使用例子：cfg, err := config.Load()
func Load() (*Config, error) {
	cfg := &Config{
		Port: getEnvInt("PORT", 8080),
		Host: getEnvString("HOST", "0.0.0.0"),
		
		Database: DatabaseConfig{
			Type:     getEnvString("DB_TYPE", "sqlite"),
			Host:     getEnvString("DB_HOST", "localhost"),
			Port:     getEnvInt("DB_PORT", 3306),
			Username: getEnvString("DB_USERNAME", ""),
			Password: getEnvString("DB_PASSWORD", ""),
			Database: getEnvString("DB_DATABASE", "aike_go.db"),
		},
		
		OpenAI: OpenAIConfig{
			APIKey:  getEnvString("OPENAI_API_KEY", ""),
			BaseURL: getEnvString("OPENAI_BASE_URL", "https://api.openai.com/v1"),
			Model:   getEnvString("OPENAI_MODEL", "gpt-3.5-turbo"),
		},
		
		Platforms: PlatformsConfig{
			QQ: QQConfig{
				Enabled: getEnvBool("QQ_ENABLED", false),
				BaseURL: getEnvString("QQ_BASE_URL", "http://localhost:3000"),
				Token:   getEnvString("QQ_TOKEN", ""),
			},
			WeChat: WeChatConfig{
				Enabled: getEnvBool("WECHAT_ENABLED", false),
				AppID:   getEnvString("WECHAT_APP_ID", ""),
				Secret:  getEnvString("WECHAT_SECRET", ""),
			},
			Telegram: TelegramConfig{
				Enabled: getEnvBool("TELEGRAM_ENABLED", false),
				Token:   getEnvString("TELEGRAM_TOKEN", ""),
			},
			Qianniu: QianniuConfig{
				Enabled: getEnvBool("QIANNIU_ENABLED", false),
				AppKey:  getEnvString("QIANNIU_APP_KEY", ""),
				Secret:  getEnvString("QIANNIU_SECRET", ""),
			},
		},
	}
	
	// 构建数据库DSN
	if err := cfg.buildDatabaseDSN(); err != nil {
		return nil, fmt.Errorf("构建数据库连接字符串失败: %w", err)
	}
	
	return cfg, nil
}

// buildDatabaseDSN 构建数据库连接字符串
// 思路：根据数据库类型生成对应的DSN格式
func (c *Config) buildDatabaseDSN() error {
	switch c.Database.Type {
	case "sqlite":
		c.Database.DSN = c.Database.Database
	case "mysql":
		c.Database.DSN = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Database.Username, c.Database.Password, c.Database.Host, c.Database.Port, c.Database.Database)
	case "postgres":
		c.Database.DSN = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			c.Database.Host, c.Database.Port, c.Database.Username, c.Database.Password, c.Database.Database)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", c.Database.Type)
	}
	return nil
}

// 辅助函数：获取环境变量字符串值
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 辅助函数：获取环境变量整数值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// 辅助函数：获取环境变量布尔值
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
