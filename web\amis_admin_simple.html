<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8" />
	<title>amis admin</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
	<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
	<link rel="stylesheet" title="default" href="https://unpkg.com/amis@3.2.0/sdk/sdk.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@3.2.0/sdk/helper.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@3.2.0/sdk/iconfont.css" />
	<script src="https://unpkg.com/amis@3.2.0/sdk/sdk.js"></script>
	<script src="https://unpkg.com/vue@2"></script>
	<script src="https://unpkg.com/history@4.10.1
/umd/history.js"></script>
	<style>
		html,
		body,
		.app-wrapper {
			position: relative;
			width: 100%;
			height: 100%;
			margin: 0;
			padding: 0;
		}
	</style>
</head>

<body>
	<div id="root" class="app-wrapper"></div>
	<script>
		(function () {
			let amis = amisRequire('amis/embed');
			const match = amisRequire('path-to-regexp').match;

			// 如果想用 browserHistory 请切换下这处代码, 其他不用变
			// const history = History.createBrowserHistory();
			const history = History.createHashHistory();

			const app = {
				type: 'app',
				brandName: 'Admin',
				logo: '/public/logo.png',
				header: {
					type: 'tpl',
					inline: false,
					className: 'w-full',
					tpl: '<div class="flex justify-between"><div>顶部区域左侧</div><div>顶部区域右侧</div></div>'
				},
				// footer: '<div class="p-2 text-center bg-light">底部区域</div>',
				// asideBefore: '<div class="p-2 text-center">菜单前面区域</div>',
				// asideAfter: '<div class="p-2 text-center">菜单后面区域</div>',
				api: '/api/v1/admin/amis-config'
			};

			function normalizeLink(to, location = history.location) {
				to = to || '';

				if (to && to[0] === '#') {
					to = location.pathname + location.search + to;
				} else if (to && to[0] === '?') {
					to = location.pathname + to;
				}

				const idx = to.indexOf('?');
				const idx2 = to.indexOf('#');
				let pathname = ~idx
					? to.substring(0, idx)
					: ~idx2
						? to.substring(0, idx2)
						: to;
				let search = ~idx ? to.substring(idx, ~idx2 ? idx2 : undefined) : '';
				let hash = ~idx2 ? to.substring(idx2) : location.hash;

				if (!pathname) {
					pathname = location.pathname;
				} else if (pathname[0] != '/' && !/^https?\:\/\//.test(pathname)) {
					let relativeBase = location.pathname;
					const paths = relativeBase.split('/');
					paths.pop();
					let m;
					while ((m = /^\.\.?\//.exec(pathname))) {
						if (m[0] === '../') {
							paths.pop();
						}
						pathname = pathname.substring(m[0].length);
					}
					pathname = paths.concat(pathname).join('/');
				}

				return pathname + search + hash;
			}

			function isCurrentUrl(to, ctx) {
				if (!to) {
					return false;
				}
				const pathname = history.location.pathname;
				const link = normalizeLink(to, {
					...location,
					pathname,
					hash: ''
				});

				if (!~link.indexOf('http') && ~link.indexOf(':')) {
					let strict = ctx && ctx.strict;
					return match(link, {
						decode: decodeURIComponent,
						strict: typeof strict !== 'undefined' ? strict : true
					})(pathname);
				}

				return decodeURI(pathname) === link;
			}

			let amisInstance = amis.embed(
				'#root',
				app,
				{
					location: history.location,
					data: {
						// 全局数据，是受控的数据
					},
					context: {
						// 全局上下文数据, 非受控的数据，无论哪一层都能获取到，包括弹窗自定义数据映射后都能获取到。
						// 可以用来放一下全局配置等。比如 API_HOST, 这样页面配置里面可以通过 ${API_HOST} 来获取到。
						API_HOST: 'https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com'
					}
				},
				{
					// watchRouteChange: fn => {
					//   return history.listen(fn);
					// },
					updateLocation: (location, replace) => {
						location = normalizeLink(location);
						if (location === 'goBack') {
							return history.goBack();
						} else if (
							(!/^https?\:\/\//.test(location) &&
								location ===
								history.location.pathname + history.location.search) ||
							location === history.location.href
						) {
							// 目标地址和当前地址一样，不处理，免得重复刷新
							return;
						} else if (/^https?\:\/\//.test(location) || !history) {
							return (window.location.href = location);
						}

						history[replace ? 'replace' : 'push'](location);
					},
					jumpTo: (to, action) => {
						if (to === 'goBack') {
							return history.goBack();
						}

						to = normalizeLink(to);

						if (isCurrentUrl(to)) {
							return;
						}

						if (action && action.actionType === 'url') {
							action.blank === false
								? (window.location.href = to)
								: window.open(to, '_blank');
							return;
						} else if (action && action.blank) {
							window.open(to, '_blank');
							return;
						}

						if (/^https?:\/\//.test(to)) {
							window.location.href = to;
						} else if (
							(!/^https?\:\/\//.test(to) &&
								to === history.pathname + history.location.search) ||
							to === history.location.href
						) {
							// do nothing
						} else {
							history.push(to);
						}
					},
					isCurrentUrl: isCurrentUrl,
					theme: 'cxd'
				}
			);

			history.listen(state => {
				amisInstance.updateProps({
					location: state.location || state
				});
			});
		})();
	</script>
</body>

</html>
