package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

const baseURL = "http://localhost:8082/api/v1/scripts"

type Script struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	Enabled     bool      `json:"enabled"`
	Priority    int       `json:"priority"`
	CreatedAt   time.Time `json:"created_at"`
}

type ScriptListResponse struct {
	Scripts []Script `json:"scripts"`
	Total   int      `json:"total"`
}

func main() {
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "list", "ls":
		listScripts()
	case "show":
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供脚本ID")
			return
		}
		showScript(os.Args[2])
	case "delete", "rm":
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供脚本ID")
			return
		}
		deleteScript(os.Args[2])
	case "enable":
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供脚本ID")
			return
		}
		toggleScript(os.Args[2], true)
	case "disable":
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供脚本ID")
			return
		}
		toggleScript(os.Args[2], false)
	case "types":
		showTypes()
	default:
		fmt.Printf("❌ 未知命令: %s\n", command)
		showUsage()
	}
}

func showUsage() {
	fmt.Println("🔧 Lua脚本管理工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/script_manager/main.go <命令> [参数]")
	fmt.Println()
	fmt.Println("命令:")
	fmt.Println("  list, ls           - 列出所有脚本")
	fmt.Println("  show <id>          - 显示脚本详情")
	fmt.Println("  delete, rm <id>    - 删除脚本")
	fmt.Println("  enable <id>        - 启用脚本")
	fmt.Println("  disable <id>       - 禁用脚本")
	fmt.Println("  types              - 显示脚本类型")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run cmd/script_manager/main.go list")
	fmt.Println("  go run cmd/script_manager/main.go show script_123456")
}

func listScripts() {
	resp, err := http.Get(baseURL + "/")
	if err != nil {
		log.Fatalf("❌ 请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("❌ 读取响应失败: %v", err)
	}

	var response ScriptListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		log.Fatalf("❌ 解析响应失败: %v", err)
	}

	fmt.Printf("📋 脚本列表 (总计: %d)\n", response.Total)
	fmt.Println(strings.Repeat("=", 80))

	for _, script := range response.Scripts {
		status := "🔴 禁用"
		if script.Enabled {
			status = "🟢 启用"
		}

		typeIcon := getTypeIcon(script.Type)
		
		fmt.Printf("%s %s [%s] %s\n", typeIcon, script.Name, script.Type, status)
		fmt.Printf("   ID: %s\n", script.ID)
		fmt.Printf("   描述: %s\n", script.Description)
		fmt.Printf("   优先级: %d | 创建时间: %s\n", script.Priority, script.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println()
	}
}

func showScript(id string) {
	resp, err := http.Get(baseURL + "/" + id)
	if err != nil {
		log.Fatalf("❌ 请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		fmt.Printf("❌ 脚本不存在: %s\n", id)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("❌ 读取响应失败: %v", err)
	}

	var script map[string]interface{}
	if err := json.Unmarshal(body, &script); err != nil {
		log.Fatalf("❌ 解析响应失败: %v", err)
	}

	fmt.Printf("📄 脚本详情: %s\n", script["name"])
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("ID: %s\n", script["id"])
	fmt.Printf("类型: %s\n", script["type"])
	fmt.Printf("状态: %v\n", script["enabled"])
	fmt.Printf("优先级: %.0f\n", script["priority"])
	fmt.Printf("描述: %s\n", script["description"])
	fmt.Println("\n📝 脚本内容:")
	fmt.Println(strings.Repeat("-", 50))
	fmt.Println(script["content"])
}

func deleteScript(id string) {
	client := &http.Client{}
	req, err := http.NewRequest("DELETE", baseURL+"/"+id, nil)
	if err != nil {
		log.Fatalf("❌ 创建请求失败: %v", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("❌ 请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		fmt.Printf("✅ 脚本删除成功: %s\n", id)
	} else {
		fmt.Printf("❌ 脚本删除失败: %s (状态码: %d)\n", id, resp.StatusCode)
	}
}

func toggleScript(id string, enabled bool) {
	action := "禁用"
	if enabled {
		action = "启用"
	}

	// 这里需要实现更新脚本的API调用
	fmt.Printf("⚠️  %s脚本功能暂未实现: %s\n", action, id)
	fmt.Println("💡 提示: 可以通过删除并重新创建脚本来改变状态")
}

func showTypes() {
	resp, err := http.Get(baseURL + "/types")
	if err != nil {
		log.Fatalf("❌ 请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("❌ 读取响应失败: %v", err)
	}

	var response map[string]map[string]string
	if err := json.Unmarshal(body, &response); err != nil {
		log.Fatalf("❌ 解析响应失败: %v", err)
	}

	fmt.Println("📚 脚本类型说明")
	fmt.Println(strings.Repeat("=", 50))

	for typeKey, description := range response["types"] {
		icon := getTypeIcon(typeKey)
		fmt.Printf("%s %s\n", icon, description)
	}
}

func getTypeIcon(scriptType string) string {
	switch scriptType {
	case "message_filter":
		return "🔍"
	case "message_handler":
		return "💬"
	case "business_rule":
		return "⚖️"
	default:
		return "📄"
	}
}
