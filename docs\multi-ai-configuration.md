# 多AI配置指南

## 概述

本指南介绍如何为不同平台、群聊、私聊配置不同的AI API，实现灵活的多AI服务支持。

## 功能特性

- ✅ **平台级配置**：为QQ、微信、Telegram等不同平台配置不同的AI服务
- ✅ **聊天类型配置**：为私聊和群聊配置不同的AI模型
- ✅ **群聊级配置**：为特定群聊配置专用的AI服务
- ✅ **用户级配置**：为VIP用户配置高级AI服务
- ✅ **动态配置**：支持运行时修改配置，无需重启服务
- ✅ **配置优先级**：VIP用户 > 特殊群聊 > 平台配置 > 默认配置

## 配置架构

### 1. 配置层级

```
默认配置
├── 平台配置
│   ├── QQ
│   │   ├── 私聊配置
│   │   └── 群聊配置
│   ├── Telegram
│   │   ├── 私聊配置
│   │   └── 群聊配置
│   └── 微信
│       ├── 私聊配置
│       └── 群聊配置
├── 特殊群聊配置
│   ├── 群聊ID: 123456789
│   └── 群聊ID: 987654321
└── VIP用户配置
    ├── 用户ID: vip_user_1
    └── 用户ID: admin_user
```

### 2. 配置参数

每个配置包含以下参数：

| 参数 | 说明 | 示例 |
|------|------|------|
| `api_key` | API密钥 | `sk-xxx...` |
| `base_url` | API地址 | `https://api.siliconflow.cn/v1` |
| `model` | 模型名称 | `Qwen/Qwen2.5-72B-Instruct` |
| `temperature` | 随机性参数 | `0.7` |
| `max_tokens` | 最大token数 | `500` |

## 使用方法

### 1. 启用多AI客服脚本

将 `scripts/handlers/multi_ai_customer_service.lua` 脚本添加到系统中：

```bash
# 方法1：通过数据库添加
INSERT INTO scripts (name, content, type, enabled) VALUES 
('多AI客服', '脚本内容...', 'handler', 1);

# 方法2：通过文件系统（推荐）
# 将脚本文件放在 scripts/handlers/ 目录下
```

### 2. 配置AI服务

编辑 `scripts/config/ai_config_manager.lua` 文件，修改配置：

```lua
-- 平台配置示例
platforms = {
    qq = {
        private = {
            api_key = "sk-your_siliconflow_key_here",
            base_url = "https://api.siliconflow.cn/v1",
            model = "Qwen/Qwen2.5-72B-Instruct",
            temperature = 0.7,
            max_tokens = 500
        },
        group = {
            api_key = "sk-your_siliconflow_key_here",
            base_url = "https://api.siliconflow.cn/v1",
            model = "meta-llama/Llama-3.1-8B-Instruct",
            temperature = 0.7,
            max_tokens = 200
        }
    }
}
```

### 3. 配置示例

#### QQ平台配置

```lua
-- QQ私聊使用硅基流动高性能模型
qq = {
    private = {
        api_key = "sk-siliconflow-key",
        base_url = "https://api.siliconflow.cn/v1",
        model = "Qwen/Qwen2.5-72B-Instruct",
        temperature = 0.7,
        max_tokens = 500
    },
    -- QQ群聊使用轻量模型节省成本
    group = {
        api_key = "sk-siliconflow-key",
        base_url = "https://api.siliconflow.cn/v1",
        model = "meta-llama/Llama-3.1-8B-Instruct",
        temperature = 0.7,
        max_tokens = 200
    }
}
```

#### Telegram配置

```lua
-- Telegram使用OpenAI
telegram = {
    private = {
        api_key = "sk-openai-key",
        base_url = "https://api.openai.com/v1",
        model = "gpt-4",
        temperature = 0.8,
        max_tokens = 600
    },
    group = {
        api_key = "sk-openai-key",
        base_url = "https://api.openai.com/v1",
        model = "gpt-3.5-turbo",
        temperature = 0.7,
        max_tokens = 400
    }
}
```

#### 特殊群聊配置

```lua
-- 为特定群聊配置专用AI服务
groups = {
    ["123456789"] = {
        api_key = "sk-special-key",
        base_url = "https://api.siliconflow.cn/v1",
        model = "Qwen/Qwen2.5-72B-Instruct",
        temperature = 0.3,
        max_tokens = 300
    }
}
```

#### VIP用户配置

```lua
-- VIP用户使用高级模型
vip_users = {
    ["vip_user_1"] = {
        api_key = "sk-premium-key",
        base_url = "https://api.openai.com/v1",
        model = "gpt-4-turbo",
        temperature = 0.7,
        max_tokens = 1000
    }
}
```

## 动态配置管理

### 配置命令

通过聊天命令动态管理配置：

```bash
# 列出所有配置
/config list

# 测试配置
/config test qq private
/config test telegram group

# 切换模型
/config switch qq group meta-llama/Llama-3.1-8B-Instruct
/config switch telegram private gpt-4-turbo
```

### API调用示例

在Lua脚本中调用多AI服务：

```lua
-- 使用自定义配置调用AI
local response = call_openai({
    model = "Qwen/Qwen2.5-72B-Instruct",
    messages = messages,
    max_tokens = 500,
    temperature = 0.7,
    api_key = "sk-your-key",
    base_url = "https://api.siliconflow.cn/v1"
})
```

## 最佳实践

### 1. 成本优化

- **群聊使用轻量模型**：群聊消息量大，使用8B模型节省成本
- **私聊使用高性能模型**：私聊更重要，使用72B模型提升体验
- **设置合理的max_tokens**：避免过长回复浪费token

### 2. 性能优化

- **就近选择API**：国内用户使用硅基流动，海外用户使用OpenAI
- **模型选择**：根据场景选择合适的模型
- **缓存策略**：对常见问题启用缓存

### 3. 安全配置

- **API密钥管理**：不同服务使用不同密钥
- **访问控制**：VIP用户使用高级服务
- **监控告警**：监控API调用量和费用

## 支持的AI服务

### 硅基流动

```lua
{
    api_key = "sk-xxx",
    base_url = "https://api.siliconflow.cn/v1",
    model = "Qwen/Qwen2.5-72B-Instruct"
}
```

**推荐模型**：
- `Qwen/Qwen2.5-72B-Instruct` - 高性能中文模型
- `meta-llama/Llama-3.1-8B-Instruct` - 轻量快速模型
- `deepseek-ai/DeepSeek-V3` - 推理能力强

### OpenAI

```lua
{
    api_key = "sk-xxx",
    base_url = "https://api.openai.com/v1",
    model = "gpt-4"
}
```

**推荐模型**：
- `gpt-4-turbo` - 最新高性能模型
- `gpt-4` - 平衡性能和成本
- `gpt-3.5-turbo` - 经济实用模型

### DeepSeek

```lua
{
    api_key = "sk-xxx",
    base_url = "https://api.deepseek.com/v1",
    model = "deepseek-chat"
}
```

## 故障排除

### 常见问题

1. **配置不生效**
   - 检查脚本是否正确加载
   - 验证配置文件语法
   - 重启服务

2. **API调用失败**
   - 检查API密钥是否正确
   - 验证网络连接
   - 确认模型名称

3. **配置冲突**
   - 检查配置优先级
   - 验证配置参数
   - 查看日志输出

### 调试方法

1. **查看日志**
   ```bash
   tail -f logs/server.log | grep "多AI"
   ```

2. **测试配置**
   ```bash
   /config test qq private
   ```

3. **验证API**
   ```bash
   curl -X POST https://api.siliconflow.cn/v1/chat/completions \
        -H "Authorization: Bearer your_key" \
        -H "Content-Type: application/json" \
        -d '{"model": "Qwen/Qwen2.5-72B-Instruct", "messages": [{"role": "user", "content": "test"}]}'
   ```

## 扩展开发

### 添加新的AI服务

1. 在配置管理器中添加新的配置模板
2. 确保API兼容OpenAI格式
3. 测试API连接和响应

### 自定义配置逻辑

1. 修改 `selectAIConfig` 函数
2. 添加新的配置条件
3. 实现配置验证逻辑

## 相关文档

- [硅基流动集成指南](siliconflow-integration.md)
- [OpenAI API文档](https://platform.openai.com/docs)
- [DeepSeek API文档](https://platform.deepseek.com/docs)
