<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI客服系统</title>
	<style>
		body {
			margin: 0;
			font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			min-height: 100vh;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.container {
			text-align: center;
			color: white;
			max-width: 600px;
			padding: 40px;
		}

		.logo {
			font-size: 48px;
			font-weight: bold;
			margin-bottom: 20px;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
		}

		.subtitle {
			font-size: 20px;
			margin-bottom: 40px;
			opacity: 0.9;
		}

		.features {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: 20px;
			margin-bottom: 40px;
		}

		.feature-card {
			background: rgba(255, 255, 255, 0.1);
			backdrop-filter: blur(10px);
			border-radius: 12px;
			padding: 20px;
			border: 1px solid rgba(255, 255, 255, 0.2);
		}

		.feature-icon {
			font-size: 32px;
			margin-bottom: 10px;
		}

		.feature-title {
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 8px;
		}

		.feature-desc {
			font-size: 14px;
			opacity: 0.8;
		}

		.actions {
			display: flex;
			gap: 20px;
			justify-content: center;
			flex-wrap: wrap;
		}

		.btn {
			display: inline-block;
			padding: 12px 24px;
			background: rgba(255, 255, 255, 0.2);
			color: white;
			text-decoration: none;
			border-radius: 8px;
			border: 1px solid rgba(255, 255, 255, 0.3);
			transition: all 0.3s ease;
			font-weight: 500;
		}

		.btn:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
		}

		.btn-primary {
			background: #409eff;
			border-color: #409eff;
		}

		.btn-primary:hover {
			background: #66b1ff;
			border-color: #66b1ff;
		}

		.status {
			margin-top: 40px;
			padding: 20px;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 8px;
			backdrop-filter: blur(10px);
		}

		.status-item {
			display: inline-block;
			margin: 0 20px;
			text-align: center;
		}

		.status-value {
			font-size: 24px;
			font-weight: bold;
			display: block;
		}

		.status-label {
			font-size: 12px;
			opacity: 0.8;
		}

		@media (max-width: 768px) {
			.container {
				padding: 20px;
			}

			.logo {
				font-size: 36px;
			}

			.subtitle {
				font-size: 16px;
			}

			.actions {
				flex-direction: column;
				align-items: center;
			}

			.btn {
				width: 200px;
			}
		}
	</style>
</head>

<body>
	<div class="container">
		<div class="logo">🤖 AI客服系统</div>
		<div class="subtitle">智能客服解决方案，支持多平台集成</div>

		<div class="features">
			<div class="feature-card">
				<div class="feature-icon">💬</div>
				<div class="feature-title">多平台支持</div>
				<div class="feature-desc">支持QQ、微信、Telegram等多个平台的消息处理</div>
			</div>
			<div class="feature-card">
				<div class="feature-icon">🧠</div>
				<div class="feature-title">智能回复</div>
				<div class="feature-desc">基于OpenAI的智能对话和知识库检索</div>
			</div>
			<div class="feature-card">
				<div class="feature-icon">⚙️</div>
				<div class="feature-title">脚本扩展</div>
				<div class="feature-desc">支持Lua脚本自定义业务逻辑和消息处理</div>
			</div>
			<div class="feature-card">
				<div class="feature-icon">📊</div>
				<div class="feature-title">实时监控</div>
				<div class="feature-desc">完整的系统监控和数据统计分析</div>
			</div>
		</div>

		<div class="actions">
			<a href="/amis" class="btn btn-primary">Amis管理后台</a>
			<a href="/admin" class="btn">Vue管理后台</a>
			<a href="/static/websocket_test.html" class="btn">WebSocket测试</a>
			<a href="/health" class="btn">系统状态</a>
			<a href="/api/v1/monitor/health/detailed" class="btn">详细健康检查</a>
		</div>

		<div class="status" id="systemStatus">
			<div class="status-item">
				<span class="status-value" id="uptime">加载中...</span>
				<span class="status-label">运行时间</span>
			</div>
			<div class="status-item">
				<span class="status-value" id="requests">加载中...</span>
				<span class="status-label">总请求数</span>
			</div>
			<div class="status-item">
				<span class="status-value" id="status">加载中...</span>
				<span class="status-label">系统状态</span>
			</div>
		</div>
	</div>

	<script>
		// 加载系统状态
		async function loadSystemStatus() {
			try {
				const response = await fetch('/api/v1/monitor/health/detailed');
				const data = await response.json();

				if (data.success) {
					const health = data.data;
					document.getElementById('uptime').textContent = health.uptime || 'N/A';
					document.getElementById('requests').textContent = health.metrics?.total_requests || 0;
					document.getElementById('status').textContent = health.status === 'healthy' ? '健康' : '异常';

					// 根据状态设置颜色
					const statusElement = document.getElementById('status');
					if (health.status === 'healthy') {
						statusElement.style.color = '#67C23A';
					} else {
						statusElement.style.color = '#F56C6C';
					}
				} else {
					throw new Error('获取状态失败');
				}
			} catch (error) {
				console.error('加载系统状态失败:', error);
				document.getElementById('uptime').textContent = 'N/A';
				document.getElementById('requests').textContent = 'N/A';
				document.getElementById('status').textContent = '未知';
			}
		}

		// 页面加载时获取系统状态
		document.addEventListener('DOMContentLoaded', loadSystemStatus);

		// 每30秒刷新一次状态
		setInterval(loadSystemStatus, 30000);
	</script>
</body>

</html>
