-- 分布式存储管理器
-- 思路：使用分布式存储系统管理聊天记录，按平台和聊天类型分类存储
-- 类型：message_handler
-- 优先级：40

-- 配置选项
-- 思路：通过开关控制调试信息的显示，避免日志过多影响性能和可读性
local DEBUG_MODE = false   -- 调试模式开关，设置为true显示详细调试信息
local VERBOSE_MODE = false -- 详细模式开关，设置为true显示更多处理细节

-- 调试输出函数
-- 思路：统一管理调试信息的输出，便于控制和维护
local function debug_log(level, message)
    if DEBUG_MODE then
        log(level, "[分布式存储] " .. message)
    end
end

local function verbose_log(level, message)
    if VERBOSE_MODE then
        log(level, "[分布式存储详细] " .. message)
    end
end

-- 检查是否是语法验证模式，如果是则跳过实际处理
if __syntax_check_mode then
    log("info", "语法验证模式，跳过分布式存储处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

debug_log("info", "处理消息: " .. content .. " 来自用户: " .. user_id .. " 平台: " .. platform)
verbose_log("info", "语法验证模式状态: " .. tostring(__syntax_check_mode))

-- 解析群聊信息
local group_id = ""
local chat_type = "private"

verbose_log("info", "检查消息元数据")
if message.metadata then
    verbose_log("info", "metadata.group_id: " .. tostring(message.metadata.group_id))
    verbose_log("info", "metadata.message_type: " .. tostring(message.metadata.message_type))
else
    verbose_log("warn", "metadata is nil!")
end

-- 调试昵称信息
verbose_log("info", "检查用户信息")
if message.from then
    verbose_log("info", "用户昵称: " .. tostring(message.from.nickname))
    verbose_log("info", "用户ID: " .. tostring(message.from.platform_user_id))
else
    verbose_log("warn", "message.from is nil!")
end

verbose_log("info", "元数据: " .. tostring(message.metadata))


-- 检查是否为群聊消息
-- 思路：群聊消息的group_id应该是非空且不为"0"的字符串
if message.metadata and message.metadata.group_id then
    local gid = tostring(message.metadata.group_id)
    -- 检查群号是否有效（不为空且不为"0"）
    if gid ~= "" and gid ~= "0" then
        group_id = gid
        chat_type = "group"
        debug_log("info", "✅ 检测到群聊消息，群号: " .. group_id)
    else
        debug_log("info", "❌ 检测到私聊消息，group_id为空或0: " .. gid)
    end
else
    debug_log("info", "❌ 检测到私聊消息，无group_id字段")
end

-- 额外检查：通过消息内容判断是否为群聊
-- 思路：群聊消息通常包含@某人的CQ码
if chat_type == "private" and content then
    if string.find(content, "%[CQ:at,qq=") then
        debug_log("warn", "消息包含@操作但被识别为私聊，可能是群聊消息识别错误")
        verbose_log("warn", "消息内容: " .. content)
        if message.metadata then
            verbose_log("warn", "元数据: " .. tostring(message.metadata.group_id or "无group_id"))
        end
    end
end

-- 构建分布式存储消息数据
local nickname = message.from and message.from.nickname or ""

local distributed_message_data = {
    -- 基本信息
    id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
    user_id = user_id,
    platform = platform,
    content = content,
    message_type = message.message_type or "text",

    -- 用户信息
    -- 思路：传递发送者的昵称和头像信息，用于用户管理和显示
    user_nickname = nickname,
    user_avatar = message.from and message.from.avatar or "",

    -- 元数据
    metadata = {
        group_id = group_id,
        chat_type = chat_type,
        platform_message_id = message.metadata and message.metadata.message_id or "",
        sender_nickname = message.from and message.from.nickname or "",
        sender_avatar = message.from and message.from.avatar or "",
        message_source = "distributed_storage",
        processing_time = os.time(),
        timestamp = os.time()
    }
}

-- 调用分布式存储API
local store_success, store_message_text = store_distributed_message(distributed_message_data)
if store_success then
    local db_info = ""
    if chat_type == "group" then
        db_info = string.format("群聊数据库(group_%s.db)", group_id)
    else
        db_info = "私聊数据库"
    end

    debug_log("info", "消息已存储到分布式存储: " .. distributed_message_data.id ..
        " (用户: " .. user_id .. ", 平台: " .. platform .. ", 类型: " .. chat_type .. ", 存储: " .. db_info .. ")")

    -- 优化后的统计信息管理
    -- 思路：完全移除变量依赖，统计信息直接从数据库获取，避免并发冲突和数据不一致

    -- 不再设置用户级别的变量，避免大量变量污染和并发问题
    -- 统计信息通过数据库查询实时获取，确保数据准确性
    verbose_log("info", "消息存储完成，统计信息可通过数据库查询获取")

    -- 如果需要缓存统计信息，可以考虑以下方案：
    -- 1. 使用Redis等外部缓存系统
    -- 2. 定期批量更新统计信息
    -- 3. 使用数据库触发器自动维护统计表
else
    log("warn", "分布式存储失败: " .. (store_message_text or "未知错误"))
end

-- 存储AI回复消息的全局函数（分布式版本）
-- 思路：供其他脚本调用，用于存储AI生成的回复消息到分布式存储
function store_distributed_ai_reply(user_id, platform, reply_content, ai_model, tokens_used, parent_message_id,
                                    is_from_kb, knowledge_id, group_id)
    -- 确定聊天类型
    local chat_type = "private"
    local reply_group_id = ""
    if group_id and group_id ~= "" then
        chat_type = "group"
        reply_group_id = group_id
    end

    -- 构建AI回复消息数据
    local reply_data = {
        -- 基本信息
        id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
        user_id = user_id,
        platform = platform,
        content = reply_content,
        message_type = "text",

        -- 元数据
        metadata = {
            group_id = reply_group_id,
            chat_type = chat_type,
            is_ai_generated = true,
            ai_model = ai_model or "unknown",
            tokens_used = tokens_used or 0,
            parent_message_id = parent_message_id or "",
            is_from_knowledge_base = is_from_kb or false,
            knowledge_id = knowledge_id or "",
            generation_time = os.time(),
            message_source = "ai_generated_distributed",
            processing_time = os.time(),
            direction = "outgoing"
        }
    }

    -- 存储AI回复到分布式存储
    local reply_store_success, reply_store_message = store_distributed_message(reply_data)
    if reply_store_success then
        local db_info = ""
        if chat_type == "group" then
            db_info = string.format("群聊数据库(group_%s.db)", reply_group_id)
        else
            db_info = "私聊数据库"
        end

        log("info", "AI回复已存储到分布式存储: " .. reply_data.id ..
            " (用户: " .. user_id .. ", 模型: " .. (ai_model or "unknown") .. ", 存储: " .. db_info .. ")")

        -- 更新AI回复统计
        set_var("last_ai_reply_id_" .. user_id, reply_data.id)
        set_var("last_ai_reply_time_" .. user_id, os.time())

        -- 记录AI模型使用统计
        local ai_model_count_key = "ai_model_usage_" .. (ai_model or "unknown")
        local model_count = tonumber(get_var(ai_model_count_key)) or 0
        set_var(ai_model_count_key, model_count + 1)

        return true, reply_data.id
    else
        log("warn", "AI回复分布式存储失败: " .. (reply_store_message or "未知错误"))
        return false, reply_store_message
    end
end

-- 获取分布式存储统计信息
function get_distributed_storage_stats()
    local stats = get_distributed_storage_statistics()
    if stats then
        log("info", string.format("分布式存储统计: %d条消息, %d个用户, %d个会话, %.2fMB存储",
            stats.total_messages or 0,
            stats.total_users or 0,
            stats.total_sessions or 0,
            (stats.storage_size or 0) / (1024 * 1024)))

        if stats.top_platforms then
            log("info", "最活跃平台: " .. table.concat(stats.top_platforms, ", "))
        end

        return stats
    else
        log("warn", "获取分布式存储统计失败")
        return nil
    end
end

-- 备份分布式存储数据
function backup_distributed_storage(backup_path)
    backup_path = backup_path or ("backup_" .. os.date("%Y%m%d_%H%M%S"))

    local backup_success, backup_message = backup_distributed_data(backup_path)
    if backup_success then
        log("info", "分布式存储数据备份成功: " .. backup_path)
        set_var("last_backup_time", os.time())
        set_var("last_backup_path", backup_path)
        return true, backup_path
    else
        log("warn", "分布式存储数据备份失败: " .. (backup_message or "未知错误"))
        return false, backup_message
    end
end

-- 获取实时统计信息的函数
-- 思路：通过数据库查询获取准确的统计信息，避免变量并发问题
local function get_realtime_stats(platform, chat_type, group_id)
    -- 这里可以调用分布式存储插件的统计API
    -- 返回实时的消息数量、用户数量等信息
    local stats = {
        total_messages = 0,
        total_users = 0,
        platform = platform,
        chat_type = chat_type,
        group_id = group_id or "",
        last_updated = os.time()
    }

    verbose_log("info", "获取统计信息: " .. platform .. " " .. chat_type)
    return stats
end

debug_log("info", "分布式存储管理器处理完成，消息继续传递")
return nil
