package middleware

import (
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// MetricsCollector 指标收集器
// 思路：收集和统计HTTP请求的各种指标
type MetricsCollector struct {
	mutex           sync.RWMutex
	requestCount    map[string]int64           // 请求总数
	requestDuration map[string][]time.Duration // 请求耗时
	statusCount     map[int]int64              // 状态码统计
	pathCount       map[string]int64           // 路径统计
	methodCount     map[string]int64           // 方法统计
	errorCount      map[string]int64           // 错误统计
	activeRequests  int64                      // 活跃请求数
	startTime       time.Time                  // 启动时间
}

// Metrics 指标数据
// 思路：提供指标查询的数据结构
type Metrics struct {
	// 基础指标
	TotalRequests  int64         `json:"total_requests"`  // 总请求数
	ActiveRequests int64         `json:"active_requests"` // 活跃请求数
	TotalErrors    int64         `json:"total_errors"`    // 总错误数
	ErrorRate      float64       `json:"error_rate"`      // 错误率
	Uptime         time.Duration `json:"uptime"`          // 运行时间

	// 性能指标
	AvgResponseTime time.Duration `json:"avg_response_time"` // 平均响应时间
	P95ResponseTime time.Duration `json:"p95_response_time"` // 95分位响应时间
	P99ResponseTime time.Duration `json:"p99_response_time"` // 99分位响应时间

	// 分类统计
	StatusCodes map[string]int64 `json:"status_codes"` // 状态码统计
	Methods     map[string]int64 `json:"methods"`      // 方法统计
	Paths       map[string]int64 `json:"paths"`        // 路径统计
	Errors      map[string]int64 `json:"errors"`       // 错误统计

	// 时间信息
	LastUpdated time.Time `json:"last_updated"` // 最后更新时间
}

// PathMetrics 路径指标
// 思路：单个路径的详细指标
type PathMetrics struct {
	Path            string        `json:"path"`              // 路径
	RequestCount    int64         `json:"request_count"`     // 请求数
	ErrorCount      int64         `json:"error_count"`       // 错误数
	ErrorRate       float64       `json:"error_rate"`        // 错误率
	AvgResponseTime time.Duration `json:"avg_response_time"` // 平均响应时间
	MinResponseTime time.Duration `json:"min_response_time"` // 最小响应时间
	MaxResponseTime time.Duration `json:"max_response_time"` // 最大响应时间
}

// NewMetricsCollector 创建指标收集器
// 思路：初始化指标收集器
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		requestCount:    make(map[string]int64),
		requestDuration: make(map[string][]time.Duration),
		statusCount:     make(map[int]int64),
		pathCount:       make(map[string]int64),
		methodCount:     make(map[string]int64),
		errorCount:      make(map[string]int64),
		startTime:       time.Now(),
	}
}

// 全局指标收集器实例
var globalMetrics = NewMetricsCollector()

// MetricsMiddleware 指标收集中间件
// 思路：收集每个HTTP请求的指标数据
// 使用例子：router.Use(middleware.MetricsMiddleware())
func MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// 增加活跃请求数
		globalMetrics.incrementActiveRequests()

		// 处理请求
		c.Next()

		// 计算耗时
		duration := time.Since(start)

		// 收集指标
		globalMetrics.recordRequest(c, duration)

		// 减少活跃请求数
		globalMetrics.decrementActiveRequests()
	}
}

// incrementActiveRequests 增加活跃请求数
func (m *MetricsCollector) incrementActiveRequests() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.activeRequests++
}

// decrementActiveRequests 减少活跃请求数
func (m *MetricsCollector) decrementActiveRequests() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.activeRequests--
}

// recordRequest 记录请求指标
// 思路：记录单个请求的各种指标
func (m *MetricsCollector) recordRequest(c *gin.Context, duration time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	path := c.Request.URL.Path
	method := c.Request.Method
	statusCode := c.Writer.Status()

	// 记录请求总数
	key := method + " " + path
	m.requestCount[key]++

	// 记录响应时间
	if _, exists := m.requestDuration[key]; !exists {
		m.requestDuration[key] = make([]time.Duration, 0)
	}
	m.requestDuration[key] = append(m.requestDuration[key], duration)

	// 限制存储的响应时间数量（保留最近1000个）
	if len(m.requestDuration[key]) > 1000 {
		m.requestDuration[key] = m.requestDuration[key][len(m.requestDuration[key])-1000:]
	}

	// 记录状态码
	m.statusCount[statusCode]++

	// 记录路径
	m.pathCount[path]++

	// 记录方法
	m.methodCount[method]++

	// 记录错误
	if statusCode >= 400 {
		errorKey := strconv.Itoa(statusCode)
		m.errorCount[errorKey]++
	}
}

// GetMetrics 获取指标数据
// 思路：返回当前的指标统计
// 使用例子：metrics := globalMetrics.GetMetrics()
func (m *MetricsCollector) GetMetrics() *Metrics {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 计算总请求数
	var totalRequests int64
	for _, count := range m.requestCount {
		totalRequests += count
	}

	// 计算总错误数
	var totalErrors int64
	for _, count := range m.errorCount {
		totalErrors += count
	}

	// 计算错误率
	var errorRate float64
	if totalRequests > 0 {
		errorRate = float64(totalErrors) / float64(totalRequests) * 100
	}

	// 计算平均响应时间
	avgResponseTime := m.calculateAverageResponseTime()
	p95ResponseTime := m.calculatePercentileResponseTime(0.95)
	p99ResponseTime := m.calculatePercentileResponseTime(0.99)

	// 转换状态码统计
	statusCodes := make(map[string]int64)
	for code, count := range m.statusCount {
		statusCodes[strconv.Itoa(code)] = count
	}

	return &Metrics{
		TotalRequests:   totalRequests,
		ActiveRequests:  m.activeRequests,
		TotalErrors:     totalErrors,
		ErrorRate:       errorRate,
		Uptime:          time.Since(m.startTime),
		AvgResponseTime: avgResponseTime,
		P95ResponseTime: p95ResponseTime,
		P99ResponseTime: p99ResponseTime,
		StatusCodes:     statusCodes,
		Methods:         copyMap(m.methodCount),
		Paths:           copyMap(m.pathCount),
		Errors:          copyMap(m.errorCount),
		LastUpdated:     time.Now(),
	}
}

// GetPathMetrics 获取路径指标
// 思路：返回各个路径的详细指标
func (m *MetricsCollector) GetPathMetrics() []*PathMetrics {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	pathMetrics := make([]*PathMetrics, 0)

	for path, count := range m.pathCount {
		// 计算该路径的错误数
		var errorCount int64
		for statusCode, errCount := range m.statusCount {
			if statusCode >= 400 {
				// 这里简化处理，实际应该按路径分别统计
				errorCount += errCount
			}
		}

		// 计算错误率
		var errorRate float64
		if count > 0 {
			errorRate = float64(errorCount) / float64(count) * 100
		}

		// 计算响应时间统计
		avgTime, minTime, maxTime := m.calculatePathResponseTimes(path)

		pathMetrics = append(pathMetrics, &PathMetrics{
			Path:            path,
			RequestCount:    count,
			ErrorCount:      errorCount,
			ErrorRate:       errorRate,
			AvgResponseTime: avgTime,
			MinResponseTime: minTime,
			MaxResponseTime: maxTime,
		})
	}

	return pathMetrics
}

// calculateAverageResponseTime 计算平均响应时间
func (m *MetricsCollector) calculateAverageResponseTime() time.Duration {
	var totalDuration time.Duration
	var totalCount int

	for _, durations := range m.requestDuration {
		for _, duration := range durations {
			totalDuration += duration
			totalCount++
		}
	}

	if totalCount == 0 {
		return 0
	}

	return totalDuration / time.Duration(totalCount)
}

// calculatePercentileResponseTime 计算百分位响应时间
func (m *MetricsCollector) calculatePercentileResponseTime(percentile float64) time.Duration {
	var allDurations []time.Duration

	for _, durations := range m.requestDuration {
		allDurations = append(allDurations, durations...)
	}

	if len(allDurations) == 0 {
		return 0
	}

	// 简单排序（实际应该使用更高效的算法）
	for i := 0; i < len(allDurations)-1; i++ {
		for j := i + 1; j < len(allDurations); j++ {
			if allDurations[i] > allDurations[j] {
				allDurations[i], allDurations[j] = allDurations[j], allDurations[i]
			}
		}
	}

	index := int(float64(len(allDurations)) * percentile)
	if index >= len(allDurations) {
		index = len(allDurations) - 1
	}

	return allDurations[index]
}

// calculatePathResponseTimes 计算路径响应时间统计
func (m *MetricsCollector) calculatePathResponseTimes(path string) (avg, min, max time.Duration) {
	var totalDuration time.Duration
	var count int
	minTime := time.Duration(0)
	maxTime := time.Duration(0)

	// 查找包含该路径的请求
	for _, durations := range m.requestDuration {
		if len(durations) == 0 {
			continue
		}

		// 简化处理：统计所有请求的响应时间
		// 实际应该更精确地按路径匹配
		for _, duration := range durations {
			totalDuration += duration
			count++

			if minTime == 0 || duration < minTime {
				minTime = duration
			}
			if duration > maxTime {
				maxTime = duration
			}
		}
	}

	if count > 0 {
		avg = totalDuration / time.Duration(count)
	}

	return avg, minTime, maxTime
}

// copyMap 复制map
func copyMap(original map[string]int64) map[string]int64 {
	copy := make(map[string]int64)
	for k, v := range original {
		copy[k] = v
	}
	return copy
}

// Reset 重置指标
// 思路：清空所有指标数据
func (m *MetricsCollector) Reset() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.requestCount = make(map[string]int64)
	m.requestDuration = make(map[string][]time.Duration)
	m.statusCount = make(map[int]int64)
	m.pathCount = make(map[string]int64)
	m.methodCount = make(map[string]int64)
	m.errorCount = make(map[string]int64)
	m.activeRequests = 0
	m.startTime = time.Now()
}

// GetGlobalMetrics 获取全局指标
// 思路：提供全局指标访问接口
func GetGlobalMetrics() *Metrics {
	return globalMetrics.GetMetrics()
}

// GetGlobalPathMetrics 获取全局路径指标
func GetGlobalPathMetrics() []*PathMetrics {
	return globalMetrics.GetPathMetrics()
}

// ResetGlobalMetrics 重置全局指标
func ResetGlobalMetrics() {
	globalMetrics.Reset()
}
