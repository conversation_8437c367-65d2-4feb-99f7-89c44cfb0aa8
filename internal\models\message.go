package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Message 消息模型
// 思路：存储所有平台的消息记录，支持不同消息类型
// 使用例子：msg := &Message{UserID: 1, Content: "你好", Type: "text", Direction: "incoming"}
type Message struct {
	ID        uint           `gorm:"primarykey" json:"id"`              // 主键ID
	CreatedAt time.Time      `json:"created_at"`                        // 创建时间
	UpdatedAt time.Time      `json:"updated_at"`                        // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"` // 软删除时间

	// 关联用户
	UserID uint `gorm:"not null;index" json:"user_id"`           // 用户ID
	User   User `gorm:"foreignKey:UserID" json:"user,omitempty"` // 关联用户

	// 会话信息
	SessionID string `gorm:"size:100;index" json:"session_id"` // 会话ID（用于分组对话）

	// 消息内容
	Content   string `gorm:"type:text" json:"content"`          // 消息内容
	Type      string `gorm:"size:20;not null" json:"type"`      // 消息类型：text, image, file, audio, video, location
	Direction string `gorm:"size:20;not null" json:"direction"` // 消息方向：incoming(用户发送), outgoing(机器人回复)

	// 平台相关
	Platform      string `gorm:"size:20;not null" json:"platform"` // 平台类型
	PlatformMsgID string `gorm:"size:100" json:"platform_msg_id"`  // 平台消息ID

	// 消息状态
	Status string `gorm:"size:20;default:sent" json:"status"` // 消息状态：sent, delivered, read, failed

	// AI相关
	IsAIGenerated bool   `gorm:"default:false" json:"is_ai_generated"` // 是否AI生成
	AIModel       string `gorm:"size:50" json:"ai_model"`              // 使用的AI模型
	TokensUsed    int    `gorm:"default:0" json:"tokens_used"`         // 使用的token数量

	// 扩展字段
	Metadata string `gorm:"type:text" json:"metadata"` // 元数据（JSON格式）

	// 关联关系
	ParentID *uint     `gorm:"index" json:"parent_id"`                        // 父消息ID（用于回复）
	Parent   *Message  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`   // 父消息
	Children []Message `gorm:"foreignKey:ParentID" json:"children,omitempty"` // 子消息
}

// TableName 指定表名
func (Message) TableName() string {
	return "messages"
}

// BeforeCreate GORM钩子：创建前处理
// 思路：设置默认值和生成会话ID
func (m *Message) BeforeCreate(tx *gorm.DB) error {
	if m.Status == "" {
		m.Status = "sent"
	}
	if m.SessionID == "" {
		// 生成会话ID：平台_用户ID_日期
		m.SessionID = generateSessionID(m.Platform, m.UserID)
	}
	return nil
}

// generateSessionID 生成会话ID
// 思路：使用平台、用户ID和日期生成唯一会话ID
func generateSessionID(platform string, userID uint) string {
	date := time.Now().Format("20060102")
	return fmt.Sprintf("%s_%d_%s", platform, userID, date)
}

// IsIncoming 检查是否为用户发送的消息
// 思路：根据方向判断消息来源
// 使用例子：if msg.IsIncoming() { ... }
func (m *Message) IsIncoming() bool {
	return m.Direction == "incoming"
}

// IsOutgoing 检查是否为机器人回复的消息
// 思路：根据方向判断消息来源
// 使用例子：if msg.IsOutgoing() { ... }
func (m *Message) IsOutgoing() bool {
	return m.Direction == "outgoing"
}

// MarkAsRead 标记消息为已读
// 思路：更新消息状态为已读
// 使用例子：msg.MarkAsRead(db)
func (m *Message) MarkAsRead(db *gorm.DB) error {
	m.Status = "read"
	return db.Model(m).Update("status", "read").Error
}

// MarkAsFailed 标记消息为发送失败
// 思路：更新消息状态为失败
// 使用例子：msg.MarkAsFailed(db)
func (m *Message) MarkAsFailed(db *gorm.DB) error {
	m.Status = "failed"
	return db.Model(m).Update("status", "failed").Error
}

// GetConversationHistory 获取会话历史
// 思路：根据会话ID获取对话历史，用于AI上下文
// 使用例子：history := msg.GetConversationHistory(db, 10)
func (m *Message) GetConversationHistory(db *gorm.DB, limit int) ([]Message, error) {
	var messages []Message
	err := db.Where("session_id = ?", m.SessionID).
		Order("created_at DESC").
		Limit(limit).
		Find(&messages).Error

	// 反转顺序，使最早的消息在前
	for i, j := 0, len(messages)-1; i < j; i, j = i+1, j-1 {
		messages[i], messages[j] = messages[j], messages[i]
	}

	return messages, err
}
