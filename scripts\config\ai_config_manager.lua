-- AI配置管理器
-- 思路：提供动态的AI配置管理功能，支持运行时修改配置
-- 使用例子：可以通过命令动态切换不同平台的AI配置

-- AI配置存储
-- 思路：使用全局变量存储配置，支持动态修改
local ai_configs = {
    -- 默认配置
    default = {
        api_key = "",
        base_url = "",
        model = "gpt-3.5-turbo",
        temperature = 0.7,
        max_tokens = 500
    },
    
    -- 平台配置
    platforms = {
        qq = {
            private = {
                api_key = "sk-your_siliconflow_key_here",
                base_url = "https://api.siliconflow.cn/v1",
                model = "Qwen/Qwen2.5-72B-Instruct",
                temperature = 0.7,
                max_tokens = 500
            },
            group = {
                api_key = "sk-your_siliconflow_key_here",
                base_url = "https://api.siliconflow.cn/v1",
                model = "meta-llama/Llama-3.1-8B-Instruct",
                temperature = 0.7,
                max_tokens = 200
            }
        },
        telegram = {
            private = {
                api_key = "sk-your_openai_key_here",
                base_url = "https://api.openai.com/v1",
                model = "gpt-4",
                temperature = 0.8,
                max_tokens = 600
            },
            group = {
                api_key = "sk-your_openai_key_here",
                base_url = "https://api.openai.com/v1",
                model = "gpt-3.5-turbo",
                temperature = 0.7,
                max_tokens = 400
            }
        },
        wechat = {
            private = {
                api_key = "sk-your_domestic_key_here",
                base_url = "https://api.domestic-ai.com/v1",
                model = "domestic-model",
                temperature = 0.6,
                max_tokens = 400
            },
            group = {
                api_key = "sk-your_domestic_key_here",
                base_url = "https://api.domestic-ai.com/v1",
                model = "domestic-model-lite",
                temperature = 0.6,
                max_tokens = 300
            }
        }
    },
    
    -- 特殊群聊配置
    groups = {
        ["123456789"] = {
            api_key = "sk-special_group_key_here",
            base_url = "https://api.siliconflow.cn/v1",
            model = "Qwen/Qwen2.5-72B-Instruct",
            temperature = 0.3,
            max_tokens = 300
        }
    },
    
    -- VIP用户配置
    vip_users = {
        ["vip_user_1"] = {
            api_key = "sk-premium_key_here",
            base_url = "https://api.openai.com/v1",
            model = "gpt-4-turbo",
            temperature = 0.7,
            max_tokens = 1000
        }
    }
}

-- 获取AI配置函数
-- 思路：根据优先级选择配置：VIP用户 > 特殊群聊 > 平台配置 > 默认配置
-- 参数：platform - 平台名称，user_id - 用户ID，group_id - 群聊ID，is_group_chat - 是否群聊
-- 返回：AI配置对象
function getAIConfig(platform, user_id, group_id, is_group_chat)
    -- 1. 检查VIP用户配置
    if ai_configs.vip_users[user_id] then
        log("info", "使用VIP用户配置: " .. user_id)
        return ai_configs.vip_users[user_id]
    end
    
    -- 2. 检查特殊群聊配置
    if is_group_chat and group_id and ai_configs.groups[group_id] then
        log("info", "使用特殊群聊配置: " .. group_id)
        return ai_configs.groups[group_id]
    end
    
    -- 3. 检查平台配置
    if ai_configs.platforms[platform] then
        local platform_config = ai_configs.platforms[platform]
        if is_group_chat and platform_config.group then
            log("info", "使用平台群聊配置: " .. platform)
            return platform_config.group
        elseif not is_group_chat and platform_config.private then
            log("info", "使用平台私聊配置: " .. platform)
            return platform_config.private
        end
    end
    
    -- 4. 使用默认配置
    log("info", "使用默认配置")
    return ai_configs.default
end

-- 设置AI配置函数
-- 思路：动态修改AI配置，支持运行时调整
-- 参数：config_type - 配置类型，config_key - 配置键，config - 配置对象
function setAIConfig(config_type, config_key, config)
    if config_type == "default" then
        ai_configs.default = config
        log("info", "更新默认配置")
    elseif config_type == "platform" then
        local parts = {}
        for part in config_key:gmatch("[^%.]+") do
            table.insert(parts, part)
        end
        if #parts == 2 then
            local platform, chat_type = parts[1], parts[2]
            if not ai_configs.platforms[platform] then
                ai_configs.platforms[platform] = {}
            end
            ai_configs.platforms[platform][chat_type] = config
            log("info", "更新平台配置: " .. config_key)
        end
    elseif config_type == "group" then
        ai_configs.groups[config_key] = config
        log("info", "更新群聊配置: " .. config_key)
    elseif config_type == "vip" then
        ai_configs.vip_users[config_key] = config
        log("info", "更新VIP用户配置: " .. config_key)
    end
end

-- 获取所有配置函数
-- 思路：返回当前所有AI配置，用于管理和调试
function getAllAIConfigs()
    return ai_configs
end

-- 配置验证函数
-- 思路：验证AI配置的有效性
-- 参数：config - 配置对象
-- 返回：是否有效，错误信息
function validateAIConfig(config)
    if not config then
        return false, "配置为空"
    end
    
    if not config.model or config.model == "" then
        return false, "模型名称不能为空"
    end
    
    if config.api_key and config.api_key ~= "" then
        if not config.base_url or config.base_url == "" then
            return false, "指定API密钥时必须提供base_url"
        end
    end
    
    if config.temperature and (config.temperature < 0 or config.temperature > 2) then
        return false, "temperature必须在0-2之间"
    end
    
    if config.max_tokens and config.max_tokens <= 0 then
        return false, "max_tokens必须大于0"
    end
    
    return true, nil
end

-- 配置命令处理函数
-- 思路：处理配置相关的命令，支持动态管理
-- 参数：command - 命令字符串，user_id - 用户ID
-- 返回：处理结果
function handleConfigCommand(command, user_id)
    local parts = {}
    for part in command:gmatch("%S+") do
        table.insert(parts, part)
    end
    
    if #parts < 2 then
        return "配置命令格式错误。使用: /config <action> [参数]"
    end
    
    local action = parts[2]
    
    if action == "list" then
        -- 列出所有配置
        local result = "📋 AI配置列表:\n"
        result = result .. "默认配置: " .. ai_configs.default.model .. "\n"
        
        for platform, config in pairs(ai_configs.platforms) do
            result = result .. "平台 " .. platform .. ":\n"
            if config.private then
                result = result .. "  私聊: " .. config.private.model .. "\n"
            end
            if config.group then
                result = result .. "  群聊: " .. config.group.model .. "\n"
            end
        end
        
        return result
    elseif action == "test" then
        -- 测试配置
        if #parts < 4 then
            return "测试命令格式: /config test <platform> <chat_type>"
        end
        
        local platform = parts[3]
        local chat_type = parts[4]
        local is_group = (chat_type == "group")
        
        local config = getAIConfig(platform, user_id, "", is_group)
        local valid, error_msg = validateAIConfig(config)
        
        if valid then
            return "✅ 配置有效: " .. config.model
        else
            return "❌ 配置无效: " .. error_msg
        end
    elseif action == "switch" then
        -- 切换模型
        if #parts < 5 then
            return "切换命令格式: /config switch <platform> <chat_type> <model>"
        end
        
        local platform = parts[3]
        local chat_type = parts[4]
        local new_model = parts[5]
        
        local config_key = platform .. "." .. chat_type
        local current_config = getAIConfig(platform, user_id, "", chat_type == "group")
        
        -- 复制当前配置并修改模型
        local new_config = {}
        for k, v in pairs(current_config) do
            new_config[k] = v
        end
        new_config.model = new_model
        
        setAIConfig("platform", config_key, new_config)
        return "✅ 已切换 " .. platform .. " " .. chat_type .. " 模型为: " .. new_model
    end
    
    return "未知的配置命令: " .. action
end

-- 导出函数供其他脚本使用
_G.getAIConfig = getAIConfig
_G.setAIConfig = setAIConfig
_G.getAllAIConfigs = getAllAIConfigs
_G.validateAIConfig = validateAIConfig
_G.handleConfigCommand = handleConfigCommand

log("info", "AI配置管理器已加载")
