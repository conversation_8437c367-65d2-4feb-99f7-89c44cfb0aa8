package database

import (
	"fmt"
	"log"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 全局数据库实例
var DB *gorm.DB

// Initialize 初始化数据库连接
// 思路：根据配置连接不同类型的数据库，并执行自动迁移
// 使用例子：err := database.Initialize(cfg)
func Initialize(cfg *config.Config) error {
	var err error
	var dialector gorm.Dialector

	// 根据数据库类型选择驱动
	switch cfg.Database.Type {
	case "sqlite":
		dialector = sqlite.Open(cfg.Database.DSN)
	case "mysql":
		dialector = mysql.Open(cfg.Database.DSN)
	case "postgres":
		dialector = postgres.Open(cfg.Database.DSN)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", cfg.Database.Type)
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // 开发环境显示SQL日志
		NowFunc: func() time.Time {
			return time.Now().Local() // 使用本地时间
		},
	}

	// 连接数据库
	DB, err = gorm.Open(dialector, gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	// 执行数据库迁移
	if err := migrate(); err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	log.Println("数据库连接成功")
	return nil
}

// migrate 执行数据库迁移
// 思路：自动创建和更新数据库表结构
func migrate() error {
	// 自动迁移所有模型
	// 思路：确保所有模型都正确迁移，插件存储使用现有的Message模型
	err := DB.AutoMigrate(
		&models.User{},
		&models.Message{},
		&models.Knowledge{},
		&models.Session{},
		&models.Script{},
	)
	if err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	// 创建索引
	if err := createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	// 插入初始数据
	if err := seedData(); err != nil {
		return fmt.Errorf("插入初始数据失败: %w", err)
	}

	log.Println("数据库迁移完成")
	return nil
}

// createIndexes 创建数据库索引
// 思路：为常用查询字段创建索引，提高查询性能
func createIndexes() error {
	// 用户表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_users_platform_id ON users(platform, platform_id)").Error; err != nil {
		return err
	}

	// 消息表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_session_created ON messages(session_id, created_at)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_messages_user_created ON messages(user_id, created_at)").Error; err != nil {
		return err
	}

	// 知识库表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_knowledge_category_status ON knowledge(category, status)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_knowledge_keywords ON knowledge(keywords)").Error; err != nil {
		return err
	}

	// 会话表索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_sessions_user_platform ON sessions(user_id, platform)").Error; err != nil {
		return err
	}

	return nil
}

// seedData 插入初始数据
// 思路：插入系统必需的初始数据
func seedData() error {
	// 检查是否已有数据
	var count int64
	DB.Model(&models.Knowledge{}).Count(&count)
	if count > 0 {
		return nil // 已有数据，跳过初始化
	}

	// 插入默认知识库条目
	defaultKnowledge := []models.Knowledge{
		{
			Title:    "欢迎使用客服系统",
			Content:  "欢迎使用我们的智能客服系统！我可以帮助您解答各种问题。如果您需要人工客服，请输入\"转人工\"。",
			Category: "系统",
			Status:   "active",
			Priority: 100,
			IsPublic: true,
		},
		{
			Title:    "工作时间",
			Content:  "我们的人工客服工作时间是周一至周五 9:00-18:00，周末 10:00-16:00。其他时间您可以留言，我们会尽快回复。",
			Category: "常见问题",
			Status:   "active",
			Priority: 90,
			IsPublic: true,
		},
		{
			Title:    "联系方式",
			Content:  "您可以通过以下方式联系我们：\n- 在线客服：24小时智能客服\n- 电话：400-123-4567\n- 邮箱：<EMAIL>",
			Category: "常见问题",
			Status:   "active",
			Priority: 80,
			IsPublic: true,
		},
	}

	for _, kb := range defaultKnowledge {
		if err := DB.Create(&kb).Error; err != nil {
			return err
		}
	}

	log.Println("初始数据插入完成")
	return nil
}

// Close 关闭数据库连接
// 思路：优雅关闭数据库连接
// 使用例子：database.Close()
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB 获取数据库实例
// 思路：提供全局数据库实例访问
// 使用例子：db := database.GetDB()
func GetDB() *gorm.DB {
	return DB
}

// Ping 检查数据库连接
// 思路：健康检查时验证数据库连接状态
// 使用例子：err := database.Ping()
func Ping() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Ping()
}
