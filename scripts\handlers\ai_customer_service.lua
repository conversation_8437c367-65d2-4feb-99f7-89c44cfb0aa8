-- AI智能客服处理器
-- 集成OpenAI、知识库和平台接口的完整客服解决方案
-- 更新时间: 2025-07-16 09:42

-- 消息过滤函数
-- 思路：判断是否应该响应这条消息，避免对所有消息都回复
local function shouldRespondToMessage(content, user_id, platform)
    local content_lower = content:lower()

    -- 1. 过滤过短的消息
    if string.len(content) < 3 then
        return false
    end

    -- 2. 过滤CQ码消息（图片、表情、语音等）
    if content:find("%[CQ:") then
        return false
    end

    -- 3. 过滤纯表情和符号
    if content:match("^[%.%,!%?]+$") or content:match("^%d+$") then
        return false
    end

    -- 4. 过滤常见闲聊词汇
    local ignore_keywords = {
        "哈哈", "呵呵", "666", "牛逼", "厉害", "赞", "好的", "嗯", "哦", "啊",
        "👍", "👌", "😂", "😄", "😊", "🤣", "😁", "😃", "😆", "😅"
    }

    for _, keyword in ipairs(ignore_keywords) do
        if content_lower:find(keyword:lower()) then
            return false
        end
    end

    -- 4. 检查响应关键词
    local response_keywords = {
        "客服", "帮助", "help", "问题", "咨询", "售后", "投诉", "bug", "故障",
        "怎么", "如何", "为什么", "什么", "哪里", "谁", "when", "where", "why", "how", "what"
    }

    for _, keyword in ipairs(response_keywords) do
        if content_lower:find(keyword:lower()) then
            return true
        end
    end

    -- 5. 检查命令前缀
    local first_char = content:sub(1, 1)
    local command_prefixes = { "/", "!", "@", "？", "?" }
    for _, prefix in ipairs(command_prefixes) do
        if first_char == prefix then
            return true
        end
    end

    -- 6. 检查问句（包含问号）
    if content:find("?") or content:find("？") then
        return true
    end

    -- 7. 检查消息长度（适中长度的消息可能是问题，但过长的可能是闲聊）
    local content_len = string.len(content)
    if content_len >= 10 and content_len <= 50 then
        -- 中等长度的消息，可能是问题，但需要进一步检查
        -- 如果包含游戏相关词汇，则不响应
        local game_keywords = {
            "游戏", "打牌", "发牌", "摸到", "866", "司马", "对面", "三张", "牌员",
            "高松", "千早", "长崎", "椎名", "胃袋", "肉食", "爱吃", "立吸"
        }

        for _, keyword in ipairs(game_keywords) do
            if content_lower:find(keyword) then
                return false
            end
        end

        return true
    end

    -- 8. 默认不响应
    return false
end

-- 检查是否是语法验证模式，如果是则跳过实际处理
if __syntax_check_mode then
    log("info", "语法验证模式，跳过AI客服处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

--log("info", "AI客服处理用户 " .. user_id .. " 的消息: " .. content)

-- 0. 消息过滤检查（最高优先级）
-- 检查是否应该响应这条消息
local should_respond = shouldRespondToMessage(content, user_id, platform)
if not should_respond then
    -- log("info", "根据过滤规则，不响应此消息")
    return "消息已过滤，不响应"
end

-- 1. 快速回复检查（优先级最高）
local quick_replies = {
    ["你好"] = "您好！我是AI智能客服，很高兴为您服务！😊",
    ["hi"] = "Hello! I'm your AI assistant, how can I help you today?",
    ["hello"] = "Hello! I'm your AI assistant, how can I help you today?",
    ["帮助"] = [[🤖 AI客服帮助菜单：
• 直接提问 - 我会智能回答您的问题
• "知识库" - 搜索相关文档
• "人工客服" - 转接人工服务
• "历史记录" - 查看对话历史]],
    ["help"] = [[🤖 AI Customer Service Help:
• Ask questions - I'll provide intelligent answers
• "knowledge" - Search documentation
• "human" - Connect to human agent
• "history" - View chat history]]
}

-- 检查快速回复
for keyword, reply in pairs(quick_replies) do
    if content:lower() == keyword:lower() then
        send_message(user_id, reply)
        log("info", "发送快速回复: " .. keyword)
        return "快速回复: " .. keyword
    end
end

-- 2. 特殊命令处理
if content:lower() == "知识库" or content:lower() == "knowledge" then
    -- 调用知识库搜索API
    local kb_result = call_api("GET", "/api/v1/knowledge/", {
        query = "常见问题",
        limit = 5
    })

    if kb_result and kb_result.success then
        local kb_text = "📚 知识库搜索结果：\n"
        for i, item in ipairs(kb_result.data) do
            kb_text = kb_text .. (i .. ". " .. item.title .. "\n")
        end
        send_message(user_id, kb_text)
        return "已发送知识库搜索结果"
    else
        send_message(user_id, "抱歉，知识库暂时无法访问，请稍后再试。")
        return "知识库访问失败"
    end
end

if content:lower() == "人工客服" or content:lower() == "human" then
    -- 设置转人工标记
    set_var("transfer_to_human_" .. user_id, true)
    send_message(user_id, "正在为您转接人工客服，请稍候...")

    -- 通知管理员
    notify_admin("用户 " .. user_id .. " 请求人工客服")
    return "已转接人工客服"
end

-- 3. 知识库智能搜索
--log("info", "开始知识库搜索: " .. content)
local kb_search = call_api("POST", "/api/v1/knowledge/search", {
    query = content,
    limit = 3,
    threshold = 0.7 -- 相似度阈值
})

local kb_answer = nil
if kb_search and kb_search.success and #kb_search.data > 0 then
    local best_match = kb_search.data[1]
    if best_match.score > 0.8 then -- 高相似度直接返回
        local kb_reply = "📚 " .. best_match.content
        send_message(user_id, kb_reply)

        -- 存储知识库回复到插件化存储系统
        local parent_message_id = get_var("last_message_id_" .. user_id) or ""

        -- 调用聊天存储管理器的存储函数
        if store_ai_reply_message then
            local store_success, reply_id = store_ai_reply_message(
                user_id,
                platform,
                kb_reply,
                "knowledge_base",
                0,    -- 知识库回复不消耗tokens
                parent_message_id,
                true, -- 来自知识库
                tostring(best_match.id or "")
            )

            if store_success then
                log("info", "知识库回复已存储到插件系统: " .. (reply_id or "unknown"))
            else
                log("warn", "知识库回复存储失败: " .. (reply_id or "unknown"))
            end
        else
            -- log("warn", "store_ai_reply_message 函数不可用，跳过知识库回复存储")
        end

        -- log("info", "知识库直接匹配: " .. best_match.title)
        return "知识库直接回答: " .. best_match.title
    elseif best_match.score > 0.6 then -- 中等相似度作为上下文
        kb_answer = best_match.content
        log("info", "知识库上下文: " .. best_match.title)
    end
end

-- 4. OpenAI智能回答
-- log("info", "调用OpenAI API")

-- 构建对话上下文
local conversation_context = get_var("conversation_" .. user_id) or ""
local system_prompt = [[你是一个专业的AI客服助手，请遵循以下规则：
1. 友好、专业、耐心地回答用户问题
2. 如果不确定答案，诚实地说明并建议联系人工客服
3. 回答要简洁明了，避免过长的回复
4. 使用适当的emoji让对话更友好
5. 如果用户询问技术问题，提供准确的信息]]

-- 添加知识库上下文
if kb_answer then
    system_prompt = system_prompt .. "\n\n相关知识库信息：" .. kb_answer
end

-- 构建消息历史
local messages = {
    { role = "system", content = system_prompt },
    { role = "user",   content = content }
}

-- 添加对话历史（最近3轮）
if conversation_context ~= "" then
    local history = json_decode(conversation_context)
    if history and #history > 0 then
        local start_idx = math.max(1, #history - 2) -- 最近3轮对话
        for i = start_idx, #history do
            table.insert(messages, history[i])
        end
    end
end

-- 调用OpenAI API（兼容硅基流动等OpenAI兼容服务）
-- 思路：使用配置中的默认模型，支持OpenAI、硅基流动等多种服务
-- 常用模型：
-- OpenAI: gpt-3.5-turbo, gpt-4, gpt-4-turbo
-- 硅基流动: Qwen/Qwen2.5-72B-Instruct, deepseek-ai/DeepSeek-V3, meta-llama/Llama-3.1-8B-Instruct
local openai_response = call_openai({
    model = "gpt-3.5-turbo", -- 默认模型，会被配置文件中的设置覆盖
    messages = messages,
    max_tokens = 500,
    temperature = 0.7
})

if openai_response and openai_response.success then
    local ai_reply = openai_response.choices[1].message.content
    local tokens_used = openai_response.usage and openai_response.usage.total_tokens or 0

    -- 发送AI回复
    send_message(user_id, "🤖 " .. ai_reply)

    -- 存储AI回复到插件化存储系统
    -- 获取用户最后一条消息ID作为父消息ID
    local parent_message_id = get_var("last_message_id_" .. user_id) or ""
    local knowledge_id = kb_answer and "kb_match" or ""

    -- 调用聊天存储管理器的存储函数
    if store_ai_reply_message then
        local store_success, reply_id = store_ai_reply_message(
            user_id,
            platform,
            "🤖 " .. ai_reply,
            "gpt-3.5-turbo",
            tokens_used,
            parent_message_id,
            kb_answer ~= nil,
            knowledge_id
        )

        if store_success then
            log("info", "AI回复已存储到插件系统: " .. (reply_id or "unknown"))
        else
            log("warn", "AI回复存储失败: " .. (reply_id or "unknown"))
        end
    else
        log("warn", "store_ai_reply_message 函数不可用，跳过AI回复存储")
    end

    -- 更新对话历史
    local new_history = json_decode(conversation_context) or {}
    table.insert(new_history, { role = "user", content = content })
    table.insert(new_history, { role = "assistant", content = ai_reply })

    -- 保持最近10轮对话
    if #new_history > 20 then
        for i = 1, 4 do
            table.remove(new_history, 1)
        end
    end

    set_var("conversation_" .. user_id, json_encode(new_history))

    log("info", "OpenAI回复成功，长度: " .. string.len(ai_reply) .. ", tokens: " .. tokens_used)
    return "AI智能回复: " .. string.sub(ai_reply, 1, 50) .. "..."
else
    -- OpenAI调用失败，使用备用回复
    log("error", "OpenAI API调用失败")

    local fallback_reply = "抱歉，我现在遇到了一些技术问题。请您：\n" ..
        "1. 稍后再试\n" ..
        "2. 发送'人工客服'转接人工服务\n" ..
        "3. 发送'知识库'搜索相关文档"

    send_message(user_id, fallback_reply)
    return "OpenAI调用失败，已发送备用回复"
end
