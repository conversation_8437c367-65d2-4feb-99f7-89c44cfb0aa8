package services

import (
	"context"
	"fmt"
	"testing"

	"aike_go/internal/database"
	"aike_go/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// KnowledgeServiceTestSuite 知识库服务测试套件
// 思路：使用testify套件进行完整的知识库服务测试
type KnowledgeServiceTestSuite struct {
	suite.Suite
	db               *gorm.DB
	knowledgeService *KnowledgeService
}

// SetupSuite 测试套件初始化
func (suite *KnowledgeServiceTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 设置全局数据库连接
	database.SetDB(db)
	suite.db = db

	// 运行数据库迁移
	err = database.AutoMigrate()
	suite.Require().NoError(err)

	// 创建知识库服务
	suite.knowledgeService = NewKnowledgeService()

	// 创建测试数据
	testKnowledge := []*models.Knowledge{
		{
			Title:     "工作时间",
			Content:   "我们的工作时间是周一至周五 9:00-18:00",
			Keywords:  "工作时间,上班时间,营业时间",
			Category:  "基础信息",
			Status:    "active",
			CreatedBy: "admin",
		},
		{
			Title:     "联系方式",
			Content:   "客服电话：400-123-4567，邮箱：<EMAIL>",
			Keywords:  "联系方式,电话,邮箱,客服",
			Category:  "基础信息",
			Status:    "active",
			CreatedBy: "admin",
		},
		{
			Title:     "退款政策",
			Content:   "商品在7天内可以无理由退款",
			Keywords:  "退款,退货,政策",
			Category:  "售后服务",
			Status:    "active",
			CreatedBy: "admin",
		},
		{
			Title:     "草稿条目",
			Content:   "这是一个草稿条目",
			Keywords:  "草稿",
			Category:  "测试",
			Status:    "draft",
			CreatedBy: "admin",
		},
	}

	for _, knowledge := range testKnowledge {
		err = suite.db.Create(knowledge).Error
		suite.Require().NoError(err)
	}
}

// TearDownSuite 测试套件清理
func (suite *KnowledgeServiceTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// TestCreateKnowledge 测试创建知识库条目
// 思路：验证知识库条目的创建功能
func (suite *KnowledgeServiceTestSuite) TestCreateKnowledge() {
	ctx := context.Background()

	knowledge := &models.Knowledge{
		Title:     "新的问题",
		Content:   "新的答案",
		Keywords:  "新问题,测试",
		Category:  "测试分类",
		Status:    "active",
		CreatedBy: "test_user",
	}

	// 创建知识库条目
	err := suite.knowledgeService.CreateKnowledge(ctx, knowledge)

	// 验证结果
	suite.NoError(err)
	suite.NotZero(knowledge.ID)

	// 验证数据库中的数据
	var savedKnowledge models.Knowledge
	err = suite.db.First(&savedKnowledge, knowledge.ID).Error
	suite.NoError(err)
	suite.Equal("新的问题", savedKnowledge.Title)
	suite.Equal("新的答案", savedKnowledge.Content)
}

// TestSearchKnowledge_ExactMatch 测试精确匹配搜索
// 思路：验证关键词精确匹配的搜索功能
func (suite *KnowledgeServiceTestSuite) TestSearchKnowledge_ExactMatch() {
	ctx := context.Background()

	// 搜索工作时间
	results, err := suite.knowledgeService.SearchKnowledge(ctx, "工作时间", 10)

	// 验证结果
	suite.NoError(err)
	suite.NotEmpty(results)

	// 应该找到工作时间相关的条目
	found := false
	for _, result := range results {
		if result.Title == "工作时间" {
			found = true
			suite.Equal("我们的工作时间是周一至周五 9:00-18:00", result.Content)
			break
		}
	}
	suite.True(found, "应该找到工作时间相关的条目")
}

// TestSearchKnowledge_PartialMatch 测试部分匹配搜索
// 思路：验证内容部分匹配的搜索功能
func (suite *KnowledgeServiceTestSuite) TestSearchKnowledge_PartialMatch() {
	ctx := context.Background()

	// 搜索客服相关内容
	results, err := suite.knowledgeService.SearchKnowledge(ctx, "客服", 10)

	// 验证结果
	suite.NoError(err)
	suite.NotEmpty(results)

	// 应该找到联系方式相关的条目
	found := false
	for _, result := range results {
		if result.Title == "联系方式" {
			found = true
			break
		}
	}
	suite.True(found, "应该找到联系方式相关的条目")
}

// TestSearchKnowledge_NoMatch 测试无匹配搜索
// 思路：验证无匹配结果时的处理
func (suite *KnowledgeServiceTestSuite) TestSearchKnowledge_NoMatch() {
	ctx := context.Background()

	// 搜索不存在的内容
	results, err := suite.knowledgeService.SearchKnowledge(ctx, "不存在的问题", 10)

	// 验证结果
	suite.NoError(err)
	suite.Empty(results)
}

// TestSearchKnowledge_OnlyActiveStatus 测试只搜索活跃状态的条目
// 思路：验证搜索只返回活跃状态的知识库条目
func (suite *KnowledgeServiceTestSuite) TestSearchKnowledge_OnlyActiveStatus() {
	ctx := context.Background()

	// 搜索草稿相关内容
	results, err := suite.knowledgeService.SearchKnowledge(ctx, "草稿", 10)

	// 验证结果 - 不应该找到草稿状态的条目
	suite.NoError(err)
	for _, result := range results {
		suite.NotEqual("draft", result.Status, "搜索结果不应包含草稿状态的条目")
	}
}

// TestListKnowledge 测试列出知识库条目
// 思路：验证知识库条目的分页列表功能
func (suite *KnowledgeServiceTestSuite) TestListKnowledge() {
	ctx := context.Background()

	// 测试获取第一页
	results, total, err := suite.knowledgeService.ListKnowledge(ctx, 2, 0, "", "")

	// 验证结果
	suite.NoError(err)
	suite.LessOrEqual(len(results), 2)    // 限制为2条
	suite.GreaterOrEqual(total, int64(3)) // 至少有3条活跃记录

	// 测试分类过滤
	results, total, err = suite.knowledgeService.ListKnowledge(ctx, 10, 0, "基础信息", "")
	suite.NoError(err)
	suite.Equal(int64(2), total) // 基础信息分类有2条记录

	for _, result := range results {
		suite.Equal("基础信息", result.Category)
	}
}

// TestUpdateKnowledge 测试更新知识库条目
// 思路：验证知识库条目的更新功能
func (suite *KnowledgeServiceTestSuite) TestUpdateKnowledge() {
	ctx := context.Background()

	// 获取一个现有条目
	var knowledge models.Knowledge
	err := suite.db.Where("title = ?", "工作时间").First(&knowledge).Error
	suite.Require().NoError(err)

	// 更新内容
	knowledge.Content = "更新后的工作时间：周一至周五 8:00-17:00"
	knowledge.UpdatedBy = "test_updater"

	// 执行更新
	err = suite.knowledgeService.UpdateKnowledge(ctx, &knowledge)

	// 验证结果
	suite.NoError(err)

	// 验证数据库中的数据已更新
	var updatedKnowledge models.Knowledge
	err = suite.db.First(&updatedKnowledge, knowledge.ID).Error
	suite.NoError(err)
	suite.Equal("更新后的工作时间：周一至周五 8:00-17:00", updatedKnowledge.Content)
	suite.Equal("test_updater", updatedKnowledge.UpdatedBy)
}

// TestDeleteKnowledge 测试删除知识库条目
// 思路：验证知识库条目的软删除功能
func (suite *KnowledgeServiceTestSuite) TestDeleteKnowledge() {
	ctx := context.Background()

	// 获取一个现有条目
	var knowledge models.Knowledge
	err := suite.db.Where("title = ?", "退款政策").First(&knowledge).Error
	suite.Require().NoError(err)

	// 执行删除
	err = suite.knowledgeService.DeleteKnowledge(ctx, knowledge.ID)

	// 验证结果
	suite.NoError(err)

	// 验证条目已被软删除
	var deletedKnowledge models.Knowledge
	err = suite.db.Unscoped().First(&deletedKnowledge, knowledge.ID).Error
	suite.NoError(err)
	suite.NotNil(deletedKnowledge.DeletedAt)

	// 验证正常查询无法找到已删除的条目
	err = suite.db.First(&deletedKnowledge, knowledge.ID).Error
	suite.Error(err)
	suite.Equal(gorm.ErrRecordNotFound, err)
}

// TestIncrementUsageCount 测试使用次数增加
// 思路：验证知识库条目使用统计功能
func (suite *KnowledgeServiceTestSuite) TestIncrementUsageCount() {
	ctx := context.Background()

	// 获取一个现有条目
	var knowledge models.Knowledge
	err := suite.db.Where("title = ?", "联系方式").First(&knowledge).Error
	suite.Require().NoError(err)

	initialUsageCount := knowledge.UsageCount

	// 增加使用次数
	err = suite.knowledgeService.IncrementUsageCount(ctx, knowledge.ID)

	// 验证结果
	suite.NoError(err)

	// 验证使用次数已增加
	var updatedKnowledge models.Knowledge
	err = suite.db.First(&updatedKnowledge, knowledge.ID).Error
	suite.NoError(err)
	suite.Equal(initialUsageCount+1, updatedKnowledge.UsageCount)
}

// 基准测试

// BenchmarkSearchKnowledge 知识库搜索性能测试
// 思路：测试知识库搜索的性能表现
func BenchmarkSearchKnowledge(b *testing.B) {
	// 设置测试环境
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	database.SetDB(db)
	database.AutoMigrate()

	knowledgeService := NewKnowledgeService()

	// 创建大量测试数据
	for i := 0; i < 100; i++ {
		knowledge := &models.Knowledge{
			Title:     fmt.Sprintf("测试问题 %d", i),
			Content:   fmt.Sprintf("这是第 %d 个测试答案", i),
			Keywords:  fmt.Sprintf("测试,问题,%d", i),
			Category:  "测试分类",
			Status:    "active",
			CreatedBy: "benchmark",
		}
		db.Create(knowledge)
	}

	ctx := context.Background()

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		_, err := knowledgeService.SearchKnowledge(ctx, "测试", 10)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// 运行测试套件
func TestKnowledgeServiceTestSuite(t *testing.T) {
	suite.Run(t, new(KnowledgeServiceTestSuite))
}

// 单独的单元测试

// TestKnowledgeService_InvalidInput 测试无效输入处理
// 思路：验证服务对无效输入的处理
func TestKnowledgeService_InvalidInput(t *testing.T) {
	knowledgeService := NewKnowledgeService()
	ctx := context.Background()

	// 测试创建空知识库条目
	err := knowledgeService.CreateKnowledge(ctx, nil)
	assert.Error(t, err)

	// 测试创建无标题的知识库条目
	emptyKnowledge := &models.Knowledge{
		Content:  "内容",
		Keywords: "关键词",
		Category: "分类",
		Status:   "active",
	}
	err = knowledgeService.CreateKnowledge(ctx, emptyKnowledge)
	assert.Error(t, err)

	// 测试搜索空查询
	results, err := knowledgeService.SearchKnowledge(ctx, "", 10)
	assert.NoError(t, err)
	assert.Empty(t, results)
}
