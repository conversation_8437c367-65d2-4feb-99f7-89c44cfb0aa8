# 硅基流动集成指南

## 概述

硅基流动（SiliconFlow）是一个提供多种开源大模型 API 服务的平台，完全兼容 OpenAI 接口规范。本指南将帮助您将 AI 客服系统从 OpenAI 切换到硅基流动，或者配置为使用硅基流动的服务。

## 优势

- ✅ **完全兼容 OpenAI 接口**：无需修改代码，只需更改配置
- ✅ **价格更便宜**：相比 OpenAI 官方 API 价格更优惠
- ✅ **国内访问速度快**：服务器在国内，访问延迟更低
- ✅ **多种模型选择**：支持 Qwen、DeepSeek、Llama 等多种开源模型
- ✅ **中文优化**：提供专门针对中文优化的模型

## 快速配置

### 方法一：修改环境变量

1. **获取 API Key**
   - 访问 [硅基流动官网](https://cloud.siliconflow.cn/)
   - 注册并登录账号
   - 进入 [API 密钥页面](https://cloud.siliconflow.cn/account/ak)
   - 点击"新建API密钥"创建密钥

2. **修改 .env 文件**
   ```bash
   # 将原来的 OpenAI 配置
   OPENAI_API_KEY=your_openai_key
   OPENAI_BASE_URL=https://api.openai.com/v1
   OPENAI_MODEL=gpt-3.5-turbo
   
   # 改为硅基流动配置
   OPENAI_API_KEY=your_siliconflow_api_key
   OPENAI_BASE_URL=https://api.siliconflow.cn/v1
   OPENAI_MODEL=Qwen/Qwen2.5-72B-Instruct
   ```

### 方法二：使用配置文件

1. **复制配置模板**
   ```bash
   cp config.siliconflow.yaml config.yaml
   ```

2. **修改 API Key**
   ```yaml
   openai:
     api_key: "your_siliconflow_api_key_here"
     base_url: "https://api.siliconflow.cn/v1"
     model: "Qwen/Qwen2.5-72B-Instruct"
   ```

## 推荐模型

### 通用对话模型

| 模型名称 | 特点 | 适用场景 |
|---------|------|----------|
| `Qwen/Qwen2.5-72B-Instruct` | 中文优化，性能强 | 客服对话、知识问答 |
| `deepseek-ai/DeepSeek-V3` | 推理能力强 | 复杂问题解答 |
| `meta-llama/Llama-3.1-8B-Instruct` | 轻量级，速度快 | 简单对话、快速响应 |

### 专业模型

| 模型名称 | 特点 | 适用场景 |
|---------|------|----------|
| `Pro/deepseek-ai/DeepSeek-R1` | 推理模型（付费） | 复杂逻辑推理 |
| `01-ai/Yi-1.5-34B-Chat` | 中文优化 | 中文客服 |
| `Qwen/Qwen2.5-32B-Instruct` | 平衡性能和成本 | 一般客服场景 |

## 完整配置示例

```yaml
# config.yaml
server:
  host: "0.0.0.0"
  port: 8080

database:
  type: "sqlite"
  database: "data/aike.db"

openai:
  api_key: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  base_url: "https://api.siliconflow.cn/v1"
  model: "Qwen/Qwen2.5-72B-Instruct"

platforms:
  qq:
    enabled: true
    base_url: "http://localhost:3000"
    token: ""
```

## 测试配置

启动服务后，可以通过以下方式测试硅基流动集成是否成功：

1. **查看启动日志**
   ```bash
   ./main.exe
   ```
   
2. **发送测试消息**
   - 在 QQ 群中发送包含"客服"或"帮助"的消息
   - 观察是否收到 AI 回复

3. **检查日志输出**
   - 查看是否有 OpenAI API 调用成功的日志
   - 确认使用的是硅基流动的模型

## 故障排除

### 常见问题

1. **API Key 无效**
   ```
   错误：OpenAI API调用失败: 401 Unauthorized
   解决：检查 API Key 是否正确，是否已激活
   ```

2. **模型不存在**
   ```
   错误：model not found
   解决：检查模型名称是否正确，参考官方模型列表
   ```

3. **网络连接问题**
   ```
   错误：connection timeout
   解决：检查网络连接，确认可以访问 api.siliconflow.cn
   ```

### 调试步骤

1. **验证 API Key**
   ```bash
   curl -H "Authorization: Bearer your_api_key" \
        https://api.siliconflow.cn/v1/models
   ```

2. **测试简单调用**
   ```bash
   curl -X POST https://api.siliconflow.cn/v1/chat/completions \
        -H "Authorization: Bearer your_api_key" \
        -H "Content-Type: application/json" \
        -d '{
          "model": "Qwen/Qwen2.5-72B-Instruct",
          "messages": [{"role": "user", "content": "你好"}]
        }'
   ```

## 性能优化建议

1. **选择合适的模型**
   - 简单客服：使用 8B 模型（速度快）
   - 复杂问答：使用 72B 模型（效果好）

2. **调整参数**
   ```lua
   local openai_response = call_openai({
       model = "Qwen/Qwen2.5-72B-Instruct",
       messages = messages,
       max_tokens = 300,      -- 减少 token 数量
       temperature = 0.3      -- 降低随机性
   })
   ```

3. **启用缓存**
   - 对常见问题启用知识库缓存
   - 减少重复的 API 调用

## 成本控制

1. **设置使用限制**
   - 在硅基流动控制台设置每日/每月使用限额
   - 监控 API 调用次数和费用

2. **优化调用策略**
   - 优先使用知识库匹配
   - 只在必要时调用 AI 模型
   - 设置合理的 max_tokens 限制

## 相关链接

- [硅基流动官网](https://cloud.siliconflow.cn/)
- [API 文档](https://docs.siliconflow.cn/)
- [模型列表](https://cloud.siliconflow.cn/models)
- [价格说明](https://cloud.siliconflow.cn/pricing)
