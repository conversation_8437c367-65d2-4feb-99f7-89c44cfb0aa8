package main

import (
	"context"
	"fmt"
	"log"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/services"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建脚本服务
	scriptService := services.NewScriptService(database.GetDB())

	return // 后面的不执行

	// 创建简单测试脚本（不在创建时引用message对象）
	scripts := []services.CreateScriptRequest{
		{
			Name:        "简单日志脚本",
			Description: "记录脚本执行日志",
			Type:        "message_filter",
			Priority:    100,
			Enabled:     true,
			Content: `-- 简单日志脚本
log("info", "脚本开始执行")

-- 检查消息是否存在
if message and message.content then
    log("info", "收到消息: " .. message.content)

    -- 检查是否包含测试关键词
    if message.content:find("测试") then
        log("warn", "检测到测试消息")
        return true
    end
else
    log("warn", "消息对象不存在")
end

log("info", "脚本执行完成")
return true`,
		},
		{
			Name:        "自动问候脚本",
			Description: "自动回复问候消息",
			Type:        "message_handler",
			Priority:    200,
			Enabled:     true,
			Content: `-- 自动问候脚本
log("info", "问候脚本开始执行")

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id

log("info", "处理用户 " .. user_id .. " 的消息: " .. content)

-- 简单的问候回复
if content == "你好" or content == "hello" or content == "hi" then
    log("info", "匹配到问候语，准备回复")
    -- 注意：这里只是记录，实际发送消息需要在消息处理流程中
    return "检测到问候语，应该回复欢迎消息"
end

-- 帮助命令
if content == "帮助" or content == "help" then
    log("info", "匹配到帮助命令")
    return "检测到帮助命令，应该回复帮助信息"
end

log("info", "未匹配到特定规则")
return nil`,
		},
	}

	// 创建脚本
	ctx := context.Background()
	for _, script := range scripts {
		createdScript, err := scriptService.CreateScript(ctx, &script)
		if err != nil {
			log.Printf("创建脚本失败 %s: %v", script.Name, err)
			continue
		}
		fmt.Printf("✅ 创建脚本成功: %s (ID: %s)\n", createdScript.Name, createdScript.ID)
	}

	fmt.Println("\n🎉 简单测试脚本创建完成！")
}
