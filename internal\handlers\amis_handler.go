package handlers

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

// AmisConfigHandler Amis配置处理器
// 思路：从JSON文件读取Amis管理后台配置，支持模块化管理
type AmisConfigHandler struct {
	configDir string // 配置文件目录路径
}

// NewAmisConfigHandler 创建Amis配置处理器
// 参数：
//   - configDir: 配置文件目录路径，例如 "configs/amis"
//
// 返回：配置处理器实例
func NewAmisConfigHandler(configDir string) *AmisConfigHandler {
	return &AmisConfigHandler{
		configDir: configDir,
	}
}

// GetAmisConfig 获取Amis管理后台配置
// 思路：直接返回app.json的内容，支持标准API响应格式
// 使用例子：GET /api/v1/admin/amis-config
func (h *AmisConfigHandler) GetAmisConfig(c *gin.Context) {
	// 读取主应用配置
	appConfig, err := h.loadJSONConfig("app.json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": 1,
			"msg":    fmt.Sprintf("加载应用配置失败: %v", err),
			"data":   nil,
		})
		return
	}

	c.JSON(http.StatusOK, appConfig)
}

// loadJSONConfig 加载JSON配置文件
// 参数：
//   - filename: 配置文件名，例如 "app.json"
//
// 返回：解析后的JSON对象和错误信息
func (h *AmisConfigHandler) loadJSONConfig(filename string) (interface{}, error) {
	filePath := filepath.Join(h.configDir, filename)

	// 读取文件内容
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败 %s: %v", filePath, err)
	}

	// 解析JSON
	var config interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析JSON失败 %s: %v", filePath, err)
	}

	return config, nil
}

// loadAllPageConfigs 加载所有页面配置
// 思路：遍历pages目录下的所有JSON文件，加载页面配置
// 返回：页面配置数组和错误信息
func (h *AmisConfigHandler) loadAllPageConfigs() ([]interface{}, error) {
	pagesDir := filepath.Join(h.configDir, "pages")

	// 定义页面文件列表（按导航顺序）
	pageFiles := []string{
		"dashboard.json",
		"users.json",
		"messages.json",
		"knowledge.json",
		"scripts.json",
		"platforms.json",
		"monitor.json",
		"settings.json",
	}

	var pages []interface{}

	// 逐个加载页面配置
	for _, filename := range pageFiles {
		filePath := filepath.Join(pagesDir, filename)

		// 读取文件内容
		data, err := ioutil.ReadFile(filePath)
		if err != nil {
			// 如果文件不存在，跳过但记录警告
			fmt.Printf("警告: 页面配置文件不存在 %s: %v\n", filePath, err)
			continue
		}

		// 解析JSON
		var pageConfig interface{}
		if err := json.Unmarshal(data, &pageConfig); err != nil {
			return nil, fmt.Errorf("解析页面配置失败 %s: %v", filePath, err)
		}

		pages = append(pages, pageConfig)
	}

	return pages, nil
}

// GetPageConfig 获取单个页面配置
// 思路：根据页面名称返回对应的页面配置，支持标准API响应格式
// 使用例子：GET /api/v1/admin/amis-config/pages/dashboard
func (h *AmisConfigHandler) GetPageConfig(c *gin.Context) {
	pageName := c.Param("page")
	if pageName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 1,
			"msg":    "页面名称不能为空",
			"data":   nil,
		})
		return
	}

	// 构建文件路径
	filename := fmt.Sprintf("%s.json", pageName)
	pageConfig, err := h.loadJSONConfig(filepath.Join("pages", filename))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"status": 1,
			"msg":    fmt.Sprintf("页面配置不存在: %s", pageName),
			"data":   nil,
		})
		return
	}

	// 如果页面配置已经包含schema字段，直接返回schema部分
	if pageConfigMap, ok := pageConfig.(map[string]interface{}); ok {
		if schema, exists := pageConfigMap["schema"]; exists {
			c.JSON(http.StatusOK, gin.H{
				"status": 0,
				"msg":    "",
				"data":   schema,
			})
			return
		}
	}

	// 否则返回整个配置
	c.JSON(http.StatusOK, gin.H{
		"status": 0,
		"msg":    "",
		"data":   pageConfig,
	})
}

// UpdatePageConfig 更新页面配置
// 思路：接收JSON数据并保存到对应的页面配置文件
// 使用例子：PUT /api/v1/admin/amis-config/pages/dashboard
func (h *AmisConfigHandler) UpdatePageConfig(c *gin.Context) {
	pageName := c.Param("page")
	if pageName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "页面名称不能为空",
		})
		return
	}

	// 读取请求体
	var pageConfig interface{}
	if err := c.ShouldBindJSON(&pageConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("无效的JSON数据: %v", err),
		})
		return
	}

	// 构建文件路径
	filename := fmt.Sprintf("%s.json", pageName)
	filePath := filepath.Join(h.configDir, "pages", filename)

	// 将配置转换为格式化的JSON
	data, err := json.MarshalIndent(pageConfig, "", "  ")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("序列化JSON失败: %v", err),
		})
		return
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, data, 0o644); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("保存配置文件失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("页面配置 %s 更新成功", pageName),
	})
}
