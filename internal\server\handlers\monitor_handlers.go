package handlers

import (
	"net/http"
	"runtime"
	"time"

	"aike_go/internal/adapters"
	"aike_go/internal/services"
	"github.com/gin-gonic/gin"
)

// MonitorHandlers 监控处理器
// 思路：将监控相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewMonitorHandlers(adapterManager, wsService)
type MonitorHandlers struct {
	adapterManager *adapters.AdapterManager
	wsService      *services.WebSocketService
	startTime      time.Time
}

// NewMonitorHandlers 创建监控处理器
// 思路：初始化监控处理器，注入相关服务依赖
// 使用例子：handlers := NewMonitorHandlers(adapterManager, wsService, startTime)
func NewMonitorHandlers(adapterManager *adapters.AdapterManager, wsService *services.WebSocketService, startTime time.Time) *MonitorHandlers {
	return &MonitorHandlers{
		adapterManager: adapterManager,
		wsService:      wsService,
		startTime:      startTime,
	}
}

// GetMetrics 获取系统指标处理器
// 思路：返回系统的基本性能指标和运行状态
// 使用例子：GET /api/v1/monitor/metrics
func (h *MonitorHandlers) GetMetrics(c *gin.Context) {
	// 获取内存统计信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 获取适配器统计信息
	adapterStats := h.adapterManager.GetAdapterStats()

	// 获取WebSocket统计信息
	wsStats := h.wsService.GetStats()

	// 计算运行时间
	uptime := time.Since(h.startTime)

	// 构建指标数据
	metrics := map[string]interface{}{
		"system": gin.H{
			"uptime_seconds":    uptime.Seconds(),
			"start_time":        h.startTime.Unix(),
			"current_time":      time.Now().Unix(),
			"goroutines":        runtime.NumGoroutine(),
			"cpu_cores":         runtime.NumCPU(),
			"go_version":        runtime.Version(),
		},
		"memory": gin.H{
			"alloc_bytes":        memStats.Alloc,
			"total_alloc_bytes":  memStats.TotalAlloc,
			"sys_bytes":          memStats.Sys,
			"heap_alloc_bytes":   memStats.HeapAlloc,
			"heap_sys_bytes":     memStats.HeapSys,
			"heap_idle_bytes":    memStats.HeapIdle,
			"heap_inuse_bytes":   memStats.HeapInuse,
			"heap_released_bytes": memStats.HeapReleased,
			"heap_objects":       memStats.HeapObjects,
			"stack_inuse_bytes":  memStats.StackInuse,
			"stack_sys_bytes":    memStats.StackSys,
			"gc_runs":            memStats.NumGC,
			"gc_pause_total_ns":  memStats.PauseTotalNs,
		},
		"adapters": gin.H{
			"total_adapters":   adapterStats.TotalAdapters,
			"running_adapters": adapterStats.RunningAdapters,
			"total_messages":   adapterStats.TotalMessages,
			"total_errors":     adapterStats.TotalErrors,
			"last_update":      adapterStats.LastUpdateTime.Unix(),
		},
		"websocket": gin.H{
			"total_connections":  wsStats.TotalConnections,
			"active_connections": wsStats.ActiveConnections,
			"messages_sent":      wsStats.MessagesSent,
			"messages_received":  wsStats.MessagesReceived,
			"bytes_sent":         wsStats.BytesSent,
			"bytes_received":     wsStats.BytesReceived,
			"connection_errors":  wsStats.ConnectionErrors,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取系统指标成功",
		"data":    metrics,
	})
}

// GetPathMetrics 获取路径指标处理器
// 思路：返回各个API路径的访问统计信息
// 使用例子：GET /api/v1/monitor/metrics/paths
func (h *MonitorHandlers) GetPathMetrics(c *gin.Context) {
	// 这里应该从中间件或监控服务中获取路径统计信息
	// 暂时返回模拟数据，实际实现需要集成路径监控中间件
	pathMetrics := map[string]interface{}{
		"/api/v1/platforms/": gin.H{
			"total_requests":   150,
			"success_requests": 148,
			"error_requests":   2,
			"avg_response_time_ms": 45.2,
			"max_response_time_ms": 120,
			"min_response_time_ms": 12,
			"last_request_at":      time.Now().Add(-5*time.Minute).Unix(),
		},
		"/api/v1/messages/send": gin.H{
			"total_requests":   89,
			"success_requests": 87,
			"error_requests":   2,
			"avg_response_time_ms": 78.5,
			"max_response_time_ms": 200,
			"min_response_time_ms": 25,
			"last_request_at":      time.Now().Add(-2*time.Minute).Unix(),
		},
		"/api/v1/knowledge/search": gin.H{
			"total_requests":   234,
			"success_requests": 230,
			"error_requests":   4,
			"avg_response_time_ms": 156.8,
			"max_response_time_ms": 500,
			"min_response_time_ms": 45,
			"last_request_at":      time.Now().Add(-1*time.Minute).Unix(),
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取路径指标成功",
		"data":    pathMetrics,
	})
}

// ResetMetrics 重置指标处理器
// 思路：重置系统的统计指标，用于测试或维护
// 使用例子：POST /api/v1/monitor/metrics/reset
func (h *MonitorHandlers) ResetMetrics(c *gin.Context) {
	var req struct {
		ResetAdapters  bool `json:"reset_adapters"`  // 是否重置适配器指标
		ResetWebSocket bool `json:"reset_websocket"` // 是否重置WebSocket指标
		ResetPaths     bool `json:"reset_paths"`     // 是否重置路径指标
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	resetItems := make([]string, 0)

	// 重置适配器指标
	if req.ResetAdapters {
		// 这里应该调用适配器管理器的重置方法
		// h.adapterManager.ResetStats()
		resetItems = append(resetItems, "adapters")
	}

	// 重置WebSocket指标
	if req.ResetWebSocket {
		err := h.wsService.ResetStats()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "重置WebSocket指标失败",
				"details": err.Error(),
			})
			return
		}
		resetItems = append(resetItems, "websocket")
	}

	// 重置路径指标
	if req.ResetPaths {
		// 这里应该调用路径监控中间件的重置方法
		resetItems = append(resetItems, "paths")
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "指标重置成功",
		"data": gin.H{
			"reset_items": resetItems,
			"reset_at":    time.Now().Unix(),
		},
	})
}

// GetDetailedHealth 详细健康检查处理器
// 思路：返回系统各个组件的详细健康状态
// 使用例子：GET /api/v1/monitor/health/detailed
func (h *MonitorHandlers) GetDetailedHealth(c *gin.Context) {
	// 检查适配器健康状态
	adapterStats := h.adapterManager.GetAdapterStats()
	adapterHealth := "healthy"
	if adapterStats.TotalErrors > 0 || adapterStats.RunningAdapters < adapterStats.TotalAdapters {
		adapterHealth = "degraded"
	}

	// 检查WebSocket健康状态
	wsStats := h.wsService.GetStats()
	wsHealth := "healthy"
	if wsStats.ConnectionErrors > 10 { // 假设错误阈值为10
		wsHealth = "degraded"
	}

	// 检查内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	memoryHealth := "healthy"
	if memStats.Alloc > 500*1024*1024 { // 假设内存阈值为500MB
		memoryHealth = "warning"
	}
	if memStats.Alloc > 1024*1024*1024 { // 假设内存阈值为1GB
		memoryHealth = "critical"
	}

	// 计算总体健康状态
	overallHealth := "healthy"
	if adapterHealth == "degraded" || wsHealth == "degraded" || memoryHealth == "warning" {
		overallHealth = "degraded"
	}
	if memoryHealth == "critical" {
		overallHealth = "critical"
	}

	healthData := map[string]interface{}{
		"overall": gin.H{
			"status":     overallHealth,
			"checked_at": time.Now().Unix(),
			"uptime":     time.Since(h.startTime).Seconds(),
		},
		"components": gin.H{
			"adapters": gin.H{
				"status":           adapterHealth,
				"total_adapters":   adapterStats.TotalAdapters,
				"running_adapters": adapterStats.RunningAdapters,
				"total_errors":     adapterStats.TotalErrors,
				"last_update":      adapterStats.LastUpdateTime.Unix(),
			},
			"websocket": gin.H{
				"status":             wsHealth,
				"active_connections": wsStats.ActiveConnections,
				"connection_errors":  wsStats.ConnectionErrors,
				"messages_sent":      wsStats.MessagesSent,
			},
			"memory": gin.H{
				"status":      memoryHealth,
				"alloc_mb":    float64(memStats.Alloc) / 1024 / 1024,
				"sys_mb":      float64(memStats.Sys) / 1024 / 1024,
				"gc_runs":     memStats.NumGC,
				"goroutines":  runtime.NumGoroutine(),
			},
		},
	}

	// 根据健康状态设置HTTP状态码
	statusCode := http.StatusOK
	if overallHealth == "degraded" {
		statusCode = http.StatusServiceUnavailable
	} else if overallHealth == "critical" {
		statusCode = http.StatusInternalServerError
	}

	c.JSON(statusCode, gin.H{
		"message": "详细健康检查完成",
		"data":    healthData,
	})
}
