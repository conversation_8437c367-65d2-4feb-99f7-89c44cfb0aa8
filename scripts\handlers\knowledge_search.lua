-- 知识库智能搜索处理器
-- 专门处理知识库相关的查询和搜索

if not message or not message.content then
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id

-- 知识库相关关键词检测
local kb_keywords = {
    "文档", "帮助文档", "说明书", "手册", "教程",
    "怎么", "如何", "什么是", "为什么", "在哪里",
    "document", "help", "manual", "tutorial", "how to",
    "what is", "where is", "why", "how"
}

local is_kb_query = false
for _, keyword in ipairs(kb_keywords) do
    if content:lower():find(keyword:lower()) then
        is_kb_query = true
        break
    end
end

-- 如果不是知识库查询，跳过
if not is_kb_query then
    return nil
end

log("info", "检测到知识库查询: " .. content)

-- 执行知识库搜索
local search_result = call_api("POST", "/api/v1/knowledge/search", {
    query = content,
    limit = 5,
    threshold = 0.5
})

if search_result and search_result.success then
    local results = search_result.data
    
    if #results == 0 then
        send_message(user_id, "📚 抱歉，没有找到相关的文档信息。\n您可以：\n• 尝试其他关键词\n• 发送'人工客服'获取帮助")
        return "知识库搜索无结果"
    end
    
    -- 构建搜索结果回复
    local reply = "📚 找到以下相关文档：\n\n"
    
    for i, result in ipairs(results) do
        reply = reply .. string.format("%d. %s\n", i, result.title)
        if result.score > 0.8 then
            -- 高相似度，直接显示内容摘要
            local summary = string.sub(result.content, 1, 100)
            reply = reply .. "   💡 " .. summary .. "...\n"
        end
        reply = reply .. "\n"
    end
    
    -- 如果有高相似度结果，提供详细内容
    local best_match = results[1]
    if best_match.score > 0.9 then
        reply = reply .. "📖 最佳匹配内容：\n" .. best_match.content
    else
        reply = reply .. "💬 需要更详细信息？发送'人工客服'获取专业帮助"
    end
    
    send_message(user_id, reply)
    log("info", "知识库搜索成功，返回" .. #results .. "个结果")
    return "知识库搜索: " .. #results .. "个结果"
    
else
    send_message(user_id, "📚 知识库暂时无法访问，请稍后再试或联系人工客服。")
    log("error", "知识库API调用失败")
    return "知识库搜索失败"
end
