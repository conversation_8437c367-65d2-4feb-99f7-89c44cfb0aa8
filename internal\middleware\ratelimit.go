package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 限流器接口
// 思路：定义限流器的通用接口，支持不同的限流算法
type RateLimiter interface {
	Allow(key string) bool
	Reset(key string)
	GetStats(key string) *RateLimitStats
}

// RateLimitStats 限流统计信息
// 思路：提供限流的统计数据
type RateLimitStats struct {
	Key           string    `json:"key"`            // 限流键
	RequestCount  int64     `json:"request_count"`  // 请求总数
	AllowedCount  int64     `json:"allowed_count"`  // 允许的请求数
	BlockedCount  int64     `json:"blocked_count"`  // 被阻止的请求数
	LastRequest   time.Time `json:"last_request"`   // 最后请求时间
	WindowStart   time.Time `json:"window_start"`   // 窗口开始时间
	WindowEnd     time.Time `json:"window_end"`     // 窗口结束时间
	Limit         int       `json:"limit"`          // 限制数量
	Remaining     int       `json:"remaining"`      // 剩余数量
}

// TokenBucketLimiter 令牌桶限流器
// 思路：使用令牌桶算法实现限流，支持突发流量
type TokenBucketLimiter struct {
	capacity int           // 桶容量
	rate     time.Duration // 令牌生成速率
	buckets  map[string]*bucket
	mutex    sync.RWMutex
}

// bucket 令牌桶
type bucket struct {
	tokens    int       // 当前令牌数
	lastRefill time.Time // 最后填充时间
	stats     *RateLimitStats
}

// NewTokenBucketLimiter 创建令牌桶限流器
// 思路：初始化令牌桶限流器
// 使用例子：limiter := NewTokenBucketLimiter(100, time.Minute) // 每分钟100个请求
func NewTokenBucketLimiter(capacity int, rate time.Duration) *TokenBucketLimiter {
	return &TokenBucketLimiter{
		capacity: capacity,
		rate:     rate,
		buckets:  make(map[string]*bucket),
	}
}

// Allow 检查是否允许请求
// 思路：从令牌桶中取出令牌，如果有令牌则允许请求
func (t *TokenBucketLimiter) Allow(key string) bool {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	now := time.Now()
	b, exists := t.buckets[key]
	
	if !exists {
		b = &bucket{
			tokens:    t.capacity,
			lastRefill: now,
			stats: &RateLimitStats{
				Key:         key,
				WindowStart: now,
				WindowEnd:   now.Add(t.rate),
				Limit:       t.capacity,
			},
		}
		t.buckets[key] = b
	}

	// 填充令牌
	t.refillTokens(b, now)

	// 更新统计
	b.stats.RequestCount++
	b.stats.LastRequest = now

	// 检查是否有令牌
	if b.tokens > 0 {
		b.tokens--
		b.stats.AllowedCount++
		b.stats.Remaining = b.tokens
		return true
	}

	b.stats.BlockedCount++
	b.stats.Remaining = 0
	return false
}

// refillTokens 填充令牌
// 思路：根据时间间隔计算应该添加的令牌数量
func (t *TokenBucketLimiter) refillTokens(b *bucket, now time.Time) {
	elapsed := now.Sub(b.lastRefill)
	tokensToAdd := int(elapsed / t.rate)
	
	if tokensToAdd > 0 {
		b.tokens += tokensToAdd
		if b.tokens > t.capacity {
			b.tokens = t.capacity
		}
		b.lastRefill = now
	}
}

// Reset 重置限流器
func (t *TokenBucketLimiter) Reset(key string) {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	delete(t.buckets, key)
}

// GetStats 获取统计信息
func (t *TokenBucketLimiter) GetStats(key string) *RateLimitStats {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	
	if b, exists := t.buckets[key]; exists {
		// 返回副本
		stats := *b.stats
		return &stats
	}
	return nil
}

// SlidingWindowLimiter 滑动窗口限流器
// 思路：使用滑动窗口算法实现更精确的限流
type SlidingWindowLimiter struct {
	limit    int           // 限制数量
	window   time.Duration // 时间窗口
	windows  map[string]*slidingWindow
	mutex    sync.RWMutex
}

// slidingWindow 滑动窗口
type slidingWindow struct {
	requests []time.Time
	stats    *RateLimitStats
}

// NewSlidingWindowLimiter 创建滑动窗口限流器
// 思路：初始化滑动窗口限流器
// 使用例子：limiter := NewSlidingWindowLimiter(100, time.Minute) // 每分钟100个请求
func NewSlidingWindowLimiter(limit int, window time.Duration) *SlidingWindowLimiter {
	return &SlidingWindowLimiter{
		limit:   limit,
		window:  window,
		windows: make(map[string]*slidingWindow),
	}
}

// Allow 检查是否允许请求
func (s *SlidingWindowLimiter) Allow(key string) bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	w, exists := s.windows[key]
	
	if !exists {
		w = &slidingWindow{
			requests: make([]time.Time, 0),
			stats: &RateLimitStats{
				Key:         key,
				WindowStart: now,
				WindowEnd:   now.Add(s.window),
				Limit:       s.limit,
			},
		}
		s.windows[key] = w
	}

	// 清理过期请求
	s.cleanExpiredRequests(w, now)

	// 更新统计
	w.stats.RequestCount++
	w.stats.LastRequest = now

	// 检查是否超过限制
	if len(w.requests) < s.limit {
		w.requests = append(w.requests, now)
		w.stats.AllowedCount++
		w.stats.Remaining = s.limit - len(w.requests)
		return true
	}

	w.stats.BlockedCount++
	w.stats.Remaining = 0
	return false
}

// cleanExpiredRequests 清理过期请求
func (s *SlidingWindowLimiter) cleanExpiredRequests(w *slidingWindow, now time.Time) {
	cutoff := now.Add(-s.window)
	validRequests := make([]time.Time, 0)
	
	for _, req := range w.requests {
		if req.After(cutoff) {
			validRequests = append(validRequests, req)
		}
	}
	
	w.requests = validRequests
}

// Reset 重置限流器
func (s *SlidingWindowLimiter) Reset(key string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	delete(s.windows, key)
}

// GetStats 获取统计信息
func (s *SlidingWindowLimiter) GetStats(key string) *RateLimitStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	if w, exists := s.windows[key]; exists {
		stats := *w.stats
		return &stats
	}
	return nil
}

// RateLimitConfig 限流配置
// 思路：配置不同路径的限流规则
type RateLimitConfig struct {
	Global    *RateLimitRule            `json:"global"`    // 全局限流
	PerIP     *RateLimitRule            `json:"per_ip"`    // 按IP限流
	PerUser   *RateLimitRule            `json:"per_user"`  // 按用户限流
	PerPath   map[string]*RateLimitRule `json:"per_path"`  // 按路径限流
}

// RateLimitRule 限流规则
type RateLimitRule struct {
	Limit    int           `json:"limit"`    // 限制数量
	Window   time.Duration `json:"window"`   // 时间窗口
	Algorithm string       `json:"algorithm"` // 算法类型：token_bucket, sliding_window
}

// RateLimitMiddleware 限流中间件
// 思路：根据配置对不同维度进行限流
// 使用例子：router.Use(middleware.RateLimitMiddleware(config))
func RateLimitMiddleware(config *RateLimitConfig) gin.HandlerFunc {
	var globalLimiter, ipLimiter, userLimiter RateLimiter
	pathLimiters := make(map[string]RateLimiter)

	// 初始化限流器
	if config.Global != nil {
		globalLimiter = createLimiter(config.Global)
	}
	if config.PerIP != nil {
		ipLimiter = createLimiter(config.PerIP)
	}
	if config.PerUser != nil {
		userLimiter = createLimiter(config.PerUser)
	}
	for path, rule := range config.PerPath {
		pathLimiters[path] = createLimiter(rule)
	}

	return func(c *gin.Context) {
		// 检查全局限流
		if globalLimiter != nil && !globalLimiter.Allow("global") {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "全局请求频率过高，请稍后再试",
				"code":  "GLOBAL_RATE_LIMIT_EXCEEDED",
			})
			c.Abort()
			return
		}

		// 检查IP限流
		if ipLimiter != nil {
			clientIP := c.ClientIP()
			if !ipLimiter.Allow(clientIP) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": "IP请求频率过高，请稍后再试",
					"code":  "IP_RATE_LIMIT_EXCEEDED",
				})
				c.Abort()
				return
			}
		}

		// 检查用户限流
		if userLimiter != nil {
			userID := getUserID(c)
			if userID != "" && !userLimiter.Allow(userID) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": "用户请求频率过高，请稍后再试",
					"code":  "USER_RATE_LIMIT_EXCEEDED",
				})
				c.Abort()
				return
			}
		}

		// 检查路径限流
		path := c.Request.URL.Path
		if limiter, exists := pathLimiters[path]; exists {
			key := fmt.Sprintf("%s:%s", path, c.ClientIP())
			if !limiter.Allow(key) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": "路径请求频率过高，请稍后再试",
					"code":  "PATH_RATE_LIMIT_EXCEEDED",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// createLimiter 根据规则创建限流器
func createLimiter(rule *RateLimitRule) RateLimiter {
	switch rule.Algorithm {
	case "sliding_window":
		return NewSlidingWindowLimiter(rule.Limit, rule.Window)
	default:
		return NewTokenBucketLimiter(rule.Limit, rule.Window)
	}
}

// GetDefaultRateLimitConfig 获取默认限流配置
// 思路：提供合理的默认限流配置
func GetDefaultRateLimitConfig() *RateLimitConfig {
	return &RateLimitConfig{
		Global: &RateLimitRule{
			Limit:     1000,
			Window:    time.Minute,
			Algorithm: "token_bucket",
		},
		PerIP: &RateLimitRule{
			Limit:     100,
			Window:    time.Minute,
			Algorithm: "sliding_window",
		},
		PerUser: &RateLimitRule{
			Limit:     200,
			Window:    time.Minute,
			Algorithm: "token_bucket",
		},
		PerPath: map[string]*RateLimitRule{
			"/api/v1/messages/send": {
				Limit:     10,
				Window:    time.Minute,
				Algorithm: "sliding_window",
			},
			"/api/v1/knowledge/search": {
				Limit:     30,
				Window:    time.Minute,
				Algorithm: "token_bucket",
			},
		},
	}
}
