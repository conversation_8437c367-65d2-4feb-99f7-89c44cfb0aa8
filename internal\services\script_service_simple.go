package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"aike_go/internal/interfaces"
	"aike_go/internal/models"

	"gorm.io/gorm"
)

// ScriptService 脚本管理服务
// 思路：避免循环导入，只提供基本的CRUD功能，并管理插件
type ScriptService struct {
	db            *gorm.DB
	luaEngine     interfaces.ScriptEngine
	pluginManager interface{} // 插件管理器，避免循环导入使用interface{}
}

// NewScriptService 创建脚本服务
// 思路：初始化脚本服务，不依赖scripting包
func NewScriptService(db *gorm.DB) *ScriptService {
	return &ScriptService{
		db: db,
		// luaEngine将在适配器中注入
	}
}

// SetLuaEngine 设置Lua引擎
// 思路：依赖注入，避免循环导入
func (s *ScriptService) SetLuaEngine(engine interfaces.ScriptEngine) {
	s.luaEngine = engine
}

// SetPluginManager 设置插件管理器
// 思路：由适配器注入插件管理器，避免循环依赖
func (s *ScriptService) SetPluginManager(manager interface{}) {
	s.pluginManager = manager
	log.Printf("插件管理器已设置到脚本服务")
}

// CreateScript 创建脚本
// 思路：创建新的Lua脚本并保存到数据库
func (s *ScriptService) CreateScript(ctx context.Context, req *CreateScriptRequest) (*interfaces.Script, error) {
	script := &interfaces.Script{
		ID:          generateScriptID(),
		Name:        req.Name,
		Description: req.Description,
		Content:     req.Content,
		Type:        req.Type,
		Priority:    req.Priority,
		Enabled:     req.Enabled,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 保存到数据库
	scriptModel := models.Script{
		ID:          script.ID,
		Name:        script.Name,
		Description: script.Description,
		Content:     script.Content,
		Type:        script.Type,
		Priority:    script.Priority,
		Enabled:     script.Enabled,
		CreatedAt:   script.CreatedAt,
		UpdatedAt:   script.UpdatedAt,
	}

	if err := s.db.WithContext(ctx).Create(&scriptModel).Error; err != nil {
		return nil, fmt.Errorf("保存脚本失败: %w", err)
	}

	log.Printf("创建脚本: %s (%s)", script.Name, script.ID)
	return script, nil
}

// GetScript 获取脚本
// 思路：根据ID获取脚本详情
func (s *ScriptService) GetScript(ctx context.Context, scriptID string) (*interfaces.Script, error) {
	var scriptModel models.Script
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&scriptModel).Error; err != nil {
		return nil, fmt.Errorf("脚本不存在: %w", err)
	}

	script := &interfaces.Script{
		ID:          scriptModel.ID,
		Name:        scriptModel.Name,
		Description: scriptModel.Description,
		Content:     scriptModel.Content,
		Type:        scriptModel.Type,
		Priority:    scriptModel.Priority,
		Enabled:     scriptModel.Enabled,
		CreatedAt:   scriptModel.CreatedAt,
		UpdatedAt:   scriptModel.UpdatedAt,
	}

	return script, nil
}

// UpdateScript 更新脚本
// 思路：更新现有脚本的信息
func (s *ScriptService) UpdateScript(ctx context.Context, scriptID string, req *UpdateScriptRequest) (*interfaces.Script, error) {
	// 检查脚本是否存在
	var scriptModel models.Script
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&scriptModel).Error; err != nil {
		return nil, fmt.Errorf("脚本不存在: %w", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Priority != nil {
		updates["priority"] = *req.Priority
	}
	if req.Enabled != nil {
		updates["enabled"] = *req.Enabled
	}
	updates["updated_at"] = time.Now()

	// 执行更新
	if err := s.db.WithContext(ctx).Model(&scriptModel).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新脚本失败: %w", err)
	}

	// 重新获取更新后的脚本
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&scriptModel).Error; err != nil {
		return nil, fmt.Errorf("获取更新后的脚本失败: %w", err)
	}

	script := &interfaces.Script{
		ID:          scriptModel.ID,
		Name:        scriptModel.Name,
		Description: scriptModel.Description,
		Content:     scriptModel.Content,
		Type:        scriptModel.Type,
		Priority:    scriptModel.Priority,
		Enabled:     scriptModel.Enabled,
		CreatedAt:   scriptModel.CreatedAt,
		UpdatedAt:   scriptModel.UpdatedAt,
	}

	log.Printf("更新脚本: %s (%s)", script.Name, script.ID)
	return script, nil
}

// GetAllScripts 获取所有脚本
// 思路：获取数据库中的所有脚本
func (s *ScriptService) GetAllScripts(ctx context.Context) ([]*interfaces.Script, error) {
	var scripts []models.Script
	if err := s.db.WithContext(ctx).Find(&scripts).Error; err != nil {
		return nil, fmt.Errorf("查询脚本失败: %w", err)
	}

	var scriptList []*interfaces.Script
	for _, scriptModel := range scripts {
		script := &interfaces.Script{
			ID:          scriptModel.ID,
			Name:        scriptModel.Name,
			Description: scriptModel.Description,
			Content:     scriptModel.Content,
			Type:        scriptModel.Type,
			Priority:    scriptModel.Priority,
			Enabled:     scriptModel.Enabled,
			CreatedAt:   scriptModel.CreatedAt,
			UpdatedAt:   scriptModel.UpdatedAt,
		}
		scriptList = append(scriptList, script)
	}

	return scriptList, nil
}

// DeleteScript 删除脚本
// 思路：从数据库中删除脚本
func (s *ScriptService) DeleteScript(ctx context.Context, scriptID string) error {
	// 检查脚本是否存在
	var scriptModel models.Script
	if err := s.db.WithContext(ctx).Where("id = ?", scriptID).First(&scriptModel).Error; err != nil {
		return fmt.Errorf("脚本不存在: %w", err)
	}

	// 软删除
	if err := s.db.WithContext(ctx).Delete(&scriptModel).Error; err != nil {
		return fmt.Errorf("删除脚本失败: %w", err)
	}

	log.Printf("删除脚本: %s", scriptID)
	return nil
}

// ListScripts 列出脚本
// 思路：分页查询脚本列表
func (s *ScriptService) ListScripts(ctx context.Context, req *ListScriptsRequest) (*ListScriptsResponse, error) {
	query := s.db.WithContext(ctx).Model(&models.Script{})

	// 添加过滤条件
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Enabled != nil {
		query = query.Where("enabled = ?", *req.Enabled)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("计算脚本总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	var scripts []models.Script
	if err := query.Offset(offset).Limit(req.PageSize).Order("priority ASC, created_at DESC").Find(&scripts).Error; err != nil {
		return nil, fmt.Errorf("查询脚本失败: %w", err)
	}

	// 转换为脚本对象
	var scriptList []*interfaces.Script
	for _, scriptModel := range scripts {
		script := &interfaces.Script{
			ID:          scriptModel.ID,
			Name:        scriptModel.Name,
			Description: scriptModel.Description,
			Content:     scriptModel.Content,
			Type:        scriptModel.Type,
			Priority:    scriptModel.Priority,
			Enabled:     scriptModel.Enabled,
			CreatedAt:   scriptModel.CreatedAt,
			UpdatedAt:   scriptModel.UpdatedAt,
		}
		scriptList = append(scriptList, script)
	}

	return &ListScriptsResponse{
		Scripts: scriptList,
		Total:   total,
		Page:    req.Page,
		Size:    req.PageSize,
	}, nil
}

// ListScriptsResponse 列出脚本响应
// 思路：分页结果，使用interfaces类型
type ListScriptsResponse struct {
	Scripts []*interfaces.Script `json:"scripts"`
	Total   int64                `json:"total"`
	Page    int                  `json:"page"`
	Size    int                  `json:"size"`
}

// CreateScriptRequest 创建脚本请求
// 思路：创建脚本的请求结构
type CreateScriptRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Content     string `json:"content" binding:"required"`
	Type        string `json:"type" binding:"required"`
	Priority    int    `json:"priority"`
	Enabled     bool   `json:"enabled"`
}

// UpdateScriptRequest 更新脚本请求
// 思路：更新脚本的请求结构
type UpdateScriptRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Content     *string `json:"content"`
	Type        *string `json:"type"`
	Priority    *int    `json:"priority"`
	Enabled     *bool   `json:"enabled"`
}

// ExecuteScriptRequest 执行脚本请求
// 思路：执行脚本的请求结构
type ExecuteScriptRequest struct {
	ScriptID  string                      `json:"script_id" binding:"required"`
	Variables map[string]interface{}      `json:"variables"`
	Message   *interfaces.IncomingMessage `json:"message"`
	Context   map[string]interface{}      `json:"context"`
}

// ListScriptsRequest 列出脚本请求
// 思路：分页查询脚本的请求结构
type ListScriptsRequest struct {
	Type     string `form:"type"`
	Enabled  *bool  `form:"enabled"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
}

// generateScriptID 生成脚本ID
// 思路：生成唯一的脚本标识符
func generateScriptID() string {
	return fmt.Sprintf("script_%d", time.Now().UnixNano())
}
