package models

import (
	"time"
	"gorm.io/gorm"
)

// User 用户模型
// 思路：存储用户基本信息，支持多平台用户统一管理
// 使用例子：user := &User{PlatformID: "qq_123456", Platform: "qq", Nickname: "张三"}
type User struct {
	ID         uint           `gorm:"primarykey" json:"id"`                    // 主键ID
	CreatedAt  time.Time      `json:"created_at"`                              // 创建时间
	UpdatedAt  time.Time      `json:"updated_at"`                              // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`       // 软删除时间
	
	// 平台相关信息
	PlatformID string `gorm:"size:100;not null" json:"platform_id"`           // 平台用户ID（如QQ号、微信openid等）
	Platform   string `gorm:"size:20;not null" json:"platform"`               // 平台类型：qq, wechat, telegram, qianniu
	
	// 用户基本信息
	Nickname   string `gorm:"size:100" json:"nickname"`                       // 用户昵称
	Avatar     string `gorm:"size:500" json:"avatar"`                         // 头像URL
	Email      string `gorm:"size:100" json:"email"`                          // 邮箱
	Phone      string `gorm:"size:20" json:"phone"`                           // 手机号
	
	// 用户状态
	Status     string `gorm:"size:20;default:active" json:"status"`           // 用户状态：active, blocked, inactive
	IsVIP      bool   `gorm:"default:false" json:"is_vip"`                    // 是否VIP用户
	
	// 统计信息
	MessageCount int `gorm:"default:0" json:"message_count"`                  // 消息总数
	LastActiveAt *time.Time `json:"last_active_at"`                          // 最后活跃时间
	
	// 关联关系
	Messages []Message `gorm:"foreignKey:UserID" json:"messages,omitempty"`   // 用户的消息记录
}

// TableName 指定表名
// 思路：使用复数形式的表名，符合数据库命名规范
func (User) TableName() string {
	return "users"
}

// BeforeCreate GORM钩子：创建前处理
// 思路：在创建用户前设置默认值和验证数据
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.Status == "" {
		u.Status = "active"
	}
	if u.LastActiveAt == nil {
		now := time.Now()
		u.LastActiveAt = &now
	}
	return nil
}

// UpdateLastActive 更新最后活跃时间
// 思路：用户发送消息时调用此方法更新活跃时间
// 使用例子：user.UpdateLastActive(db)
func (u *User) UpdateLastActive(db *gorm.DB) error {
	now := time.Now()
	u.LastActiveAt = &now
	return db.Model(u).Update("last_active_at", now).Error
}

// IncrementMessageCount 增加消息计数
// 思路：用户发送消息时增加计数器
// 使用例子：user.IncrementMessageCount(db)
func (u *User) IncrementMessageCount(db *gorm.DB) error {
	return db.Model(u).Update("message_count", gorm.Expr("message_count + ?", 1)).Error
}

// IsActive 检查用户是否活跃
// 思路：根据用户状态判断是否可以正常使用服务
// 使用例子：if user.IsActive() { ... }
func (u *User) IsActive() bool {
	return u.Status == "active"
}

// GetPlatformKey 获取平台唯一标识
// 思路：组合平台类型和平台ID作为唯一标识
// 使用例子：key := user.GetPlatformKey() // 返回 "qq_123456"
func (u *User) GetPlatformKey() string {
	return u.Platform + "_" + u.PlatformID
}
