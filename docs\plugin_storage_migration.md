# 插件化存储系统迁移说明

## 📋 概述

本文档说明了从直接数据库存储到插件化存储系统的迁移过程。

## 🔄 架构变更

### **原来的存储方式**
```go
// 直接保存到数据库
userMessage := &models.Message{...}
if err := s.db.Create(userMessage).Error; err != nil {
    return fmt.Errorf("保存用户消息失败: %w", err)
}
```

### **新的插件化存储方式**
```lua
-- 通过Lua脚本插件自动存储
local message_data = {
    id = "msg_" .. os.time() .. "_" .. math.random(1000, 9999),
    user_id = user_id,
    platform = platform,
    content = message.content,
    message_type = message.message_type or "text",
    direction = "incoming",
    session_id = session_id,
}

local success, message_text = store_message(message_data)
```

## 🎯 迁移的好处

### **1. 灵活性**
- ✅ **多存储后端** - 支持数据库、文件、云存储等
- ✅ **动态切换** - 运行时切换存储方式
- ✅ **插件化配置** - 每个存储插件独立配置

### **2. 可扩展性**
- ✅ **新存储类型** - 轻松添加新的存储插件
- ✅ **自定义逻辑** - Lua脚本实现复杂存储逻辑
- ✅ **条件存储** - 根据消息内容决定是否存储

### **3. 管理便利性**
- ✅ **统一接口** - 通过命令管理所有存储插件
- ✅ **实时配置** - 无需重启即可修改存储配置
- ✅ **监控统计** - 实时查看存储状态和统计

## 📁 文件变更说明

### **修改的文件**

#### `internal/services/chat.go`
- **ProcessMessage方法** - 注释了直接数据库存储
- **HandleIncomingMessage方法** - 注释了直接数据库存储
- **保留功能** - 用户和会话管理仍然保留

#### 注释的代码块：
```go
// 原来的数据库保存逻辑已注释，现在使用插件化存储
// if err := s.db.Create(userMessage).Error; err != nil {
//     return nil, fmt.Errorf("保存用户消息失败: %w", err)
// }
```

### **新增的文件**

#### `internal/plugins/interfaces.go`
- 插件系统的核心接口定义
- ChatStoragePlugin接口
- 消息格式转换函数

#### `internal/plugins/storage/database_storage.go`
- 数据库存储插件实现
- 支持SQLite、MySQL、PostgreSQL等

#### `internal/plugins/manager.go`
- 插件管理器实现
- 插件生命周期管理
- 事件系统

#### `internal/scripting/plugin_api.go`
- Lua API实现
- 插件管理函数
- 存储操作函数

#### `scripts/plugins/chat_storage_manager.lua`
- 聊天记录存储管理脚本
- 自动存储逻辑
- 命令行接口

#### `scripts/plugins/plugin_config_manager.lua`
- 插件配置管理脚本
- 动态配置功能
- 批量操作支持

## 🚀 使用方法

### **1. 启动系统**
```bash
go run cmd/server/main.go
```

### **2. 查看插件状态**
```
/plugin list
/plugin stats
```

### **3. 管理存储插件**
```
/plugin enable database_storage
/plugin disable database_storage
/config get database_storage
```

### **4. 查看存储统计**
```
/plugin storage stats
```

### **5. 查询聊天记录**
```
/history 10
/search 关键词
```

## ⚙️ 配置说明

### **数据库存储插件配置**
```yaml
database_storage:
  table_name: "plugin_chat_messages"
  auto_migrate: true
  retention_days: 365
  backup_enabled: true
  backup_interval: "24h"
  compression: true
  encryption: false
```

### **配置修改命令**
```
/config set database_storage retention_days 30
/config set database_storage backup_enabled true
/config reload database_storage
```

## 🔧 开发指南

### **添加新的存储插件**

1. **实现插件接口**
```go
type MyStoragePlugin struct {
    // 插件实现
}

func (p *MyStoragePlugin) StoreMessage(ctx context.Context, message *plugins.ChatMessage) error {
    // 存储逻辑
}
```

2. **注册插件**
```go
pluginManager.RegisterPlugin(myPlugin)
```

3. **在Lua中使用**
```lua
enable_plugin("my_storage")
store_message(message_data)
```

### **自定义存储逻辑**

可以在Lua脚本中实现复杂的存储逻辑：

```lua
-- 条件存储示例
if message.content:find("重要") then
    -- 存储到高优先级存储
    store_message_priority(message_data, "high")
elseif message.content:find("临时") then
    -- 不存储临时消息
    log("info", "跳过临时消息存储")
else
    -- 正常存储
    store_message(message_data)
end
```

## 🔍 故障排除

### **常见问题**

#### **1. 插件未启用**
```
/plugin enable database_storage
```

#### **2. 存储失败**
```
/plugin stats
/config get database_storage
```

#### **3. 查看日志**
```
tail -f logs/server.log
```

#### **4. 重新加载插件**
```
/plugin reload database_storage
```

## 📊 性能对比

### **存储性能**
- **直接数据库** - 固定性能，无法优化
- **插件化存储** - 可选择最优存储方式，支持缓存、批量写入等优化

### **查询性能**
- **直接数据库** - 依赖SQL查询
- **插件化存储** - 支持索引优化、分布式查询等

### **扩展性**
- **直接数据库** - 受限于单一数据库
- **插件化存储** - 支持多存储后端、负载均衡

## 🎉 总结

插件化存储系统提供了：

- ✅ **更好的灵活性** - 支持多种存储方式
- ✅ **更强的扩展性** - 轻松添加新功能
- ✅ **更便捷的管理** - 统一的命令行接口
- ✅ **更好的性能** - 可选择最优存储策略

通过这次迁移，系统的存储能力得到了显著提升，为未来的功能扩展奠定了坚实基础。
