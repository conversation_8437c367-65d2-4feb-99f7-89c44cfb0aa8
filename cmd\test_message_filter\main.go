package main

import (
	"context"
	"fmt"
	"strings"

	"aike_go/internal/config"
	"aike_go/internal/services"
)

// 测试消息过滤功能
func main() {
	fmt.Println("🧪 测试消息过滤功能")

	// 1. 初始化配置
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type: "sqlite",
			DSN:  "data/aike.db",
		},
	}

	// 2. 创建服务（不需要真实数据库连接来测试过滤逻辑）
	chatService := services.NewChatService(cfg)

	// 4. 测试用例
	testCases := []struct {
		name     string
		content  string
		expected bool
		reason   string
	}{
		// 应该回复的消息
		{"客服关键词", "请问客服在吗？", true, "包含响应关键词"},
		{"帮助关键词", "需要帮助", true, "包含响应关键词"},
		{"问题关键词", "有个问题想咨询", true, "包含响应关键词"},
		{"问号消息", "这个怎么用？", true, "包含问号"},
		{"命令前缀", "/help", true, "命令前缀"},
		{"感叹号前缀", "!status", true, "命令前缀"},
		{"长消息工作时间", "我想了解一下你们的产品功能", true, "工作时间内，消息长度足够"},

		// 不应该回复的消息
		{"短消息", "哦", false, "消息过短"},
		{"纯表情", "😂😂😂", false, "纯表情消息"},
		{"纯标点", "。。。", false, "纯标点符号"},
		{"纯数字", "123456", false, "纯数字消息"},
		{"闲聊词汇", "哈哈哈", false, "包含忽略关键词"},
		{"点赞", "👍👍👍", false, "包含忽略关键词"},
		{"简单回应", "好的", false, "包含忽略关键词"},
		{"语气词", "嗯嗯", false, "包含忽略关键词"},
	}

	fmt.Println("\n📋 测试结果:")
	fmt.Println(strings.Repeat("=", 80))

	successCount := 0
	totalCount := len(testCases)

	for i, tc := range testCases {
		// 创建测试请求
		req := services.ProcessMessageRequest{
			Platform:    "test",
			PlatformID:  "test_user",
			Content:     tc.content,
			MessageType: "text",
		}

		// 调用过滤方法
		shouldReply, reason := chatService.ShouldReplyToMessage(req)

		// 检查结果
		passed := shouldReply == tc.expected
		if passed {
			successCount++
		}

		// 输出结果
		status := "✅ PASS"
		if !passed {
			status = "❌ FAIL"
		}

		fmt.Printf("%2d. %s %s\n", i+1, status, tc.name)
		fmt.Printf("    消息: \"%s\"\n", tc.content)
		fmt.Printf("    期望: %v, 实际: %v\n", tc.expected, shouldReply)
		fmt.Printf("    原因: %s\n", reason)
		fmt.Println()
	}

	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("📊 测试统计: %d/%d 通过 (%.1f%%)\n",
		successCount, totalCount, float64(successCount)/float64(totalCount)*100)

	if successCount == totalCount {
		fmt.Println("🎉 所有测试通过！")
	} else {
		fmt.Printf("⚠️  有 %d 个测试失败\n", totalCount-successCount)
	}

	// 5. 测试实际消息处理
	fmt.Println("\n🔄 测试实际消息处理:")
	fmt.Println(strings.Repeat("-", 50))

	testMessages := []string{
		"你好，我需要帮助",
		"哈哈哈",
		"请问这个产品怎么使用？",
		"👍",
		"/help",
	}

	ctx := context.Background()
	for i, content := range testMessages {
		req := services.ProcessMessageRequest{
			Platform:    "test",
			PlatformID:  fmt.Sprintf("user_%d", i+1),
			Content:     content,
			MessageType: "text",
		}

		fmt.Printf("\n%d. 处理消息: \"%s\"\n", i+1, content)

		response, err := chatService.ProcessMessage(ctx, req)
		if err != nil {
			fmt.Printf("   ❌ 处理失败: %v\n", err)
			continue
		}

		if response.Reply == "" {
			fmt.Printf("   ⏭️  消息被过滤，未生成回复\n")
		} else {
			fmt.Printf("   ✅ 生成回复: \"%s\"\n", response.Reply)
			fmt.Printf("   📊 Token使用: %d\n", response.TokensUsed)
		}
	}

	fmt.Println("\n✨ 测试完成！")
}
