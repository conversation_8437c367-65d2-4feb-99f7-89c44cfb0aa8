package plugins

import (
	"context"
	"fmt"
	"log"
	"sort"
	"sync"
	"time"
)

// DefaultPluginManager 默认插件管理器实现
// 思路：管理所有插件的注册、启用、禁用和配置
type DefaultPluginManager struct {
	plugins       map[string]Plugin
	configs       map[string]*PluginConfig
	eventHandlers []PluginEventHandler
	mutex         sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
}

// NewPluginManager 创建插件管理器
// 思路：初始化插件管理器实例
func NewPluginManager() *DefaultPluginManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &DefaultPluginManager{
		plugins:       make(map[string]Plugin),
		configs:       make(map[string]*PluginConfig),
		eventHandlers: make([]PluginEventHandler, 0),
		ctx:           ctx,
		cancel:        cancel,
	}
}

// RegisterPlugin 注册插件
// 思路：将插件添加到管理器中
func (pm *DefaultPluginManager) RegisterPlugin(plugin Plugin) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	name := plugin.GetName()
	if _, exists := pm.plugins[name]; exists {
		return fmt.Errorf("插件 %s 已存在", name)
	}

	pm.plugins[name] = plugin

	// 创建默认配置
	config := &PluginConfig{
		Name:        name,
		Version:     plugin.GetVersion(),
		Description: plugin.GetDescription(),
		Enabled:     plugin.IsEnabled(),
		Priority:    100,
		Config:      plugin.GetConfig(),
	}
	pm.configs[name] = config

	// 初始化插件
	if err := plugin.Initialize(pm.ctx); err != nil {
		delete(pm.plugins, name)
		delete(pm.configs, name)
		return fmt.Errorf("初始化插件 %s 失败: %w", name, err)
	}

	log.Printf("插件已注册: %s v%s", name, plugin.GetVersion())

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "registered",
		Plugin:    name,
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"version":     plugin.GetVersion(),
			"description": plugin.GetDescription(),
		},
	})

	return nil
}

// UnregisterPlugin 注销插件
// 思路：从管理器中移除插件并清理资源
func (pm *DefaultPluginManager) UnregisterPlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	// 清理插件资源
	if err := plugin.Cleanup(pm.ctx); err != nil {
		log.Printf("清理插件 %s 失败: %v", name, err)
	}

	delete(pm.plugins, name)
	delete(pm.configs, name)

	log.Printf("插件已注销: %s", name)

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "unregistered",
		Plugin:    name,
		Timestamp: time.Now(),
	})

	return nil
}

// GetPlugin 获取插件
func (pm *DefaultPluginManager) GetPlugin(name string) (Plugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return nil, fmt.Errorf("插件 %s 不存在", name)
	}

	return plugin, nil
}

// GetAllPlugins 获取所有插件
func (pm *DefaultPluginManager) GetAllPlugins() []Plugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]Plugin, 0, len(pm.plugins))
	for _, plugin := range pm.plugins {
		plugins = append(plugins, plugin)
	}

	// 按优先级排序
	sort.Slice(plugins, func(i, j int) bool {
		configI := pm.configs[plugins[i].GetName()]
		configJ := pm.configs[plugins[j].GetName()]
		return configI.Priority < configJ.Priority
	})

	return plugins
}

// GetEnabledPlugins 获取启用的插件
func (pm *DefaultPluginManager) GetEnabledPlugins() []Plugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]Plugin, 0)
	for _, plugin := range pm.plugins {
		if plugin.IsEnabled() {
			plugins = append(plugins, plugin)
		}
	}

	// 按优先级排序
	sort.Slice(plugins, func(i, j int) bool {
		configI := pm.configs[plugins[i].GetName()]
		configJ := pm.configs[plugins[j].GetName()]
		return configI.Priority < configJ.Priority
	})

	return plugins
}

// EnablePlugin 启用插件
func (pm *DefaultPluginManager) EnablePlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	if plugin.IsEnabled() {
		return fmt.Errorf("插件 %s 已启用", name)
	}

	if err := plugin.Enable(); err != nil {
		pm.emitEvent(&PluginEvent{
			Type:      "error",
			Plugin:    name,
			Timestamp: time.Now(),
			Error:     err.Error(),
		})
		return fmt.Errorf("启用插件 %s 失败: %w", name, err)
	}

	// 更新配置
	if config, exists := pm.configs[name]; exists {
		config.Enabled = true
	}

	log.Printf("插件已启用: %s", name)

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "enabled",
		Plugin:    name,
		Timestamp: time.Now(),
	})

	return nil
}

// DisablePlugin 禁用插件
func (pm *DefaultPluginManager) DisablePlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	if !plugin.IsEnabled() {
		return fmt.Errorf("插件 %s 已禁用", name)
	}

	if err := plugin.Disable(); err != nil {
		pm.emitEvent(&PluginEvent{
			Type:      "error",
			Plugin:    name,
			Timestamp: time.Now(),
			Error:     err.Error(),
		})
		return fmt.Errorf("禁用插件 %s 失败: %w", name, err)
	}

	// 更新配置
	if config, exists := pm.configs[name]; exists {
		config.Enabled = false
	}

	log.Printf("插件已禁用: %s", name)

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "disabled",
		Plugin:    name,
		Timestamp: time.Now(),
	})

	return nil
}

// GetPluginConfig 获取插件配置
func (pm *DefaultPluginManager) GetPluginConfig(name string) (map[string]interface{}, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return nil, fmt.Errorf("插件 %s 不存在", name)
	}

	return plugin.GetConfig(), nil
}

// SetPluginConfig 设置插件配置
func (pm *DefaultPluginManager) SetPluginConfig(name string, config map[string]interface{}) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	if err := plugin.SetConfig(config); err != nil {
		pm.emitEvent(&PluginEvent{
			Type:      "error",
			Plugin:    name,
			Timestamp: time.Now(),
			Error:     err.Error(),
		})
		return fmt.Errorf("设置插件 %s 配置失败: %w", name, err)
	}

	// 更新管理器中的配置
	if pluginConfig, exists := pm.configs[name]; exists {
		pluginConfig.Config = config
	}

	log.Printf("插件配置已更新: %s", name)

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "configured",
		Plugin:    name,
		Timestamp: time.Now(),
		Data:      map[string]interface{}{"config": config},
	})

	return nil
}

// ReloadPlugin 重新加载插件
func (pm *DefaultPluginManager) ReloadPlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	plugin, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	// 清理并重新初始化
	if err := plugin.Cleanup(pm.ctx); err != nil {
		log.Printf("清理插件 %s 失败: %v", name, err)
	}

	if err := plugin.Initialize(pm.ctx); err != nil {
		pm.emitEvent(&PluginEvent{
			Type:      "error",
			Plugin:    name,
			Timestamp: time.Now(),
			Error:     err.Error(),
		})
		return fmt.Errorf("重新初始化插件 %s 失败: %w", name, err)
	}

	log.Printf("插件已重新加载: %s", name)

	// 发送事件
	pm.emitEvent(&PluginEvent{
		Type:      "reloaded",
		Plugin:    name,
		Timestamp: time.Now(),
	})

	return nil
}

// GetPluginStats 获取插件统计信息
func (pm *DefaultPluginManager) GetPluginStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_plugins":   len(pm.plugins),
		"enabled_plugins": 0,
		"plugins":         make([]map[string]interface{}, 0),
	}

	enabledCount := 0
	pluginList := make([]map[string]interface{}, 0)

	for name, plugin := range pm.plugins {
		if plugin.IsEnabled() {
			enabledCount++
		}

		config := pm.configs[name]
		pluginInfo := map[string]interface{}{
			"name":        name,
			"version":     plugin.GetVersion(),
			"description": plugin.GetDescription(),
			"enabled":     plugin.IsEnabled(),
			"priority":    config.Priority,
		}
		pluginList = append(pluginList, pluginInfo)
	}

	stats["enabled_plugins"] = enabledCount
	stats["plugins"] = pluginList

	return stats
}

// AddEventHandler 添加事件处理器
func (pm *DefaultPluginManager) AddEventHandler(handler PluginEventHandler) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.eventHandlers = append(pm.eventHandlers, handler)
}

// emitEvent 发送插件事件
func (pm *DefaultPluginManager) emitEvent(event *PluginEvent) {
	for _, handler := range pm.eventHandlers {
		go handler(event)
	}
}

// Shutdown 关闭插件管理器
func (pm *DefaultPluginManager) Shutdown() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 清理所有插件
	for name, plugin := range pm.plugins {
		if err := plugin.Cleanup(pm.ctx); err != nil {
			log.Printf("清理插件 %s 失败: %v", name, err)
		}
	}

	pm.cancel()
	log.Printf("插件管理器已关闭")
	return nil
}

// StoreDistributedMessage 存储消息到分布式存储
// 思路：查找分布式存储插件并调用其存储方法
func (pm *DefaultPluginManager) StoreDistributedMessage(ctx context.Context, message *ChatMessage) error {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 查找分布式存储插件
	for name, plugin := range pm.plugins {
		if !plugin.IsEnabled() {
			continue
		}

		// 类型断言，检查是否是存储插件
		if storagePlugin, ok := plugin.(interface {
			StoreMessage(context.Context, ChatMessage) error
		}); ok && name == "distributed_storage" {
			// 调用分布式存储插件的存储方法
			return storagePlugin.StoreMessage(ctx, *message)
		}
	}

	return fmt.Errorf("分布式存储插件未找到或未启用")
}

// GetDistributedStorageStats 获取分布式存储统计信息
// 思路：查找分布式存储插件并获取统计信息
func (pm *DefaultPluginManager) GetDistributedStorageStats(ctx context.Context) (StorageStats, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 查找分布式存储插件
	for name, plugin := range pm.plugins {
		if !plugin.IsEnabled() {
			continue
		}

		// 类型断言，检查是否是存储插件
		if storagePlugin, ok := plugin.(interface {
			GetStats(context.Context) (StorageStats, error)
		}); ok && name == "distributed_storage" {
			// 调用分布式存储插件的统计方法
			return storagePlugin.GetStats(ctx)
		}
	}

	return StorageStats{}, fmt.Errorf("分布式存储插件未找到或未启用")
}

// BackupDistributedData 备份分布式存储数据
// 思路：查找分布式存储插件并调用备份方法
func (pm *DefaultPluginManager) BackupDistributedData(ctx context.Context, outputPath string) error {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 查找分布式存储插件
	for name, plugin := range pm.plugins {
		if !plugin.IsEnabled() {
			continue
		}

		// 类型断言，检查是否是存储插件
		if storagePlugin, ok := plugin.(interface {
			BackupData(context.Context, string) error
		}); ok && name == "distributed_storage" {
			// 调用分布式存储插件的备份方法
			return storagePlugin.BackupData(ctx, outputPath)
		}
	}

	return fmt.Errorf("分布式存储插件未找到或未启用")
}
