package adapters

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"sync"
	"time"
)

// QQAdapter QQ平台适配器（基于NapCat API）
// 思路：实现PlatformAdapter接口，对接NapCat的HTTP API
// 使用例子：adapter := NewQQAdapter(handler); adapter.Initialize(config)
type QQAdapter struct {
	config     *QQConfig
	handler    MessageHandler
	httpClient *http.Client
	stats      *AdapterStats
	mutex      sync.RWMutex
	ctx        context.Context
	cancel     context.CancelFunc
	isRunning  bool
}

// NapCatMessage NapCat消息结构体
// 思路：NapCat API的消息格式
type NapCatMessage struct {
	PostType    string `json:"post_type"`    // 消息类型
	MessageType string `json:"message_type"` // 消息子类型
	Time        int64  `json:"time"`         // 时间戳
	SelfID      int64  `json:"self_id"`      // 机器人QQ号
	UserID      int64  `json:"user_id"`      // 发送者QQ号
	GroupID     int64  `json:"group_id"`     // 群号（群消息时）
	Message     string `json:"message"`      // 消息内容
	RawMessage  string `json:"raw_message"`  // 原始消息
	MessageID   int32  `json:"message_id"`   // 消息ID
	Sender      struct {
		UserID   int64  `json:"user_id"`  // 发送者QQ号
		Nickname string `json:"nickname"` // 昵称
		Card     string `json:"card"`     // 群名片
		Role     string `json:"role"`     // 群角色
	} `json:"sender"` // 发送者信息
}

// NapCatSendRequest NapCat发送消息请求
// 思路：发送消息到NapCat API的请求格式
type NapCatSendRequest struct {
	UserID     int64  `json:"user_id,omitempty"`  // 目标用户QQ号
	GroupID    int64  `json:"group_id,omitempty"` // 目标群号
	Message    string `json:"message"`            // 消息内容
	AutoEscape bool   `json:"auto_escape"`        // 是否转义
}

// NapCatResponse NapCat API响应
// 思路：NapCat API的标准响应格式
type NapCatResponse struct {
	Status  string      `json:"status"`  // 状态：ok, failed
	RetCode int         `json:"retcode"` // 返回码
	Data    interface{} `json:"data"`    // 响应数据
	Message string      `json:"message"` // 错误信息
}

// NewQQAdapter 创建QQ适配器实例
// 思路：初始化QQ适配器
// 使用例子：adapter := NewQQAdapter()
func NewQQAdapter() PlatformAdapter {
	return &QQAdapter{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		stats: &AdapterStats{
			Platform:         "qq",
			Status:           "stopped",
			ConnectionStatus: "disconnected",
		},
	}
}

// GetPlatformName 获取平台名称
func (a *QQAdapter) GetPlatformName() string {
	return "qq"
}

// SetMessageHandler 设置消息处理器
func (q *QQAdapter) SetMessageHandler(handler MessageHandler) {
	q.handler = handler
}

// Initialize 初始化适配器
// 思路：设置配置和初始化状态
func (a *QQAdapter) Initialize(config PlatformConfig) error {
	qqConfig, ok := config.(*QQConfig)
	if !ok {
		return fmt.Errorf("无效的QQ配置类型")
	}

	if err := qqConfig.Validate(); err != nil {
		return fmt.Errorf("QQ配置验证失败: %w", err)
	}

	a.config = qqConfig
	a.stats.Status = AdapterStatusStopped
	a.stats.StartTime = time.Now()

	log.Printf("QQ适配器初始化完成，NapCat地址: %s", a.config.Config.BaseURL)
	return nil
}

// Start 启动适配器
// 思路：启动消息监听和健康检查
func (a *QQAdapter) Start(ctx context.Context) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if a.isRunning {
		return fmt.Errorf("QQ适配器已在运行")
	}

	a.ctx, a.cancel = context.WithCancel(ctx)
	a.isRunning = true
	a.stats.Status = "starting"

	// 测试连接
	if err := a.testConnection(); err != nil {
		a.stats.Status = "error"
		a.stats.LastError = err.Error()
		return fmt.Errorf("连接NapCat失败: %w", err)
	}

	a.stats.Status = "running"
	a.stats.ConnectionStatus = "connected"

	// 启动消息轮询（NapCat通常使用WebHook，这里模拟轮询）
	go a.messageLoop()

	// 启动健康检查
	go a.healthCheckLoop()

	log.Println("QQ适配器启动成功")
	return nil
}

// Stop 停止适配器
// 思路：优雅关闭适配器
func (a *QQAdapter) Stop(ctx context.Context) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if !a.isRunning {
		return nil
	}

	a.cancel()
	a.isRunning = false
	a.stats.Status = "stopped"
	a.stats.ConnectionStatus = "disconnected"

	log.Println("QQ适配器已停止")
	return nil
}

// SendMessage 发送消息
// 思路：通过NapCat API发送消息
func (a *QQAdapter) SendMessage(ctx context.Context, message *OutgoingMessage) error {
	if !a.IsHealthy() {
		return fmt.Errorf("QQ适配器不健康")
	}

	// 转换用户ID
	userID, err := strconv.ParseInt(message.PlatformUserID, 10, 64)
	if err != nil {
		return fmt.Errorf("无效的QQ号: %s", message.PlatformUserID)
	}

	// 构建请求
	req := NapCatSendRequest{
		UserID:     userID,
		Message:    message.Content,
		AutoEscape: false,
	}

	// 如果是群消息
	if message.PlatformChatID != "" {
		groupID, err := strconv.ParseInt(message.PlatformChatID, 10, 64)
		if err != nil {
			return fmt.Errorf("无效的群号: %s", message.PlatformChatID)
		}
		req.GroupID = groupID
		req.UserID = 0 // 群消息时不需要用户ID
	}

	// 发送请求
	endpoint := "/send_private_msg"
	if req.GroupID != 0 {
		endpoint = "/send_group_msg"
	}

	if err := a.sendRequest(endpoint, req); err != nil {
		a.stats.ErrorCount++
		a.stats.LastError = err.Error()
		return fmt.Errorf("发送消息失败: %w", err)
	}

	a.stats.MessagesSent++
	return nil
}

// GetUserInfo 获取用户信息
// 思路：通过NapCat API获取用户详细信息
func (a *QQAdapter) GetUserInfo(ctx context.Context, platformUserID string) (*UserInfo, error) {
	userID, err := strconv.ParseInt(platformUserID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的QQ号: %s", platformUserID)
	}

	// 构建请求参数
	params := map[string]interface{}{
		"user_id": userID,
	}

	// 发送请求
	var response struct {
		UserID   int64  `json:"user_id"`
		Nickname string `json:"nickname"`
		Sex      string `json:"sex"`
		Age      int    `json:"age"`
	}

	if err := a.sendRequest("/get_stranger_info", params); err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	return &UserInfo{
		PlatformUserID: platformUserID,
		Platform:       "qq",
		Nickname:       response.Nickname,
		IsBot:          false,
	}, nil
}

// IsHealthy 检查适配器健康状态
func (a *QQAdapter) IsHealthy() bool {
	a.mutex.RLock()
	defer a.mutex.RUnlock()
	return a.isRunning && a.stats.Status == "running"
}

// GetStats 获取适配器统计信息
func (a *QQAdapter) GetStats() *AdapterStats {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	// 复制统计信息
	stats := *a.stats
	return &stats
}

// testConnection 测试与NapCat的连接
// 思路：发送健康检查请求验证连接
func (a *QQAdapter) testConnection() error {
	url := a.config.Config.BaseURL + "/get_status"

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}

	// 添加认证头
	if a.config.Config.Token != "" {
		req.Header.Set("Authorization", "Bearer "+a.config.Config.Token)
	}

	resp, err := a.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("NapCat返回错误状态: %d", resp.StatusCode)
	}

	return nil
}

// sendRequest 发送请求到NapCat API
// 思路：统一的API请求方法
func (a *QQAdapter) sendRequest(endpoint string, data interface{}) error {
	url := a.config.Config.BaseURL + endpoint

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	if a.config.Config.Token != "" {
		req.Header.Set("Authorization", "Bearer "+a.config.Config.Token)
	}

	resp, err := a.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var napCatResp NapCatResponse
	if err := json.Unmarshal(body, &napCatResp); err != nil {
		return err
	}

	if napCatResp.Status != "ok" {
		return fmt.Errorf("NapCat API错误: %s", napCatResp.Message)
	}

	return nil
}

// messageLoop 消息轮询循环
// 思路：定期检查新消息（实际项目中应该使用WebHook）
func (a *QQAdapter) messageLoop() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 这里应该实现消息轮询或WebHook处理
			// 由于NapCat通常使用WebHook，这里只是示例
		case <-a.ctx.Done():
			return
		}
	}
}

// healthCheckLoop 健康检查循环
// 思路：定期检查与NapCat的连接状态
func (a *QQAdapter) healthCheckLoop() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := a.testConnection(); err != nil {
				a.mutex.Lock()
				a.stats.ConnectionStatus = "disconnected"
				a.stats.LastError = err.Error()
				a.stats.ErrorCount++
				a.mutex.Unlock()
				log.Printf("QQ适配器健康检查失败: %v", err)
			} else {
				a.mutex.Lock()
				a.stats.ConnectionStatus = "connected"
				a.mutex.Unlock()
			}
		case <-a.ctx.Done():
			return
		}
	}
}

// HandleWebHook 处理NapCat WebHook
// 思路：处理来自NapCat的WebHook消息
// 使用例子：adapter.HandleWebHook(w, r)
func (a *QQAdapter) HandleWebHook(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read body", http.StatusBadRequest)
		return
	}

	var napCatMsg NapCatMessage
	if err := json.Unmarshal(body, &napCatMsg); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 只处理消息事件
	if napCatMsg.PostType != "message" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 转换为统一消息格式
	incomingMsg := &IncomingMessage{
		ID:        fmt.Sprintf("qq_%d", napCatMsg.MessageID),
		Platform:  "qq",
		Timestamp: time.Unix(napCatMsg.Time, 0),
		From: &UserInfo{
			PlatformUserID: fmt.Sprintf("%d", napCatMsg.UserID),
			Platform:       "qq",
			Nickname:       napCatMsg.Sender.Nickname,
			IsBot:          false,
		},
		Content:     napCatMsg.Message,
		MessageType: MessageTypeText,
		Metadata: map[string]string{
			"group_id":   fmt.Sprintf("%d", napCatMsg.GroupID),
			"user_id":    fmt.Sprintf("%d", napCatMsg.UserID),
			"message_id": fmt.Sprintf("%d", napCatMsg.MessageID),
		},
	}

	// 处理消息
	if err := a.handler.HandleIncomingMessage(context.Background(), incomingMsg); err != nil {
		log.Printf("处理QQ消息失败: %v", err)
		http.Error(w, "Failed to process message", http.StatusInternalServerError)
		return
	}

	a.stats.MessagesReceived++
	w.WriteHeader(http.StatusOK)
}
