package handlers

import (
	"net/http"
	"strconv"
	"time"

	"aike_go/internal/services"
	"github.com/gin-gonic/gin"
)

// MessageHandlers 消息处理器
// 思路：将消息相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewMessageHandlers(chatService)
type MessageHandlers struct {
	chatService *services.ChatService
}

// NewMessageHandlers 创建消息处理器
// 思路：初始化消息处理器，注入聊天服务依赖
// 使用例子：handlers := NewMessageHandlers(chatService)
func NewMessageHandlers(chatService *services.ChatService) *MessageHandlers {
	return &MessageHandlers{
		chatService: chatService,
	}
}

// SendMessage 发送消息处理器
// 思路：处理发送消息的HTTP请求，支持多平台消息发送
// 使用例子：POST /api/v1/messages/send
func (h *MessageHandlers) SendMessage(c *gin.Context) {
	var req struct {
		Platform string `json:"platform" binding:"required"` // 平台名称（qq、telegram等）
		UserID   string `json:"user_id" binding:"required"`  // 用户ID
		Content  string `json:"content" binding:"required"`  // 消息内容
		Type     string `json:"type"`                        // 消息类型（text、image等）
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置默认消息类型
	if req.Type == "" {
		req.Type = "text"
	}

	// 调用聊天服务发送消息
	err := h.chatService.SendMessage(c.Request.Context(), req.Platform, req.UserID, req.Content, req.Type)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "消息发送成功",
		"data": gin.H{
			"platform": req.Platform,
			"user_id":  req.UserID,
			"content":  req.Content,
			"type":     req.Type,
			"sent_at":  time.Now().Unix(),
		},
	})
}

// GetMessageHistory 获取消息历史处理器
// 思路：处理获取消息历史的HTTP请求，支持分页和过滤
// 使用例子：GET /api/v1/messages/history?platform=qq&user_id=123&page=1&limit=20
func (h *MessageHandlers) GetMessageHistory(c *gin.Context) {
	// 获取查询参数
	platform := c.Query("platform")
	userID := c.Query("user_id")
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")

	// 参数验证
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "platform参数不能为空",
		})
		return
	}

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "user_id参数不能为空",
		})
		return
	}

	// 转换分页参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	// 计算偏移量
	offset := (page - 1) * limit

	// 调用聊天服务获取消息历史
	messages, total, err := h.chatService.GetMessageHistory(c.Request.Context(), platform, userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取消息历史失败",
			"details": err.Error(),
		})
		return
	}

	// 计算分页信息
	totalPages := (total + limit - 1) / limit

	c.JSON(http.StatusOK, gin.H{
		"message": "获取消息历史成功",
		"data": gin.H{
			"messages": messages,
			"pagination": gin.H{
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": totalPages,
				"has_next":    page < totalPages,
				"has_prev":    page > 1,
			},
		},
	})
}

// SendPlatformMessage 发送平台消息处理器
// 思路：处理特定平台的消息发送请求
// 使用例子：POST /api/v1/platforms/qq/send
func (h *MessageHandlers) SendPlatformMessage(c *gin.Context) {
	platform := c.Param("platform")
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "平台参数不能为空",
		})
		return
	}

	var req struct {
		UserID   string `json:"user_id" binding:"required"`   // 用户ID
		Content  string `json:"content" binding:"required"`   // 消息内容
		Type     string `json:"type"`                         // 消息类型
		GroupID  string `json:"group_id"`                     // 群组ID（可选）
		Metadata map[string]interface{} `json:"metadata"`    // 额外元数据
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置默认消息类型
	if req.Type == "" {
		req.Type = "text"
	}

	// 调用聊天服务发送平台消息
	err := h.chatService.SendPlatformMessage(c.Request.Context(), platform, req.UserID, req.Content, req.Type, req.GroupID, req.Metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送平台消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "平台消息发送成功",
		"data": gin.H{
			"platform": platform,
			"user_id":  req.UserID,
			"content":  req.Content,
			"type":     req.Type,
			"group_id": req.GroupID,
			"sent_at":  time.Now().Unix(),
		},
	})
}
