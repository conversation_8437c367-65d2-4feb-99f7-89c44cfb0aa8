package handlers

import (
	"net/http"
	"strconv"
	"time"

	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
)

// MessageHandlers 消息处理器
// 思路：将消息相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewMessageHandlers(chatService)
type MessageHandlers struct {
	chatService *services.ChatService
}

// NewMessageHandlers 创建消息处理器
// 思路：初始化消息处理器，注入聊天服务依赖
// 使用例子：handlers := NewMessageHandlers(chatService)
func NewMessageHandlers(chatService *services.ChatService) *MessageHandlers {
	return &MessageHandlers{
		chatService: chatService,
	}
}

// SendMessage 发送消息处理器
// 思路：处理发送消息的HTTP请求，通过ProcessMessage方法处理
// 使用例子：POST /api/v1/messages/send
func (h *MessageHandlers) SendMessage(c *gin.Context) {
	var req struct {
		Platform   string `json:"platform" binding:"required"`    // 平台名称（qq、telegram等）
		PlatformID string `json:"platform_id" binding:"required"` // 平台用户ID
		Content    string `json:"content" binding:"required"`     // 消息内容
		Type       string `json:"type"`                           // 消息类型（text、image等）
		SessionID  string `json:"session_id"`                     // 会话ID（可选）
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置默认消息类型
	if req.Type == "" {
		req.Type = "text"
	}

	// 构建ProcessMessage请求
	processReq := services.ProcessMessageRequest{
		Platform:    req.Platform,
		PlatformID:  req.PlatformID,
		Content:     req.Content,
		MessageType: req.Type,
		SessionID:   req.SessionID,
	}

	// 调用聊天服务处理消息
	response, err := h.chatService.ProcessMessage(c.Request.Context(), processReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "处理消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "消息处理成功",
		"data": gin.H{
			"platform":     req.Platform,
			"platform_id":  req.PlatformID,
			"content":      req.Content,
			"type":         req.Type,
			"reply":        response.Reply,
			"session_id":   response.SessionID,
			"tokens_used":  response.TokensUsed,
			"is_from_kb":   response.IsFromKB,
			"processed_at": time.Now().Unix(),
		},
	})
}

// GetMessageHistory 获取消息历史处理器
// 思路：暂时返回空数据，实际实现需要直接查询数据库
// 使用例子：GET /api/v1/messages/history?platform=qq&user_id=123&page=1&limit=20
func (h *MessageHandlers) GetMessageHistory(c *gin.Context) {
	// 获取查询参数
	platform := c.Query("platform")
	userID := c.Query("user_id")
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")

	// 参数验证
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "platform参数不能为空",
		})
		return
	}

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "user_id参数不能为空",
		})
		return
	}

	// 转换分页参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	// 暂时返回空数据，实际实现需要查询数据库
	// TODO: 实现真正的消息历史查询
	// 可以通过 database.GetDB() 获取数据库连接，然后查询 messages 表

	c.JSON(http.StatusOK, gin.H{
		"message": "获取消息历史成功",
		"data": gin.H{
			"messages": []interface{}{}, // 空消息列表
			"pagination": gin.H{
				"page":        page,
				"limit":       limit,
				"total":       0,
				"total_pages": 0,
				"has_next":    false,
				"has_prev":    false,
			},
		},
	})
}

// SendPlatformMessage 发送平台消息处理器
// 思路：处理特定平台的消息发送请求
// 使用例子：POST /api/v1/platforms/qq/send
func (h *MessageHandlers) SendPlatformMessage(c *gin.Context) {
	platform := c.Param("platform")
	if platform == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "平台参数不能为空",
		})
		return
	}

	var req struct {
		UserID   string                 `json:"user_id" binding:"required"` // 用户ID
		Content  string                 `json:"content" binding:"required"` // 消息内容
		Type     string                 `json:"type"`                       // 消息类型
		GroupID  string                 `json:"group_id"`                   // 群组ID（可选）
		Metadata map[string]interface{} `json:"metadata"`                   // 额外元数据
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置默认消息类型
	if req.Type == "" {
		req.Type = "text"
	}

	// 构建ProcessMessage请求
	processReq := services.ProcessMessageRequest{
		Platform:    platform,
		PlatformID:  req.UserID, // 使用UserID作为PlatformID
		Content:     req.Content,
		MessageType: req.Type,
	}

	// 调用聊天服务处理消息
	response, err := h.chatService.ProcessMessage(c.Request.Context(), processReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "处理平台消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "平台消息处理成功",
		"data": gin.H{
			"platform":     platform,
			"user_id":      req.UserID,
			"content":      req.Content,
			"type":         req.Type,
			"group_id":     req.GroupID,
			"reply":        response.Reply,
			"session_id":   response.SessionID,
			"tokens_used":  response.TokensUsed,
			"is_from_kb":   response.IsFromKB,
			"processed_at": time.Now().Unix(),
		},
	})
}
