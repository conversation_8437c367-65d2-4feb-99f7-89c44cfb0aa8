package tests

import (
	"context"
	"testing"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// ChatServiceTestSuite 聊天服务测试套件
// 思路：使用testify套件进行完整的服务测试
type ChatServiceTestSuite struct {
	suite.Suite
	db          *gorm.DB
	chatService *ChatService
	testUser    *models.User
	testSession *models.Session
}

// SetupSuite 测试套件初始化
// 思路：设置测试数据库和服务实例
func (suite *ChatServiceTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 设置全局数据库连接
	database.InitDB(&config.DatabaseConfig{
		Type:     "sqlite",
		Database: ":memory:",
	})
	suite.db = db

	// 运行数据库迁移
	err = db.AutoMigrate(&models.User{}, &models.Message{}, &models.Knowledge{}, &models.Session{})
	suite.Require().NoError(err)

	// 创建配置
	cfg := &config.Config{
		OpenAI: config.OpenAIConfig{
			APIKey:  "test-api-key",
			BaseURL: "https://api.openai.com/v1",
			Model:   "gpt-3.5-turbo",
		},
	}

	// 创建知识库服务
	knowledgeService := NewKnowledgeService()

	// 创建聊天服务
	suite.chatService = NewChatService(cfg, knowledgeService)

	// 创建测试用户
	suite.testUser = &models.User{
		Platform:       "test",
		PlatformID:     "test_user_123",
		PlatformUserID: "test_user_123",
		Nickname:       "测试用户",
		IsBot:          false,
	}
	err = suite.db.Create(suite.testUser).Error
	suite.Require().NoError(err)

	// 创建测试会话
	suite.testSession = &models.Session{
		SessionID: "test_session_123",
		UserID:    suite.testUser.ID,
		Platform:  "test",
		Status:    "active",
		StartTime: time.Now(),
	}
	err = suite.db.Create(suite.testSession).Error
	suite.Require().NoError(err)

	// 创建测试知识库条目
	knowledge := &models.Knowledge{
		Title:     "测试问题",
		Content:   "这是测试答案",
		Keywords:  "测试,问题",
		Category:  "测试分类",
		Status:    "active",
		CreatedBy: "system",
	}
	err = suite.db.Create(knowledge).Error
	suite.Require().NoError(err)
}

// TearDownSuite 测试套件清理
func (suite *ChatServiceTestSuite) TearDownSuite() {
	// 清理数据库连接
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// TestProcessMessage_WithKnowledgeMatch 测试知识库匹配的消息处理
// 思路：验证知识库匹配功能是否正常工作
func (suite *ChatServiceTestSuite) TestProcessMessage_WithKnowledgeMatch() {
	ctx := context.Background()

	// 创建输入消息
	inputMessage := &models.Message{
		UserID:    suite.testUser.ID,
		SessionID: suite.testSession.SessionID,
		Content:   "测试问题",
		Type:      "text",
		Direction: "incoming",
		Platform:  "test",
	}

	// 处理消息
	response, err := suite.chatService.ProcessMessage(ctx, inputMessage)

	// 验证结果
	suite.NoError(err)
	suite.NotNil(response)
	suite.Equal("这是测试答案", response.Content)
	suite.Equal("outgoing", response.Direction)
	suite.Equal("knowledge", response.Source)

	// 验证消息已保存到数据库
	var savedMessages []models.Message
	err = suite.db.Where("session_id = ?", suite.testSession.SessionID).Find(&savedMessages).Error
	suite.NoError(err)
	suite.Len(savedMessages, 2) // 输入消息 + 回复消息
}

// TestProcessMessage_WithoutKnowledgeMatch 测试无知识库匹配的消息处理
// 思路：验证当知识库无匹配时的默认回复
func (suite *ChatServiceTestSuite) TestProcessMessage_WithoutKnowledgeMatch() {
	ctx := context.Background()

	// 创建输入消息（不会匹配知识库）
	inputMessage := &models.Message{
		UserID:    suite.testUser.ID,
		SessionID: suite.testSession.SessionID,
		Content:   "这是一个不存在的问题",
		Type:      "text",
		Direction: "incoming",
		Platform:  "test",
	}

	// 处理消息
	response, err := suite.chatService.ProcessMessage(ctx, inputMessage)

	// 验证结果
	suite.NoError(err)
	suite.NotNil(response)
	suite.Contains(response.Content, "抱歉") // 默认回复包含"抱歉"
	suite.Equal("outgoing", response.Direction)
	suite.Equal("default", response.Source)
}

// TestGetMessageHistory 测试获取消息历史
// 思路：验证消息历史查询功能
func (suite *ChatServiceTestSuite) TestGetMessageHistory() {
	ctx := context.Background()

	// 创建多条测试消息
	messages := []*models.Message{
		{
			UserID:    suite.testUser.ID,
			SessionID: suite.testSession.SessionID,
			Content:   "消息1",
			Type:      "text",
			Direction: "incoming",
			Platform:  "test",
			CreatedAt: time.Now().Add(-3 * time.Minute),
		},
		{
			UserID:    suite.testUser.ID,
			SessionID: suite.testSession.SessionID,
			Content:   "回复1",
			Type:      "text",
			Direction: "outgoing",
			Platform:  "test",
			CreatedAt: time.Now().Add(-2 * time.Minute),
		},
		{
			UserID:    suite.testUser.ID,
			SessionID: suite.testSession.SessionID,
			Content:   "消息2",
			Type:      "text",
			Direction: "incoming",
			Platform:  "test",
			CreatedAt: time.Now().Add(-1 * time.Minute),
		},
	}

	for _, msg := range messages {
		err := suite.db.Create(msg).Error
		suite.Require().NoError(err)
	}

	// 获取消息历史
	history, err := suite.chatService.GetMessageHistory(ctx, suite.testUser.ID, 10, 0)

	// 验证结果
	suite.NoError(err)
	suite.GreaterOrEqual(len(history), 3) // 至少包含刚创建的3条消息

	// 验证消息按时间倒序排列
	for i := 1; i < len(history); i++ {
		suite.True(history[i-1].CreatedAt.After(history[i].CreatedAt) ||
			history[i-1].CreatedAt.Equal(history[i].CreatedAt))
	}
}

// TestCreateOrGetSession 测试会话创建和获取
// 思路：验证会话管理功能
func (suite *ChatServiceTestSuite) TestCreateOrGetSession() {
	ctx := context.Background()

	// 测试获取现有会话
	session, err := suite.chatService.CreateOrGetSession(ctx, suite.testUser.ID, "test")
	suite.NoError(err)
	suite.NotNil(session)
	suite.Equal(suite.testUser.ID, session.UserID)
	suite.Equal("test", session.Platform)

	// 测试创建新用户的会话
	newUser := &models.User{
		Platform:       "test",
		PlatformID:     "new_user_456",
		PlatformUserID: "new_user_456",
		Nickname:       "新用户",
		IsBot:          false,
	}
	err = suite.db.Create(newUser).Error
	suite.Require().NoError(err)

	newSession, err := suite.chatService.CreateOrGetSession(ctx, newUser.ID, "test")
	suite.NoError(err)
	suite.NotNil(newSession)
	suite.Equal(newUser.ID, newSession.UserID)
	suite.NotEqual(session.SessionID, newSession.SessionID) // 不同的会话ID
}

// TestUpdateSessionStats 测试会话统计更新
// 思路：验证会话统计功能
func (suite *ChatServiceTestSuite) TestUpdateSessionStats() {
	ctx := context.Background()

	// 获取初始统计
	var initialSession models.Session
	err := suite.db.First(&initialSession, suite.testSession.ID).Error
	suite.NoError(err)

	initialMessageCount := initialSession.MessageCount
	initialLastActivity := initialSession.LastActivity

	// 更新会话统计
	err = suite.chatService.UpdateSessionStats(ctx, suite.testSession.SessionID)
	suite.NoError(err)

	// 验证统计已更新
	var updatedSession models.Session
	err = suite.db.First(&updatedSession, suite.testSession.ID).Error
	suite.NoError(err)

	suite.Greater(updatedSession.MessageCount, initialMessageCount)
	suite.True(updatedSession.LastActivity.After(initialLastActivity))
}

// 基准测试

// BenchmarkProcessMessage 消息处理性能测试
// 思路：测试消息处理的性能表现
func BenchmarkProcessMessage(b *testing.B) {
	// 设置测试环境
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	database.SetDB(db)
	database.AutoMigrate()

	cfg := &config.Config{
		OpenAI: config.OpenAIConfig{
			APIKey: "test-api-key",
			Model:  "gpt-3.5-turbo",
		},
	}

	knowledgeService := NewKnowledgeService()
	chatService := NewChatService(cfg, knowledgeService)

	// 创建测试用户
	user := &models.User{
		Platform:       "test",
		PlatformID:     "bench_user",
		PlatformUserID: "bench_user",
		Nickname:       "基准测试用户",
	}
	db.Create(user)

	// 创建测试会话
	session := &models.Session{
		SessionID: "bench_session",
		UserID:    user.ID,
		Platform:  "test",
		Status:    "active",
		StartTime: time.Now(),
	}
	db.Create(session)

	// 创建测试消息
	message := &models.Message{
		UserID:    user.ID,
		SessionID: session.SessionID,
		Content:   "测试消息",
		Type:      "text",
		Direction: "incoming",
		Platform:  "test",
	}

	ctx := context.Background()

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		_, err := chatService.ProcessMessage(ctx, message)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// 运行测试套件
func TestChatServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ChatServiceTestSuite))
}

// 单独的单元测试

// TestChatService_InvalidInput 测试无效输入处理
// 思路：验证服务对无效输入的处理
func TestChatService_InvalidInput(t *testing.T) {
	cfg := &config.Config{}
	knowledgeService := NewKnowledgeService()
	chatService := NewChatService(cfg, knowledgeService)

	ctx := context.Background()

	// 测试空消息
	_, err := chatService.ProcessMessage(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "消息不能为空")

	// 测试空内容消息
	emptyMessage := &models.Message{
		UserID:    1,
		SessionID: "test",
		Content:   "",
		Type:      "text",
		Direction: "incoming",
		Platform:  "test",
	}
	_, err = chatService.ProcessMessage(ctx, emptyMessage)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "消息内容不能为空")
}
