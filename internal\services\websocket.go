package services

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"aike_go/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocketService WebSocket服务结构体
// 思路：管理WebSocket连接，支持实时消息推送和广播
// 使用例子：service := NewWebSocketService(cfg); service.HandleConnection(c)
type WebSocketService struct {
	config     *config.Config
	upgrader   websocket.Upgrader
	clients    map[string]*Client // 客户端连接映射
	broadcast  chan []byte        // 广播通道
	register   chan *Client       // 注册通道
	unregister chan *Client       // 注销通道
	mutex      sync.RWMutex       // 读写锁
}

// Client WebSocket客户端结构体
// 思路：封装单个WebSocket连接和相关信息
type Client struct {
	ID         string            `json:"id"`          // 客户端唯一标识
	Platform   string            `json:"platform"`    // 平台类型
	PlatformID string            `json:"platform_id"` // 平台用户ID
	Conn       *websocket.Conn   `json:"-"`           // WebSocket连接
	Send       chan []byte       `json:"-"`           // 发送通道
	Service    *WebSocketService `json:"-"`           // 服务引用
}

// WSMessage WebSocket消息结构体
// 思路：统一的WebSocket消息格式
type WSMessage struct {
	Type      string      `json:"type"`                 // 消息类型
	Data      interface{} `json:"data"`                 // 消息数据
	Timestamp int64       `json:"timestamp"`            // 时间戳
	From      string      `json:"from,omitempty"`       // 发送者
	To        string      `json:"to,omitempty"`         // 接收者
	SessionID string      `json:"session_id,omitempty"` // 会话ID
}

// WSAuthMessage 认证消息结构体
// 思路：WebSocket连接认证信息
type WSAuthMessage struct {
	Platform   string `json:"platform" binding:"required"`    // 平台类型
	PlatformID string `json:"platform_id" binding:"required"` // 平台用户ID
	Token      string `json:"token,omitempty"`                // 认证令牌（可选）
}

// WSChatMessage 聊天消息结构体
// 思路：WebSocket聊天消息格式
type WSChatMessage struct {
	Content     string `json:"content" binding:"required"` // 消息内容
	MessageType string `json:"message_type"`               // 消息类型
	SessionID   string `json:"session_id,omitempty"`       // 会话ID
}

// NewWebSocketService 创建WebSocket服务实例
// 思路：初始化WebSocket服务和相关通道
// 使用例子：service := NewWebSocketService(cfg)
func NewWebSocketService(cfg *config.Config) *WebSocketService {
	service := &WebSocketService{
		config: cfg,
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				// 允许所有来源（生产环境应该限制）
				return true
			},
		},
		clients:    make(map[string]*Client),
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}

	// 启动消息处理协程
	go service.handleMessages()

	return service
}

// HandleConnection 处理WebSocket连接
// 思路：升级HTTP连接为WebSocket，创建客户端实例
// 使用例子：service.HandleConnection(c)
func (s *WebSocketService) HandleConnection(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := s.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 创建客户端实例
	client := &Client{
		ID:      generateClientID(),
		Conn:    conn,
		Send:    make(chan []byte, 256),
		Service: s,
	}

	// 注册客户端
	s.register <- client

	// 启动客户端处理协程
	go client.writePump()
	go client.readPump()
}

// handleMessages 处理消息分发
// 思路：处理客户端注册、注销和广播消息
func (s *WebSocketService) handleMessages() {
	for {
		select {
		case client := <-s.register:
			s.mutex.Lock()
			s.clients[client.ID] = client
			s.mutex.Unlock()
			log.Printf("WebSocket客户端连接: %s", client.ID)

		case client := <-s.unregister:
			s.mutex.Lock()
			if _, ok := s.clients[client.ID]; ok {
				delete(s.clients, client.ID)
				close(client.Send)
			}
			s.mutex.Unlock()
			log.Printf("WebSocket客户端断开: %s", client.ID)

		case message := <-s.broadcast:
			s.mutex.RLock()
			for _, client := range s.clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(s.clients, client.ID)
				}
			}
			s.mutex.RUnlock()
		}
	}
}

// BroadcastMessage 广播消息给所有客户端
// 思路：向所有连接的客户端发送消息
// 使用例子：service.BroadcastMessage(message)
func (s *WebSocketService) BroadcastMessage(msgType string, data interface{}) {
	message := WSMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化广播消息失败: %v", err)
		return
	}

	s.broadcast <- messageBytes
}

// SendToClient 发送消息给指定客户端
// 思路：向特定客户端发送消息
// 使用例子：service.SendToClient(clientID, msgType, data)
func (s *WebSocketService) SendToClient(clientID, msgType string, data interface{}) {
	s.mutex.RLock()
	client, exists := s.clients[clientID]
	s.mutex.RUnlock()

	if !exists {
		log.Printf("客户端不存在: %s", clientID)
		return
	}

	message := WSMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case client.Send <- messageBytes:
	default:
		close(client.Send)
		s.mutex.Lock()
		delete(s.clients, clientID)
		s.mutex.Unlock()
	}
}

// SendToPlatformUser 发送消息给指定平台用户
// 思路：根据平台和用户ID发送消息
// 使用例子：service.SendToPlatformUser(platform, platformID, msgType, data)
func (s *WebSocketService) SendToPlatformUser(platform, platformID, msgType string, data interface{}) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, client := range s.clients {
		if client.Platform == platform && client.PlatformID == platformID {
			message := WSMessage{
				Type:      msgType,
				Data:      data,
				Timestamp: time.Now().Unix(),
			}

			messageBytes, err := json.Marshal(message)
			if err != nil {
				log.Printf("序列化消息失败: %v", err)
				continue
			}

			select {
			case client.Send <- messageBytes:
			default:
				close(client.Send)
				delete(s.clients, client.ID)
			}
		}
	}
}

// GetConnectedClients 获取连接的客户端列表
// 思路：返回当前所有连接的客户端信息
// 使用例子：clients := service.GetConnectedClients()
func (s *WebSocketService) GetConnectedClients() []Client {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	clients := make([]Client, 0, len(s.clients))
	for _, client := range s.clients {
		// 复制客户端信息（不包含连接对象）
		clientInfo := Client{
			ID:         client.ID,
			Platform:   client.Platform,
			PlatformID: client.PlatformID,
		}
		clients = append(clients, clientInfo)
	}

	return clients
}

// readPump 读取客户端消息
// 思路：持续读取WebSocket消息并处理
func (c *Client) readPump() {
	defer func() {
		c.Service.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, messageBytes, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}

		// 解析消息
		var wsMsg WSMessage
		if err := json.Unmarshal(messageBytes, &wsMsg); err != nil {
			log.Printf("解析WebSocket消息失败: %v", err)
			continue
		}

		// 处理不同类型的消息
		c.handleMessage(&wsMsg)
	}
}

// writePump 发送消息给客户端
// 思路：持续发送消息到WebSocket连接
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket写入错误: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
// 思路：根据消息类型执行相应的处理逻辑
func (c *Client) handleMessage(msg *WSMessage) {
	switch msg.Type {
	case "auth":
		c.handleAuth(msg)
	case "chat":
		c.handleChat(msg)
	case "ping":
		c.sendPong()
	default:
		log.Printf("未知消息类型: %s", msg.Type)
	}
}

// handleAuth 处理认证消息
// 思路：验证客户端身份并设置平台信息
func (c *Client) handleAuth(msg *WSMessage) {
	authData, ok := msg.Data.(map[string]interface{})
	if !ok {
		c.sendError("认证数据格式错误")
		return
	}

	platform, _ := authData["platform"].(string)
	platformID, _ := authData["platform_id"].(string)

	if platform == "" || platformID == "" {
		c.sendError("缺少必需的认证参数")
		return
	}

	c.Platform = platform
	c.PlatformID = platformID

	// 发送认证成功消息
	c.sendMessage("auth_success", map[string]interface{}{
		"client_id":   c.ID,
		"platform":    c.Platform,
		"platform_id": c.PlatformID,
	})

	log.Printf("客户端认证成功: %s (%s:%s)", c.ID, c.Platform, c.PlatformID)
}

// handleChat 处理聊天消息
// 思路：处理客户端发送的聊天消息
func (c *Client) handleChat(msg *WSMessage) {
	if c.Platform == "" || c.PlatformID == "" {
		c.sendError("请先进行认证")
		return
	}

	chatData, ok := msg.Data.(map[string]interface{})
	if !ok {
		c.sendError("聊天数据格式错误")
		return
	}

	content, _ := chatData["content"].(string)
	if content == "" {
		c.sendError("消息内容不能为空")
		return
	}

	// 这里可以集成聊天服务处理消息
	// 暂时回显消息
	c.sendMessage("chat_reply", map[string]interface{}{
		"content":    "收到消息: " + content,
		"session_id": msg.SessionID,
	})
}

// sendMessage 发送消息给客户端
// 思路：封装消息发送逻辑
func (c *Client) sendMessage(msgType string, data interface{}) {
	message := WSMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case c.Send <- messageBytes:
	default:
		close(c.Send)
	}
}

// sendError 发送错误消息
// 思路：发送错误信息给客户端
func (c *Client) sendError(errorMsg string) {
	c.sendMessage("error", map[string]interface{}{
		"message": errorMsg,
	})
}

// sendPong 发送pong消息
// 思路：响应ping消息
func (c *Client) sendPong() {
	c.sendMessage("pong", map[string]interface{}{
		"timestamp": time.Now().Unix(),
	})
}

// generateClientID 生成客户端唯一标识
// 思路：生成基于时间戳的唯一ID
func generateClientID() string {
	return fmt.Sprintf("client_%d", time.Now().UnixNano())
}
