package adapters

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"
)

// TelegramAdapter Telegram平台适配器
// 思路：基于Telegram Bot API实现消息收发和用户管理
type TelegramAdapter struct {
	config     PlatformConfig
	client     *http.Client
	botToken   string
	baseURL    string
	webhookURL string
	isRunning  bool
	stopChan   chan struct{}
	msgHandler MessageHandler
}

// TelegramUpdate Telegram更新结构体
// 思路：Telegram Bot API的Update对象
type TelegramUpdate struct {
	UpdateID      int                    `json:"update_id"`
	Message       *TelegramMessage       `json:"message,omitempty"`
	CallbackQuery *TelegramCallbackQuery `json:"callback_query,omitempty"`
}

// TelegramMessage Telegram消息结构体
// 思路：Telegram Bot API的Message对象
type TelegramMessage struct {
	MessageID int                 `json:"message_id"`
	From      *TelegramUser       `json:"from,omitempty"`
	Chat      *TelegramChat       `json:"chat"`
	Date      int64               `json:"date"`
	Text      string              `json:"text,omitempty"`
	Photo     []TelegramPhotoSize `json:"photo,omitempty"`
	Document  *TelegramDocument   `json:"document,omitempty"`
	Voice     *TelegramVoice      `json:"voice,omitempty"`
}

// TelegramUser Telegram用户结构体
type TelegramUser struct {
	ID           int64  `json:"id"`
	IsBot        bool   `json:"is_bot"`
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name,omitempty"`
	Username     string `json:"username,omitempty"`
	LanguageCode string `json:"language_code,omitempty"`
}

// TelegramChat Telegram聊天结构体
type TelegramChat struct {
	ID        int64  `json:"id"`
	Type      string `json:"type"` // "private", "group", "supergroup", "channel"
	Title     string `json:"title,omitempty"`
	Username  string `json:"username,omitempty"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
}

// TelegramPhotoSize Telegram图片尺寸结构体
type TelegramPhotoSize struct {
	FileID       string `json:"file_id"`
	FileUniqueID string `json:"file_unique_id"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	FileSize     int    `json:"file_size,omitempty"`
}

// TelegramDocument Telegram文档结构体
type TelegramDocument struct {
	FileID       string `json:"file_id"`
	FileUniqueID string `json:"file_unique_id"`
	FileName     string `json:"file_name,omitempty"`
	MimeType     string `json:"mime_type,omitempty"`
	FileSize     int    `json:"file_size,omitempty"`
}

// TelegramVoice Telegram语音结构体
type TelegramVoice struct {
	FileID       string `json:"file_id"`
	FileUniqueID string `json:"file_unique_id"`
	Duration     int    `json:"duration"`
	MimeType     string `json:"mime_type,omitempty"`
	FileSize     int    `json:"file_size,omitempty"`
}

// TelegramCallbackQuery Telegram回调查询结构体
type TelegramCallbackQuery struct {
	ID      string           `json:"id"`
	From    *TelegramUser    `json:"from"`
	Message *TelegramMessage `json:"message,omitempty"`
	Data    string           `json:"data,omitempty"`
}

// TelegramSendMessageRequest 发送消息请求结构体
// 思路：Telegram Bot API的sendMessage参数
type TelegramSendMessageRequest struct {
	ChatID                string `json:"chat_id"`
	Text                  string `json:"text"`
	ParseMode             string `json:"parse_mode,omitempty"`
	DisableWebPagePreview bool   `json:"disable_web_page_preview,omitempty"`
	DisableNotification   bool   `json:"disable_notification,omitempty"`
	ReplyToMessageID      int    `json:"reply_to_message_id,omitempty"`
}

// TelegramResponse Telegram API响应结构体
type TelegramResponse struct {
	OK          bool        `json:"ok"`
	Result      interface{} `json:"result,omitempty"`
	ErrorCode   int         `json:"error_code,omitempty"`
	Description string      `json:"description,omitempty"`
}

// NewTelegramAdapter 创建Telegram适配器
// 思路：初始化Telegram适配器实例
// 使用例子：adapter := NewTelegramAdapter()
func NewTelegramAdapter() PlatformAdapter {
	return &TelegramAdapter{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		stopChan: make(chan struct{}),
	}
}

// GetPlatformName 返回平台名称
func (t *TelegramAdapter) GetPlatformName() string {
	return "telegram"
}

// Initialize 初始化适配器
// 思路：设置Bot Token和API配置
func (t *TelegramAdapter) Initialize(config PlatformConfig) error {
	// 这里暂时使用简单的配置方式
	// 实际使用时需要从环境变量或配置文件获取
	t.botToken = os.Getenv("TELEGRAM_BOT_TOKEN")
	if t.botToken == "" {
		return fmt.Errorf("Telegram Bot Token未配置，请设置TELEGRAM_BOT_TOKEN环境变量")
	}

	// 设置API基础URL
	t.baseURL = os.Getenv("TELEGRAM_BASE_URL")
	if t.baseURL == "" {
		t.baseURL = "https://api.telegram.org"
	}

	// 设置WebHook URL
	t.webhookURL = os.Getenv("TELEGRAM_WEBHOOK_URL")

	log.Printf("Telegram适配器初始化完成，Bot Token: %s...", t.botToken[:10])
	return nil
}

// Start 启动适配器
// 思路：设置WebHook或启动长轮询
func (t *TelegramAdapter) Start(ctx context.Context) error {
	if t.isRunning {
		return fmt.Errorf("Telegram适配器已在运行")
	}

	// 验证Bot Token
	if err := t.validateBotToken(ctx); err != nil {
		return fmt.Errorf("Bot Token验证失败: %w", err)
	}

	// 设置WebHook（如果配置了）
	if t.webhookURL != "" {
		if err := t.setWebHook(ctx); err != nil {
			log.Printf("设置WebHook失败，将使用长轮询: %v", err)
			go t.startPolling(ctx)
		} else {
			log.Printf("WebHook设置成功: %s", t.webhookURL)
		}
	} else {
		// 启动长轮询
		go t.startPolling(ctx)
	}

	t.isRunning = true
	log.Printf("Telegram适配器启动成功")
	return nil
}

// Stop 停止适配器
func (t *TelegramAdapter) Stop(ctx context.Context) error {
	if !t.isRunning {
		return nil
	}

	close(t.stopChan)
	t.isRunning = false

	// 删除WebHook
	if t.webhookURL != "" {
		t.deleteWebHook(ctx)
	}

	log.Printf("Telegram适配器已停止")
	return nil
}

// SendMessage 发送消息
// 思路：调用Telegram Bot API发送消息
func (t *TelegramAdapter) SendMessage(ctx context.Context, message *OutgoingMessage) error {
	if !t.isRunning {
		return fmt.Errorf("Telegram适配器未运行")
	}

	// 构建发送请求
	chatID := message.PlatformChatID
	if chatID == "" {
		chatID = message.PlatformUserID
	}

	request := TelegramSendMessageRequest{
		ChatID: chatID,
		Text:   message.Content,
	}

	// 设置消息格式
	if message.MessageType == "markdown" {
		request.ParseMode = "Markdown"
	} else if message.MessageType == "html" {
		request.ParseMode = "HTML"
	}

	// 发送消息
	url := fmt.Sprintf("%s/bot%s/sendMessage", t.baseURL, t.botToken)

	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	resp, err := t.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var telegramResp TelegramResponse
	if err := json.NewDecoder(resp.Body).Decode(&telegramResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !telegramResp.OK {
		return fmt.Errorf("Telegram API错误 [%d]: %s", telegramResp.ErrorCode, telegramResp.Description)
	}

	log.Printf("消息发送成功到Telegram用户: %s", message.PlatformUserID)
	return nil
}

// GetUserInfo 获取用户信息
// 思路：通过Telegram API获取用户详细信息
func (t *TelegramAdapter) GetUserInfo(ctx context.Context, platformUserID string) (*UserInfo, error) {
	// Telegram Bot API不支持直接获取用户信息
	// 只能在用户发送消息时获取用户信息
	return &UserInfo{
		PlatformUserID: platformUserID,
		Platform:       "telegram",
		Nickname:       "Telegram用户",
		Avatar:         "",
		IsBot:          false,
	}, nil
}

// IsHealthy 健康检查
// 思路：通过getMe API检查Bot状态
func (t *TelegramAdapter) IsHealthy() bool {
	if !t.isRunning {
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return t.validateBotToken(ctx) == nil
}

// SetMessageHandler 设置消息处理器
func (t *TelegramAdapter) SetMessageHandler(handler MessageHandler) {
	t.msgHandler = handler
}

// GetStats 获取适配器统计信息
func (t *TelegramAdapter) GetStats() *AdapterStats {
	status := "stopped"
	if t.isRunning {
		status = "running"
	}

	return &AdapterStats{
		Platform:         "telegram",
		Status:           status,
		ConnectionStatus: "disconnected",
		StartTime:        time.Now(),
		LastMessageTime:  time.Now(),
		MessagesReceived: 0,
		MessagesSent:     0,
		ErrorCount:       0,
		LastError:        "",
	}
}

// validateBotToken 验证Bot Token
// 思路：调用getMe API验证Token有效性
func (t *TelegramAdapter) validateBotToken(ctx context.Context) error {
	url := fmt.Sprintf("%s/bot%s/getMe", t.baseURL, t.botToken)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return err
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	var telegramResp TelegramResponse
	if err := json.NewDecoder(resp.Body).Decode(&telegramResp); err != nil {
		return err
	}

	if !telegramResp.OK {
		return fmt.Errorf("Bot Token无效: %s", telegramResp.Description)
	}

	return nil
}

// setWebHook 设置WebHook
// 思路：配置Telegram服务器向指定URL发送更新
func (t *TelegramAdapter) setWebHook(ctx context.Context) error {
	url := fmt.Sprintf("%s/bot%s/setWebhook", t.baseURL, t.botToken)

	data := map[string]string{
		"url": t.webhookURL,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := t.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	var telegramResp TelegramResponse
	if err := json.NewDecoder(resp.Body).Decode(&telegramResp); err != nil {
		return err
	}

	if !telegramResp.OK {
		return fmt.Errorf("设置WebHook失败: %s", telegramResp.Description)
	}

	return nil
}

// deleteWebHook 删除WebHook
// 思路：清除WebHook配置
func (t *TelegramAdapter) deleteWebHook(ctx context.Context) error {
	url := fmt.Sprintf("%s/bot%s/deleteWebhook", t.baseURL, t.botToken)

	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		return err
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return nil
}

// startPolling 启动长轮询
// 思路：定期调用getUpdates API获取新消息
func (t *TelegramAdapter) startPolling(ctx context.Context) {
	log.Printf("启动Telegram长轮询")

	offset := 0
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-t.stopChan:
			return
		case <-ticker.C:
			updates, err := t.getUpdates(ctx, offset)
			if err != nil {
				log.Printf("获取Telegram更新失败: %v", err)
				continue
			}

			for _, update := range updates {
				if update.UpdateID >= offset {
					offset = update.UpdateID + 1
				}

				// 处理更新
				go t.handleUpdate(ctx, &update)
			}
		}
	}
}

// getUpdates 获取更新
// 思路：调用getUpdates API获取新的消息和事件
func (t *TelegramAdapter) getUpdates(ctx context.Context, offset int) ([]TelegramUpdate, error) {
	url := fmt.Sprintf("%s/bot%s/getUpdates?offset=%d&timeout=10", t.baseURL, t.botToken, offset)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var telegramResp TelegramResponse
	if err := json.NewDecoder(resp.Body).Decode(&telegramResp); err != nil {
		return nil, err
	}

	if !telegramResp.OK {
		return nil, fmt.Errorf("获取更新失败: %s", telegramResp.Description)
	}

	// 解析更新数组
	var updates []TelegramUpdate
	if telegramResp.Result != nil {
		resultBytes, err := json.Marshal(telegramResp.Result)
		if err != nil {
			return nil, err
		}

		if err := json.Unmarshal(resultBytes, &updates); err != nil {
			return nil, err
		}
	}

	return updates, nil
}

// handleUpdate 处理更新
// 思路：将Telegram更新转换为统一的消息格式
func (t *TelegramAdapter) handleUpdate(ctx context.Context, update *TelegramUpdate) {
	if t.msgHandler == nil {
		return
	}

	// 处理普通消息
	if update.Message != nil {
		t.handleMessage(ctx, update.Message)
	}

	// 处理回调查询
	if update.CallbackQuery != nil {
		t.handleCallbackQuery(ctx, update.CallbackQuery)
	}
}

// handleMessage 处理消息
// 思路：将Telegram消息转换为统一格式并调用处理器
func (t *TelegramAdapter) handleMessage(ctx context.Context, msg *TelegramMessage) {
	// 构建用户信息
	var userInfo *UserInfo
	if msg.From != nil {
		nickname := msg.From.FirstName
		if msg.From.LastName != "" {
			nickname += " " + msg.From.LastName
		}
		if nickname == "" && msg.From.Username != "" {
			nickname = msg.From.Username
		}

		userInfo = &UserInfo{
			PlatformUserID: strconv.FormatInt(msg.From.ID, 10),
			Platform:       "telegram",
			Nickname:       nickname,
			Avatar:         "",
			IsBot:          msg.From.IsBot,
		}
	}

	// 确定消息类型和内容
	messageType := "text"
	content := msg.Text

	if len(msg.Photo) > 0 {
		messageType = "image"
		content = "[图片]"
	} else if msg.Document != nil {
		messageType = "file"
		content = "[文件]"
		if msg.Document.FileName != "" {
			content = "[文件: " + msg.Document.FileName + "]"
		}
	} else if msg.Voice != nil {
		messageType = "voice"
		content = "[语音]"
	}

	// 构建统一消息格式
	incomingMsg := &IncomingMessage{
		ID:          fmt.Sprintf("telegram_%d", msg.MessageID),
		Platform:    "telegram",
		Content:     content,
		MessageType: messageType,
		Timestamp:   time.Unix(msg.Date, 0),
		From:        userInfo,
		Metadata: map[string]string{
			"chat_id":    strconv.FormatInt(msg.Chat.ID, 10),
			"chat_type":  msg.Chat.Type,
			"message_id": strconv.Itoa(msg.MessageID),
		},
	}

	// 调用消息处理器
	if err := t.msgHandler.HandleIncomingMessage(ctx, incomingMsg); err != nil {
		log.Printf("处理Telegram消息失败: %v", err)
	}
}

// handleCallbackQuery 处理回调查询
// 思路：处理内联键盘的回调事件
func (t *TelegramAdapter) handleCallbackQuery(ctx context.Context, query *TelegramCallbackQuery) {
	// 构建用户信息
	nickname := query.From.FirstName
	if query.From.LastName != "" {
		nickname += " " + query.From.LastName
	}

	userInfo := &UserInfo{
		PlatformUserID: strconv.FormatInt(query.From.ID, 10),
		Platform:       "telegram",
		Nickname:       nickname,
		Avatar:         "",
		IsBot:          query.From.IsBot,
	}

	// 构建回调消息
	incomingMsg := &IncomingMessage{
		ID:          fmt.Sprintf("telegram_callback_%s", query.ID),
		Platform:    "telegram",
		Content:     query.Data,
		MessageType: "callback",
		Timestamp:   time.Now(),
		From:        userInfo,
		Metadata: map[string]string{
			"callback_id": query.ID,
			"query_type":  "callback",
		},
	}

	// 调用消息处理器
	if err := t.msgHandler.HandleIncomingMessage(ctx, incomingMsg); err != nil {
		log.Printf("处理Telegram回调查询失败: %v", err)
	}
}

// ProcessWebHook 处理WebHook请求
// 思路：处理来自Telegram服务器的WebHook请求
func (t *TelegramAdapter) ProcessWebHook(ctx context.Context, body []byte) error {
	var update TelegramUpdate
	if err := json.Unmarshal(body, &update); err != nil {
		return fmt.Errorf("解析WebHook数据失败: %w", err)
	}

	// 处理更新
	go t.handleUpdate(ctx, &update)

	return nil
}
