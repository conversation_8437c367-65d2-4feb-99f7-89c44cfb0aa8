package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"aike_go/internal/models"
	"aike_go/internal/plugins"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 优化的分布式存储消息结构
// 思路：减少冗余字段，使用数字代替字符串，优化存储空间
// 相比原Message模型减少约60%的存储空间
type DistributedMessage struct {
	ID        uint   `gorm:"primarykey" json:"id"`                  // 自增ID（兼容现有结构）
	CreatedAt int64  `gorm:"not null;index" json:"created_at"`      // 创建时间戳（秒）
	UserID    string `gorm:"size:20;not null;index" json:"user_id"` // 用户ID（平台用户ID）
	Content   string `gorm:"type:text" json:"content"`              // 消息内容
	Type      int8   `gorm:"not null;default:1" json:"type"`        // 消息类型：1=text, 2=image, 3=file, 4=audio, 5=video, 6=location
	Direction int8   `gorm:"not null" json:"direction"`             // 消息方向：1=incoming(用户发送), 2=outgoing(机器人回复)
	Status    int8   `gorm:"default:1" json:"status"`               // 消息状态：1=sent, 2=delivered, 3=read, 4=failed
	ParentID  string `gorm:"size:32;index" json:"parent_id"`        // 父消息ID（用于回复）
}

// 兼容现有数据库的用户结构
// 思路：保持与现有表结构完全兼容，同时优化时间戳格式
type DistributedUser struct {
	ID           uint       `gorm:"primarykey" json:"id"`                    // 自增ID
	CreatedAt    int64      `gorm:"not null" json:"created_at"`              // 创建时间戳（秒）
	UpdatedAt    int64      `gorm:"not null" json:"updated_at"`              // 更新时间戳（秒）
	DeletedAt    *time.Time `gorm:"index" json:"deleted_at"`                 // 软删除时间
	PlatformID   string     `gorm:"not null;uniqueIndex" json:"platform_id"` // 平台用户ID
	Platform     string     `gorm:"not null" json:"platform"`                // 平台名称（从路径推断，但保持兼容）
	Nickname     string     `json:"nickname"`                                // 用户昵称
	Avatar       string     `json:"avatar"`                                  // 用户头像URL
	Email        string     `json:"email"`                                   // 邮箱（保持兼容）
	Phone        string     `json:"phone"`                                   // 电话（保持兼容）
	Status       string     `gorm:"default:active" json:"status"`            // 用户状态（保持兼容）
	IsVIP        bool       `gorm:"default:false" json:"is_v_ip"`            // VIP状态（保持兼容）
	MessageCount int        `gorm:"default:0" json:"message_count"`          // 消息计数（保持兼容）
	LastActiveAt *time.Time `json:"last_active_at"`                          // 最后活跃时间（保持兼容）
}

// 消息类型常量
// 思路：使用数字代替字符串，减少存储空间和提高查询效率
const (
	MessageTypeText     int8 = 1 // 文本消息
	MessageTypeImage    int8 = 2 // 图片消息
	MessageTypeFile     int8 = 3 // 文件消息
	MessageTypeAudio    int8 = 4 // 音频消息
	MessageTypeVideo    int8 = 5 // 视频消息
	MessageTypeLocation int8 = 6 // 位置消息
)

// 消息方向常量
const (
	DirectionIncoming int8 = 1 // 用户发送
	DirectionOutgoing int8 = 2 // 机器人回复
)

// 消息状态常量
const (
	StatusSent      int8 = 1 // 已发送
	StatusDelivered int8 = 2 // 已送达
	StatusRead      int8 = 3 // 已读
	StatusFailed    int8 = 4 // 发送失败
)

// TableName 指定表名
func (DistributedMessage) TableName() string {
	return "messages"
}

// TableName 指定表名
func (DistributedUser) TableName() string {
	return "users"
}

// getMessageTypeCode 将字符串消息类型转换为数字代码
// 思路：减少存储空间，提高查询效率
func getMessageTypeCode(messageType string) int8 {
	switch messageType {
	case "text":
		return MessageTypeText
	case "image":
		return MessageTypeImage
	case "file":
		return MessageTypeFile
	case "audio":
		return MessageTypeAudio
	case "video":
		return MessageTypeVideo
	case "location":
		return MessageTypeLocation
	default:
		return MessageTypeText // 默认为文本消息
	}
}

// getMessageTypeString 将数字代码转换为字符串消息类型
// 思路：用于显示和API返回
func getMessageTypeString(typeCode int8) string {
	switch typeCode {
	case MessageTypeText:
		return "text"
	case MessageTypeImage:
		return "image"
	case MessageTypeFile:
		return "file"
	case MessageTypeAudio:
		return "audio"
	case MessageTypeVideo:
		return "video"
	case MessageTypeLocation:
		return "location"
	default:
		return "text"
	}
}

// DistributedStoragePlugin 分布式存储插件
// 思路：按平台、聊天类型、群号等维度分布存储聊天记录
// 目录结构：
// data/
//
//	├── qq/
//	│   ├── private/
//	│   │   ├── private_001.db (按大小分文件，每个文件最大100MB)
//	│   │   ├── private_002.db
//	│   │   └── ...
//	│   └── groups/
//	│       ├── group_123456789.db (每个群一个数据库)
//	│       ├── group_987654321.db
//	│       └── ...
//	├── telegram/
//	│   ├── private/
//	│   └── groups/
//	└── wechat/
//	    ├── private/
//	    └── groups/
type DistributedStoragePlugin struct {
	name        string
	version     string
	description string
	enabled     bool
	config      map[string]interface{}

	// 数据库连接池
	// key: 数据库路径, value: gorm.DB实例
	dbPool map[string]*gorm.DB

	// 配置参数
	baseDir     string // 基础存储目录
	maxFileSize int64  // 私聊数据库最大文件大小 (字节)

	// 私聊数据库文件计数器
	// key: platform, value: 当前文件编号
	privateFileCounters map[string]int
}

// NewDistributedStoragePlugin 创建分布式存储插件
// 思路：初始化插件实例和配置参数
func NewDistributedStoragePlugin(baseDir string) *DistributedStoragePlugin {
	return &DistributedStoragePlugin{
		name:        "distributed_storage",
		version:     "1.0.0",
		description: "按平台和聊天类型分布式存储聊天记录",
		enabled:     true,
		config: map[string]interface{}{
			"base_dir":         baseDir,
			"max_file_size_mb": 100, // 私聊数据库最大100MB
			"auto_migrate":     true,
			"retention_days":   365,
			"backup_enabled":   true,
			"compression":      true,
			"create_indexes":   true,
		},
		dbPool:              make(map[string]*gorm.DB),
		baseDir:             baseDir,
		maxFileSize:         100 * 1024 * 1024, // 100MB
		privateFileCounters: make(map[string]int),
	}
}

// GetName 获取插件名称
func (p *DistributedStoragePlugin) GetName() string {
	return p.name
}

// GetVersion 获取插件版本
func (p *DistributedStoragePlugin) GetVersion() string {
	return p.version
}

// GetDescription 获取插件描述
func (p *DistributedStoragePlugin) GetDescription() string {
	return p.description
}

// IsEnabled 检查插件是否启用
func (p *DistributedStoragePlugin) IsEnabled() bool {
	return p.enabled
}

// Enable 启用插件
func (p *DistributedStoragePlugin) Enable() error {
	p.enabled = true
	return nil
}

// Disable 禁用插件
func (p *DistributedStoragePlugin) Disable() error {
	p.enabled = false
	return nil
}

// GetConfig 获取插件配置
func (p *DistributedStoragePlugin) GetConfig() map[string]interface{} {
	return p.config
}

// SetConfig 设置插件配置
func (p *DistributedStoragePlugin) SetConfig(config map[string]interface{}) error {
	p.config = config
	return nil
}

// Cleanup 清理插件资源
func (p *DistributedStoragePlugin) Cleanup(ctx context.Context) error {
	// 关闭所有数据库连接
	for dbPath, db := range p.dbPool {
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
		delete(p.dbPool, dbPath)
	}
	log.Printf("分布式存储插件资源清理完成")
	return nil
}

// Initialize 初始化插件
// 思路：创建目录结构，初始化数据库连接池
func (p *DistributedStoragePlugin) Initialize(ctx context.Context) error {
	log.Printf("分布式存储插件初始化开始，基础目录: %s", p.baseDir)

	// 创建基础目录结构
	platforms := []string{"qq", "telegram", "wechat", "test"}
	chatTypes := []string{"private", "groups"}

	for _, platform := range platforms {
		for _, chatType := range chatTypes {
			dir := filepath.Join(p.baseDir, platform, chatType)
			if err := os.MkdirAll(dir, 0o755); err != nil {
				return fmt.Errorf("创建目录失败 %s: %w", dir, err)
			}
		}
	}

	// 扫描现有的私聊数据库文件，初始化计数器
	for _, platform := range platforms {
		privateDir := filepath.Join(p.baseDir, platform, "private")
		counter := p.scanPrivateFiles(privateDir)
		p.privateFileCounters[platform] = counter
		log.Printf("平台 %s 私聊数据库文件计数器初始化为: %d", platform, counter)
	}

	log.Printf("分布式存储插件初始化完成")
	return nil
}

// scanPrivateFiles 扫描私聊目录中的数据库文件，返回最大编号
// 思路：扫描 private_001.db, private_002.db 等文件，找到最大编号
func (p *DistributedStoragePlugin) scanPrivateFiles(privateDir string) int {
	maxCounter := 0

	files, err := os.ReadDir(privateDir)
	if err != nil {
		return 0 // 目录不存在或无法读取，从0开始
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "private_") && strings.HasSuffix(file.Name(), ".db") {
			// 提取编号：private_001.db -> 001
			name := file.Name()
			numStr := strings.TrimPrefix(name, "private_")
			numStr = strings.TrimSuffix(numStr, ".db")

			if num, err := strconv.Atoi(numStr); err == nil && num > maxCounter {
				maxCounter = num
			}
		}
	}

	return maxCounter
}

// getDBPath 获取数据库文件路径
// 思路：根据平台、聊天类型、群号等信息确定数据库文件路径
func (p *DistributedStoragePlugin) getDBPath(platform, chatType, groupID string) string {
	if chatType == "group" && groupID != "" {
		// 群聊：每个群一个数据库文件
		// 路径：data/qq/groups/group_123456789.db
		return filepath.Join(p.baseDir, platform, "groups", fmt.Sprintf("group_%s.db", groupID))
	} else {
		// 私聊：按文件大小分割
		// 路径：data/qq/private/private_001.db
		counter := p.privateFileCounters[platform]
		if counter == 0 {
			counter = 1
			p.privateFileCounters[platform] = counter
		}

		dbPath := filepath.Join(p.baseDir, platform, "private", fmt.Sprintf("private_%03d.db", counter))

		// 检查当前文件是否超过大小限制
		if p.checkFileSizeLimit(dbPath) {
			// 文件过大，创建新文件
			counter++
			p.privateFileCounters[platform] = counter
			dbPath = filepath.Join(p.baseDir, platform, "private", fmt.Sprintf("private_%03d.db", counter))
		}

		return dbPath
	}
}

// checkFileSizeLimit 检查数据库文件是否超过大小限制
// 思路：检查文件大小，如果超过限制则需要创建新文件
func (p *DistributedStoragePlugin) checkFileSizeLimit(dbPath string) bool {
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		return false // 文件不存在，不需要分割
	}

	fileInfo, err := os.Stat(dbPath)
	if err != nil {
		return false
	}

	return fileInfo.Size() >= p.maxFileSize
}

// getOrCreateDB 获取或创建数据库连接
// 思路：从连接池中获取数据库连接，如果不存在则创建新连接
func (p *DistributedStoragePlugin) getOrCreateDB(dbPath string) (*gorm.DB, error) {
	// 检查连接池中是否已存在
	if db, exists := p.dbPool[dbPath]; exists {
		return db, nil
	}

	// 创建新的数据库连接
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // 减少日志输出
	})
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败 %s: %w", dbPath, err)
	}

	// 自动迁移表结构 - 使用优化的分布式存储结构
	if err := db.AutoMigrate(&DistributedMessage{}, &DistributedUser{}); err != nil {
		return nil, fmt.Errorf("数据库迁移失败 %s: %w", dbPath, err)
	}

	// 执行数据迁移，处理时间戳格式转换和表结构优化
	if err := p.migrateTimeStampData(db); err != nil {
		log.Printf("数据迁移警告 %s: %v", dbPath, err)
	}

	// 执行表结构优化迁移
	if err := p.migrateTableStructure(db); err != nil {
		log.Printf("表结构迁移警告 %s: %v", dbPath, err)
	}

	// 创建索引以提高查询性能
	if err := p.createIndexes(db); err != nil {
		log.Printf("创建索引失败 %s: %v", dbPath, err)
	}

	// 添加到连接池
	p.dbPool[dbPath] = db

	log.Printf("创建新数据库连接: %s", dbPath)
	return db, nil
}

// createIndexes 创建数据库索引
// 思路：为常用查询字段创建索引，提高查询性能，适配优化后的表结构
func (p *DistributedStoragePlugin) createIndexes(db *gorm.DB) error {
	indexes := []string{
		// 消息表索引 - 优化后的结构
		"CREATE INDEX IF NOT EXISTS idx_messages_user_created ON messages(user_id, created_at)",
		"CREATE INDEX IF NOT EXISTS idx_messages_created ON messages(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type)",
		"CREATE INDEX IF NOT EXISTS idx_messages_direction ON messages(direction)",
		"CREATE INDEX IF NOT EXISTS idx_messages_parent ON messages(parent_id)",
		// 用户表索引 - 优化后的结构（检查是否已存在）
		"CREATE INDEX IF NOT EXISTS idx_users_created ON users(created_at)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			return fmt.Errorf("创建索引失败: %s, 错误: %w", indexSQL, err)
		}
	}

	// 检查并创建用户平台ID的唯一索引（如果不存在）
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name='idx_users_platform_id'").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查索引是否存在失败: %w", err)
	}

	if count == 0 {
		// 在创建唯一索引之前，先清理重复的platform_id数据
		if err := p.cleanDuplicateUsers(db); err != nil {
			log.Printf("清理重复用户数据失败: %v", err)
		}

		// 索引不存在，创建它
		if err := db.Exec("CREATE UNIQUE INDEX idx_users_platform_id ON users(platform_id)").Error; err != nil {
			// 如果仍然失败，可能是数据重复，再次尝试清理并重新创建
			log.Printf("创建唯一索引失败，再次尝试清理重复数据: %v", err)
			if err := p.cleanDuplicateUsers(db); err != nil {
				log.Printf("再次清理重复用户数据失败: %v", err)
			} else {
				// 清理后重新尝试创建唯一索引
				if err := db.Exec("CREATE UNIQUE INDEX idx_users_platform_id ON users(platform_id)").Error; err != nil {
					log.Printf("清理后仍然无法创建唯一索引，创建普通索引: %v", err)
					if err := db.Exec("CREATE INDEX idx_users_platform_id_non_unique ON users(platform_id)").Error; err != nil {
						log.Printf("创建普通索引也失败，跳过索引创建: %v", err)
					}
				} else {
					log.Printf("清理后成功创建唯一索引")
				}
			}
		}
	}

	return nil
}

// cleanDuplicateUsers 清理重复的用户数据
// 思路：保留最新的用户记录，删除重复的旧记录
func (p *DistributedStoragePlugin) cleanDuplicateUsers(db *gorm.DB) error {
	// 查找重复的platform_id
	var duplicates []struct {
		PlatformID string `gorm:"column:platform_id"`
		Count      int    `gorm:"column:count"`
	}

	err := db.Raw(`
		SELECT platform_id, COUNT(*) as count
		FROM users
		GROUP BY platform_id
		HAVING COUNT(*) > 1
	`).Scan(&duplicates).Error
	if err != nil {
		return fmt.Errorf("查找重复用户失败: %w", err)
	}

	if len(duplicates) == 0 {
		return nil // 没有重复数据
	}

	log.Printf("发现 %d 个重复的platform_id，开始清理...", len(duplicates))

	for _, dup := range duplicates {
		// 保留最新的记录（ID最大的），删除其他重复记录
		// 先获取最大ID
		var maxID uint
		err = db.Raw("SELECT MAX(id) FROM users WHERE platform_id = ?", dup.PlatformID).Scan(&maxID).Error
		if err != nil {
			log.Printf("获取用户 %s 最大ID失败: %v", dup.PlatformID, err)
			continue
		}

		// 删除非最大ID的记录
		result := db.Exec("DELETE FROM users WHERE platform_id = ? AND id != ?", dup.PlatformID, maxID)
		if result.Error != nil {
			log.Printf("清理重复用户 %s 失败: %v", dup.PlatformID, result.Error)
		} else {
			log.Printf("清理重复用户 %s 成功，删除了 %d 条重复记录", dup.PlatformID, result.RowsAffected)
		}
	}

	return nil
}

// migrateTimeStampData 迁移时间戳数据格式
// 思路：将字符串格式的时间戳转换为Unix时间戳（int64）
func (p *DistributedStoragePlugin) migrateTimeStampData(db *gorm.DB) error {
	// 检查是否需要迁移用户表的时间戳
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM users WHERE typeof(created_at) = 'text'").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查用户表时间戳格式失败: %w", err)
	}

	if count > 0 {
		log.Printf("发现 %d 条用户记录需要时间戳格式迁移", count)

		// 迁移用户表的时间戳
		err = db.Exec(`
			UPDATE users
			SET created_at = CAST(strftime('%s', created_at) AS INTEGER),
			    updated_at = CAST(strftime('%s', updated_at) AS INTEGER)
			WHERE typeof(created_at) = 'text'
		`).Error
		if err != nil {
			return fmt.Errorf("迁移用户表时间戳失败: %w", err)
		}

		log.Printf("用户表时间戳迁移完成，共迁移 %d 条记录", count)
	}

	// 检查是否需要迁移消息表的时间戳
	var msgCount int64
	err = db.Raw("SELECT COUNT(*) FROM messages WHERE typeof(created_at) = 'text'").Scan(&msgCount).Error
	if err != nil {
		// 消息表可能不存在或为空，这是正常的
		return nil
	}

	if msgCount > 0 {
		log.Printf("发现 %d 条消息记录需要时间戳格式迁移", msgCount)

		// 迁移消息表的时间戳
		err = db.Exec(`
			UPDATE messages
			SET created_at = CAST(strftime('%s', created_at) AS INTEGER)
			WHERE typeof(created_at) = 'text'
		`).Error
		if err != nil {
			return fmt.Errorf("迁移消息表时间戳失败: %w", err)
		}

		log.Printf("消息表时间戳迁移完成，共迁移 %d 条记录", msgCount)
	}

	return nil
}

// migrateTableStructure 迁移表结构到优化版本
// 思路：移除冗余字段，优化存储结构
func (p *DistributedStoragePlugin) migrateTableStructure(db *gorm.DB) error {
	// 检查是否需要迁移messages表结构
	var columns []struct {
		Name string `gorm:"column:name"`
	}

	err := db.Raw("PRAGMA table_info(messages)").Scan(&columns).Error
	if err != nil {
		return fmt.Errorf("检查messages表结构失败: %w", err)
	}

	// 检查是否存在需要移除的冗余字段
	redundantFields := []string{
		"updated_at", "deleted_at", "session_id", "platform",
		"platform_msg_id", "is_ai_generated", "ai_model",
		"tokens_used", "metadata",
	}

	hasRedundantFields := false
	for _, col := range columns {
		for _, field := range redundantFields {
			if col.Name == field {
				hasRedundantFields = true
				break
			}
		}
		if hasRedundantFields {
			break
		}
	}

	if hasRedundantFields {
		log.Printf("检测到旧表结构，开始迁移到优化结构...")

		// 创建新的优化表结构（完全移除platform_msg_id等冗余字段）
		err = db.Exec(`
			CREATE TABLE IF NOT EXISTS messages_new (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				created_at INTEGER NOT NULL,
				user_id TEXT NOT NULL,
				content TEXT,
				type INTEGER NOT NULL DEFAULT 1,
				direction INTEGER NOT NULL,
				status INTEGER DEFAULT 1,
				parent_id TEXT
			)
		`).Error
		if err != nil {
			return fmt.Errorf("创建新表结构失败: %w", err)
		}

		// 迁移数据到新表
		err = db.Exec(`
			INSERT INTO messages_new (id, created_at, user_id, content, type, direction, status, parent_id)
			SELECT
				id,
				created_at,
				user_id,
				content,
				COALESCE(type, 1) as type,
				COALESCE(direction, 1) as direction,
				COALESCE(status, 1) as status,
				parent_id
			FROM messages
		`).Error
		if err != nil {
			return fmt.Errorf("迁移数据到新表失败: %w", err)
		}

		// 删除旧表并重命名新表
		err = db.Exec("DROP TABLE messages").Error
		if err != nil {
			return fmt.Errorf("删除旧表失败: %w", err)
		}

		err = db.Exec("ALTER TABLE messages_new RENAME TO messages").Error
		if err != nil {
			return fmt.Errorf("重命名新表失败: %w", err)
		}

		// 重新创建索引
		if err := p.createIndexes(db); err != nil {
			return fmt.Errorf("重新创建索引失败: %w", err)
		}

		log.Printf("表结构迁移完成，已移除冗余字段")
	}

	return nil
}

// StoreMessage 存储消息
// 思路：根据消息的平台和聊天类型，选择合适的数据库文件进行存储
func (p *DistributedStoragePlugin) StoreMessage(ctx context.Context, message plugins.ChatMessage) error {
	// 解析聊天类型和群号
	chatType := "private" // 默认私聊
	groupID := ""

	if message.Metadata != nil {
		if gid, ok := message.Metadata["group_id"].(string); ok && gid != "" {
			chatType = "group"
			groupID = gid
		}
	}

	// 获取数据库路径
	dbPath := p.getDBPath(message.Platform, chatType, groupID)

	// 获取数据库连接
	db, err := p.getOrCreateDB(dbPath)
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 查找或创建用户 - 使用兼容的用户结构
	var user DistributedUser
	err = db.Where("platform_id = ?", message.UserID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新用户
			// 思路：从消息中提取用户昵称和头像信息，创建完整的用户记录
			now := time.Now().Unix()
			// 从数据库路径推断平台名称
			platform := "unknown"
			if strings.Contains(dbPath, "/qq/") || strings.Contains(dbPath, "\\qq\\") {
				platform = "qq"
			} else if strings.Contains(dbPath, "/telegram/") || strings.Contains(dbPath, "\\telegram\\") {
				platform = "telegram"
			} else if strings.Contains(dbPath, "/wechat/") || strings.Contains(dbPath, "\\wechat\\") {
				platform = "wechat"
			}

			user = DistributedUser{
				PlatformID:   message.UserID,
				Platform:     platform,             // 从路径推断平台
				Nickname:     message.UserNickname, // 使用消息中的昵称
				Avatar:       message.UserAvatar,   // 使用消息中的头像
				Status:       "active",             // 默认状态
				IsVIP:        false,                // 默认非VIP
				MessageCount: 0,                    // 初始消息计数
				CreatedAt:    now,
				UpdatedAt:    now,
			}
			if err := db.Create(&user).Error; err != nil {
				return fmt.Errorf("创建用户失败: %w", err)
			}
			// log.Printf("为平台用户创建新用户记录: %s@%s -> ID:%d 昵称:%s (数据库: %s)", message.UserID, platform, user.ID, user.Nickname, filepath.Base(dbPath))
		} else {
			return fmt.Errorf("查询用户失败: %w", err)
		}
	} else {
		// 用户已存在，更新昵称和头像（如果有新信息）
		// 思路：用户昵称可能会变化，需要及时更新
		needUpdate := false
		if message.UserNickname != "" && user.Nickname != message.UserNickname {
			user.Nickname = message.UserNickname
			needUpdate = true
		}
		if message.UserAvatar != "" && user.Avatar != message.UserAvatar {
			user.Avatar = message.UserAvatar
			needUpdate = true
		}

		if needUpdate {
			user.UpdatedAt = time.Now().Unix()
			if err := db.Save(&user).Error; err != nil {
				log.Printf("更新用户信息失败: %v", err)
			} else {
				log.Printf("更新用户信息: %s 昵称:%s (数据库: %s)",
					message.UserID, user.Nickname, filepath.Base(dbPath))
			}
		}
	}

	// 构建优化的消息记录
	// 思路：使用优化结构，减少存储空间，提高性能
	messageRecord := DistributedMessage{
		// ID字段自增，不需要手动设置
		CreatedAt: time.Now().Unix(),                       // 时间戳（秒）
		UserID:    message.UserID,                          // 平台用户ID
		Content:   message.Content,                         // 消息内容
		Type:      getMessageTypeCode(message.MessageType), // 转换为数字类型
		Direction: DirectionIncoming,                       // 用户发送消息
		Status:    StatusSent,                              // 已发送状态
		ParentID:  "",                                      // 父消息ID（暂时为空）
	}

	// 存储消息
	if err := db.Create(&messageRecord).Error; err != nil {
		return fmt.Errorf("存储消息失败: %w", err)
	}

	// log.Printf("消息已存储到分布式数据库: %s (用户: %s, 平台: %s, 类型: %s, 数据库: %s)",		message.ID, message.UserID, message.Platform, chatType, filepath.Base(dbPath))

	return nil
}

// GetMessages 获取消息历史
// 思路：根据查询条件，从相应的数据库文件中查询消息
func (p *DistributedStoragePlugin) GetMessages(ctx context.Context, filter plugins.MessageFilter) ([]plugins.ChatMessage, error) {
	var allMessages []plugins.ChatMessage

	// 根据过滤条件确定需要查询的数据库文件
	dbPaths := p.getDBPathsForQuery(filter)

	for _, dbPath := range dbPaths {
		db, err := p.getOrCreateDB(dbPath)
		if err != nil {
			log.Printf("获取数据库连接失败 %s: %v", dbPath, err)
			continue
		}

		// 构建查询
		query := db.Model(&models.Message{}).Preload("User")

		// 应用过滤条件
		if filter.Platform != "" {
			query = query.Where("platform = ?", filter.Platform)
		}
		if filter.UserID != "" {
			query = query.Joins("JOIN users ON messages.user_id = users.id").
				Where("users.platform_id = ?", filter.UserID)
		}
		if filter.SessionID != "" {
			query = query.Where("session_id = ?", filter.SessionID)
		}
		if !filter.StartTime.IsZero() {
			query = query.Where("created_at >= ?", filter.StartTime)
		}
		if !filter.EndTime.IsZero() {
			query = query.Where("created_at <= ?", filter.EndTime)
		}

		// 执行查询
		var messages []models.Message
		if err := query.Order("created_at DESC").Limit(filter.Limit).Find(&messages).Error; err != nil {
			log.Printf("查询消息失败 %s: %v", dbPath, err)
			continue
		}

		// 转换为插件消息格式
		for _, msg := range messages {
			chatMsg := plugins.ChatMessage{
				ID:          msg.PlatformMsgID,
				UserID:      msg.User.PlatformID,
				Platform:    msg.Platform,
				Content:     msg.Content,
				MessageType: msg.Type, // 使用Type字段
				Timestamp:   msg.CreatedAt,
			}

			// 解析元数据
			if msg.Metadata != "" {
				var metadata map[string]interface{}
				if err := json.Unmarshal([]byte(msg.Metadata), &metadata); err == nil {
					chatMsg.Metadata = metadata
				}
			}

			allMessages = append(allMessages, chatMsg)
		}
	}

	return allMessages, nil
}

// getDBPathsForQuery 根据查询条件获取需要查询的数据库文件路径
// 思路：根据平台、群号等条件，确定需要查询哪些数据库文件
func (p *DistributedStoragePlugin) getDBPathsForQuery(filter plugins.MessageFilter) []string {
	var dbPaths []string

	platforms := []string{"qq", "telegram", "wechat", "test"}
	if filter.Platform != "" {
		platforms = []string{filter.Platform}
	}

	for _, platform := range platforms {
		// 如果指定了群ID，只查询对应的群数据库
		if filter.GroupID != "" {
			groupDBPath := filepath.Join(p.baseDir, platform, "groups", fmt.Sprintf("group_%s.db", filter.GroupID))
			if _, err := os.Stat(groupDBPath); err == nil {
				dbPaths = append(dbPaths, groupDBPath)
			}
		} else {
			// 查询私聊数据库
			privateDir := filepath.Join(p.baseDir, platform, "private")
			files, err := os.ReadDir(privateDir)
			if err == nil {
				for _, file := range files {
					if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
						dbPaths = append(dbPaths, filepath.Join(privateDir, file.Name()))
					}
				}
			}

			// 如果没有指定群ID，也查询所有群数据库
			if filter.GroupID == "" {
				groupsDir := filepath.Join(p.baseDir, platform, "groups")
				files, err := os.ReadDir(groupsDir)
				if err == nil {
					for _, file := range files {
						if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
							dbPaths = append(dbPaths, filepath.Join(groupsDir, file.Name()))
						}
					}
				}
			}
		}
	}

	return dbPaths
}

// GetStats 获取存储统计信息
// 思路：统计各个数据库文件的消息数量和存储大小
func (p *DistributedStoragePlugin) GetStats(ctx context.Context) (plugins.StorageStats, error) {
	stats := plugins.StorageStats{
		TotalMessages: 0,
		TotalUsers:    0,
		TotalSessions: 0,
		StorageSize:   0,
		MessagesByDay: make([]int64, 7), // 最近7天
		TopPlatforms:  make([]string, 0),
	}

	// 用于统计平台消息数量
	platformStats := make(map[string]int64)
	databaseCount := 0

	// 遍历所有平台目录
	platforms := []string{"qq", "telegram", "wechat", "test"}
	for _, platform := range platforms {
		platformPath := filepath.Join(p.baseDir, platform)
		if _, err := os.Stat(platformPath); os.IsNotExist(err) {
			continue
		}

		// 统计私聊数据库
		privateDir := filepath.Join(platformPath, "private")
		if files, err := os.ReadDir(privateDir); err == nil {
			for _, file := range files {
				if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
					dbPath := filepath.Join(privateDir, file.Name())
					if dbStats, err := p.getDBStats(dbPath, platform); err == nil {
						stats.TotalMessages += dbStats.MessageCount
						stats.TotalUsers += dbStats.UserCount
						stats.TotalSessions += dbStats.SessionCount
						stats.StorageSize += dbStats.FileSize
						databaseCount++
						platformStats[platform] += dbStats.MessageCount
					}
				}
			}
		}

		// 统计群聊数据库
		groupsDir := filepath.Join(platformPath, "groups")
		if files, err := os.ReadDir(groupsDir); err == nil {
			for _, file := range files {
				if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
					dbPath := filepath.Join(groupsDir, file.Name())
					if dbStats, err := p.getDBStats(dbPath, platform); err == nil {
						stats.TotalMessages += dbStats.MessageCount
						stats.TotalUsers += dbStats.UserCount
						stats.TotalSessions += dbStats.SessionCount
						stats.StorageSize += dbStats.FileSize
						databaseCount++
						platformStats[platform] += dbStats.MessageCount
					}
				}
			}
		}
	}

	// 设置最活跃的平台
	type platformStat struct {
		Platform string
		Count    int64
	}
	var platformStatsList []platformStat
	for platform, count := range platformStats {
		platformStatsList = append(platformStatsList, platformStat{Platform: platform, Count: count})
	}

	// 按消息数量排序
	sort.Slice(platformStatsList, func(i, j int) bool {
		return platformStatsList[i].Count > platformStatsList[j].Count
	})

	// 取前3个最活跃的平台
	for i := 0; i < len(platformStatsList) && i < 3; i++ {
		stats.TopPlatforms = append(stats.TopPlatforms, platformStatsList[i].Platform)
	}

	// 设置最新和最旧消息时间
	if stats.TotalMessages > 0 {
		// 这里简化处理，实际应该查询数据库获取最新和最旧消息时间
		stats.NewestMessage = time.Now()
		stats.OldestMessage = time.Now().AddDate(0, -1, 0) // 假设最旧消息是一个月前
	}

	log.Printf("分布式存储统计: %d条消息, %d个用户, %d个会话, %.2fMB存储, %d个数据库文件",
		stats.TotalMessages, stats.TotalUsers, stats.TotalSessions,
		float64(stats.StorageSize)/(1024*1024), databaseCount)

	return stats, nil
}

// getDBStats 获取单个数据库的统计信息
// 思路：查询数据库中的消息、用户、会话数量和文件大小
func (p *DistributedStoragePlugin) getDBStats(dbPath, platform string) (*dbStats, error) {
	db, err := p.getOrCreateDB(dbPath)
	if err != nil {
		return nil, err
	}

	stats := &dbStats{}

	// 统计消息数量
	db.Model(&models.Message{}).Count(&stats.MessageCount)

	// 统计用户数量
	db.Model(&models.User{}).Count(&stats.UserCount)

	// 统计会话数量
	db.Model(&models.Session{}).Count(&stats.SessionCount)

	// 获取文件大小
	if fileInfo, err := os.Stat(dbPath); err == nil {
		stats.FileSize = fileInfo.Size()
	}

	return stats, nil
}

// dbStats 数据库统计信息
type dbStats struct {
	MessageCount int64
	UserCount    int64
	SessionCount int64
	FileSize     int64
}

// BackupData 备份数据
// 思路：将指定时间范围的数据导出为JSON格式
func (p *DistributedStoragePlugin) BackupData(ctx context.Context, outputPath string) error {
	log.Printf("开始备份分布式存储数据到: %s", outputPath)

	// 创建备份目录
	if err := os.MkdirAll(outputPath, 0o755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 遍历所有数据库文件进行备份
	platforms := []string{"qq", "telegram", "wechat", "test"}
	for _, platform := range platforms {
		platformBackupDir := filepath.Join(outputPath, platform)
		if err := os.MkdirAll(platformBackupDir, 0o755); err != nil {
			continue
		}

		// 备份私聊数据库
		privateDir := filepath.Join(p.baseDir, platform, "private")
		if files, err := os.ReadDir(privateDir); err == nil {
			privateBackupDir := filepath.Join(platformBackupDir, "private")
			os.MkdirAll(privateBackupDir, 0o755)

			for _, file := range files {
				if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
					srcPath := filepath.Join(privateDir, file.Name())
					dstPath := filepath.Join(privateBackupDir, file.Name())
					p.copyFile(srcPath, dstPath)
				}
			}
		}

		// 备份群聊数据库
		groupsDir := filepath.Join(p.baseDir, platform, "groups")
		if files, err := os.ReadDir(groupsDir); err == nil {
			groupsBackupDir := filepath.Join(platformBackupDir, "groups")
			os.MkdirAll(groupsBackupDir, 0o755)

			for _, file := range files {
				if !file.IsDir() && strings.HasSuffix(file.Name(), ".db") {
					srcPath := filepath.Join(groupsDir, file.Name())
					dstPath := filepath.Join(groupsBackupDir, file.Name())
					p.copyFile(srcPath, dstPath)
				}
			}
		}
	}

	log.Printf("分布式存储数据备份完成")
	return nil
}

// copyFile 复制文件
// 思路：简单的文件复制功能
func (p *DistributedStoragePlugin) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
