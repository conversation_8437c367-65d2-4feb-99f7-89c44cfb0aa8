@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动QQ客服系统
echo ==================

REM 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Go环境，请先安装Go
    pause
    exit /b 1
)

REM 设置环境变量
set GIN_MODE=debug
set LOG_LEVEL=info

REM QQ平台配置
set QQ_ENABLED=true
set QQ_BASE_URL=http://localhost:3000
set QQ_TOKEN=

REM 数据库配置
set DB_TYPE=sqlite
set DB_DATABASE=aike_go.db

REM OpenAI配置（可选）
REM set OPENAI_API_KEY=your_api_key_here
REM set OPENAI_BASE_URL=https://api.openai.com/v1
REM set OPENAI_MODEL=gpt-3.5-turbo

echo 📋 当前配置：
echo   - QQ平台: %QQ_ENABLED%
echo   - NapCat地址: %QQ_BASE_URL%
echo   - 数据库: %DB_TYPE% (%DB_DATABASE%)
echo   - 服务器端口: 8080
echo.

REM 检查NapCat连接
echo 🔍 检查NapCat连接...
curl -s --connect-timeout 3 "%QQ_BASE_URL%/get_status" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告：无法连接到NapCat (%QQ_BASE_URL%)
    echo    请确保NapCat已启动并配置正确
    echo.
) else (
    echo ✅ NapCat连接正常
)

REM 构建项目
echo 🔨 构建项目...
if not exist "bin" mkdir bin
go build -o bin\aike_server.exe cmd\server\main.go
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo ✅ 构建成功

REM 启动服务器
echo.
echo 🎯 启动客服服务器...
echo    - HTTP服务: http://localhost:8080
echo    - WebSocket测试: http://localhost:8080/static/websocket_test.html
echo    - API文档: http://localhost:8080/health
echo.
echo 按 Ctrl+C 停止服务
echo.

REM 启动服务器
bin\aike_server.exe

echo.
echo 👋 服务器已停止
pause
