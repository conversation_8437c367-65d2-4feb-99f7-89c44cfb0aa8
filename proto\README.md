# AI客服系统 Protocol Buffers 定义

## 概述

本目录包含 AI客服系统的 Protocol Buffers (proto3) 定义文件，用于定义数据库结构、API 接口和 gRPC 服务。

## 文件结构

```
proto/
├── aike_service.proto    # 数据模型和基础消息定义
├── aike_api.proto        # gRPC 服务接口定义
├── Makefile             # 代码生成工具
└── README.md            # 本文档
```

## 主要内容

### 1. 数据库模型 (aike_service.proto)

#### 核心实体
- **User**: 用户模型，支持多平台用户统一管理
- **Message**: 消息模型，存储所有平台的消息记录
- **Session**: 会话模型，管理用户会话状态和上下文
- **Knowledge**: 知识库模型，支持向量搜索和分类管理
- **Script**: 脚本模型，管理 Lua 脚本的配置和执行

#### 枚举类型
- **UserStatus**: 用户状态 (active, blocked, inactive)
- **MessageType**: 消息类型 (text, image, file, audio, video, location)
- **MessageDirection**: 消息方向 (incoming, outgoing)
- **MessageStatus**: 消息状态 (sent, delivered, read, failed)
- **SessionStatus**: 会话状态 (active, inactive, closed)
- **KnowledgeStatus**: 知识库状态 (active, inactive, draft)
- **ScriptType**: 脚本类型 (message_handler, business_rule, plugin, filter)

### 2. API 接口定义 (aike_service.proto)

#### 请求/响应消息
- **ProcessMessageRequest/Response**: 消息处理接口
- **SendMessageRequest/Response**: 消息发送接口
- **ApiResponse**: 通用 API 响应格式
- **PaginationRequest/Response**: 分页查询支持

#### 平台相关
- **PlatformUserInfo**: 平台用户信息
- **IncomingMessage/OutgoingMessage**: 消息格式定义
- **WSMessage**: WebSocket 消息格式

#### 统计监控
- **SystemStats**: 系统统计信息
- **PlatformStats**: 平台统计信息

### 3. gRPC 服务定义 (aike_api.proto)

#### 核心服务
- **MessageService**: 消息处理服务
- **UserService**: 用户管理服务
- **KnowledgeService**: 知识库管理服务
- **ScriptService**: 脚本管理服务
- **PlatformService**: 平台集成服务
- **StatsService**: 统计监控服务

## 使用方法

### 1. 安装依赖工具

```bash
# 安装 Protocol Buffers 编译器
# macOS
brew install protobuf

# Ubuntu
sudo apt-get install protobuf-compiler

# Windows
# 下载 https://github.com/protocolbuffers/protobuf/releases

# 安装 Go 插件
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# 安装文档生成插件 (可选)
go install github.com/pseudomuto/protoc-gen-doc/cmd/protoc-gen-doc@latest
```

### 2. 生成代码

```bash
# 进入 proto 目录
cd proto

# 检查工具安装
make check-tools

# 生成 Go 代码和文档
make all

# 或者分别执行
make generate  # 只生成 Go 代码
make docs      # 只生成文档
```

### 3. 验证和格式化

```bash
# 验证 proto 文件语法
make validate

# 格式化 proto 文件
make format
```

### 4. 开发模式

```bash
# 监听文件变化并自动重新生成 (需要安装 fswatch)
make watch
```

## 生成的文件

执行 `make all` 后，将在以下位置生成文件：

```
internal/proto/
├── aike_service.pb.go      # 数据模型 Go 代码
├── aike_api.pb.go          # API 接口 Go 代码
├── aike_api_grpc.pb.go     # gRPC 服务 Go 代码
└── ...

docs/proto/
├── index.html              # HTML 格式文档
└── README.md               # Markdown 格式文档
```

## 在项目中使用

### 1. 导入生成的包

```go
import (
    aikev1 "aike_go/internal/proto/aike/v1"
    "google.golang.org/grpc"
)
```

### 2. 使用数据模型

```go
// 创建用户
user := &aikev1.User{
    PlatformId: "qq_123456",
    Platform:   "qq",
    Nickname:   "张三",
    Status:     aikev1.UserStatus_USER_STATUS_ACTIVE,
    IsVip:      false,
}

// 创建消息
message := &aikev1.Message{
    UserId:    user.Id,
    Content:   "你好，工作时间是什么？",
    Type:      aikev1.MessageType_MESSAGE_TYPE_TEXT,
    Direction: aikev1.MessageDirection_MESSAGE_DIRECTION_INCOMING,
    Platform:  "qq",
}
```

### 3. 实现 gRPC 服务

```go
type messageServiceServer struct {
    aikev1.UnimplementedMessageServiceServer
}

func (s *messageServiceServer) ProcessMessage(
    ctx context.Context,
    req *aikev1.ProcessMessageRequest,
) (*aikev1.ProcessMessageResponse, error) {
    // 实现消息处理逻辑
    return &aikev1.ProcessMessageResponse{
        Reply:     "这是AI的回复",
        SessionId: req.SessionId,
        TokensUsed: 150,
    }, nil
}

// 注册服务
server := grpc.NewServer()
aikev1.RegisterMessageServiceServer(server, &messageServiceServer{})
```

### 4. 客户端调用

```go
// 连接 gRPC 服务
conn, err := grpc.Dial("localhost:9090", grpc.WithInsecure())
if err != nil {
    log.Fatal(err)
}
defer conn.Close()

// 创建客户端
client := aikev1.NewMessageServiceClient(conn)

// 调用服务
response, err := client.ProcessMessage(context.Background(), &aikev1.ProcessMessageRequest{
    Platform:   "qq",
    PlatformId: "123456",
    Content:    "你好",
    MessageType: "text",
})
```

## 设计原则

### 1. 向后兼容性
- 使用 proto3 语法
- 字段编号不重复使用
- 新字段使用可选或默认值

### 2. 类型安全
- 使用枚举代替字符串常量
- 明确的字段类型定义
- 避免使用 `any` 类型

### 3. 性能优化
- 合理的字段编号分配
- 避免嵌套过深的消息结构
- 使用适当的数据类型

### 4. 可扩展性
- 预留字段编号空间
- 使用 `oneof` 处理互斥字段
- 支持元数据扩展

## 版本管理

### 版本策略
- 主版本号：不兼容的 API 变更
- 次版本号：向后兼容的功能新增
- 修订号：向后兼容的问题修正

### 变更记录
- v1.0.0: 初始版本，包含核心数据模型和 API 定义

## 最佳实践

### 1. 字段命名
- 使用 snake_case 命名
- 字段名称要清晰明确
- 避免缩写和简写

### 2. 消息设计
- 保持消息结构简单
- 避免循环引用
- 合理使用嵌套消息

### 3. 服务设计
- 单一职责原则
- 合理的方法粒度
- 统一的错误处理

### 4. 文档维护
- 及时更新注释
- 提供使用示例
- 记录变更历史

## 故障排除

### 常见问题

1. **编译错误**
   ```
   protoc: command not found
   ```
   解决：安装 Protocol Buffers 编译器

2. **插件未找到**
   ```
   protoc-gen-go: program not found or is not executable
   ```
   解决：安装 Go 插件并确保在 PATH 中

3. **导入错误**
   ```
   import "aike_service.proto": file not found
   ```
   解决：检查 proto_path 设置

### 调试技巧

1. **验证语法**
   ```bash
   make validate
   ```

2. **查看生成的代码**
   ```bash
   make generate
   cat ../internal/proto/aike_service.pb.go
   ```

3. **检查工具版本**
   ```bash
   protoc --version
   protoc-gen-go --version
   ```

## 相关资源

- [Protocol Buffers 官方文档](https://developers.google.com/protocol-buffers)
- [gRPC Go 快速开始](https://grpc.io/docs/languages/go/quickstart/)
- [Protocol Buffers 语言指南](https://developers.google.com/protocol-buffers/docs/proto3)
- [gRPC 最佳实践](https://grpc.io/docs/guides/)
