{"url": "/scripts", "schema": {"type": "page", "title": "脚本管理", "body": {"type": "crud", "api": {"method": "get", "url": "/api/v1/scripts/", "adaptor": "return {data: payload.scripts || [], total: payload.total || 0};"}, "headerToolbar": [{"type": "button", "label": "新增脚本", "level": "primary", "actionType": "dialog", "dialog": {"title": "新增脚本", "size": "lg", "body": {"type": "form", "api": "post:/api/v1/scripts", "body": [{"type": "input-text", "name": "name", "label": "脚本名称", "required": true}, {"type": "textarea", "name": "description", "label": "脚本描述", "minRows": 2}, {"type": "select", "name": "type", "label": "脚本类型", "required": true, "options": [{"label": "消息处理器", "value": "SCRIPT_TYPE_MESSAGE_HANDLER"}, {"label": "业务规则", "value": "SCRIPT_TYPE_BUSINESS_RULE"}, {"label": "插件", "value": "SCRIPT_TYPE_PLUGIN"}, {"label": "过滤器", "value": "SCRIPT_TYPE_FILTER"}]}, {"type": "input-number", "name": "priority", "label": "优先级", "value": 50, "min": 1, "max": 100}, {"type": "editor", "name": "content", "label": "脚本内容", "language": "lua", "required": true, "size": "xxl"}, {"type": "json-editor", "name": "config", "label": "配置参数", "placeholder": "JSON格式的配置参数"}, {"type": "switch", "name": "enabled", "label": "启用脚本", "value": true}]}}}, {"type": "button", "label": "批量操作", "level": "info", "actionType": "dropdown", "buttons": [{"type": "button", "label": "批量启用", "actionType": "ajax", "api": "put:/api/v1/scripts/batch-enable", "confirmText": "确定要启用选中的脚本吗？"}, {"type": "button", "label": "批量禁用", "actionType": "ajax", "api": "put:/api/v1/scripts/batch-disable", "confirmText": "确定要禁用选中的脚本吗？"}]}], "columns": [{"type": "checkbox", "name": "id", "label": "", "toggled": false, "width": 50}, {"name": "id", "label": "ID", "width": 80, "sortable": true}, {"name": "name", "label": "脚本名称", "searchable": true}, {"name": "description", "label": "描述", "width": 80, "type": "tpl", "tpl": "${description | truncate:50}"}, {"name": "type", "label": "类型", "width": 80, "type": "mapping", "map": {"business_rule": "<span class='label label-success'>业务规则</span>", "message_handler": "<span class='label label-primary'>消息处理器</span>", "SCRIPT_TYPE_PLUGIN": "<span class='label label-info'>插件</span>", "message_filter": "<span class='label label-warning'>过滤器</span>"}}, {"name": "enabled", "label": "状态", "width": 60, "type": "mapping", "map": {"true": "<span class='label label-success'>已启用</span>", "false": "<span class='label label-default'>已禁用</span>"}}, {"name": "priority", "label": "优先级", "width": 40, "sortable": true}, {"name": "created_at", "label": "创建时间", "width": 100, "type": "datetime", "sortable": true}, {"type": "operation", "label": "操作", "width": 250, "buttons": [{"type": "button", "label": "查看", "level": "link", "actionType": "dialog", "dialog": {"title": "脚本详情", "size": "lg", "body": {"type": "service", "api": "get:/api/v1/scripts/${id}", "body": [{"type": "property", "column": 2, "items": [{"label": "脚本ID", "content": "${id}"}, {"label": "脚本名称", "content": "${name}"}, {"label": "描述", "content": "${description}"}, {"label": "类型", "content": "${type}"}, {"label": "状态", "content": "${enabled ? '已启用' : '已禁用'}"}, {"label": "优先级", "content": "${priority}"}, {"label": "执行次数", "content": "${execution_count}"}, {"label": "最后执行", "content": "${last_executed_at}"}, {"label": "创建时间", "content": "${created_at}"}, {"label": "更新时间", "content": "${updated_at}"}]}, {"type": "divider"}, {"type": "static", "label": "脚本内容", "value": "${content}", "className": "bg-light p-3", "style": {"fontFamily": "monospace", "whiteSpace": "pre-wrap"}}, {"type": "divider"}, {"type": "static", "label": "配置参数", "value": "${config}", "className": "bg-light p-3", "style": {"fontFamily": "monospace", "whiteSpace": "pre-wrap"}}]}}}, {"type": "button", "label": "编辑", "level": "link", "actionType": "dialog", "dialog": {"title": "编辑脚本", "size": "lg", "body": {"type": "form", "api": "put:/api/v1/scripts/${id}", "initApi": "get:/api/v1/scripts/${id}", "body": [{"type": "input-text", "name": "name", "label": "脚本名称", "required": true}, {"type": "textarea", "name": "description", "label": "脚本描述", "minRows": 2}, {"type": "select", "name": "type", "label": "脚本类型", "required": true, "options": [{"label": "消息处理器", "value": "SCRIPT_TYPE_MESSAGE_HANDLER"}, {"label": "业务规则", "value": "SCRIPT_TYPE_BUSINESS_RULE"}, {"label": "插件", "value": "SCRIPT_TYPE_PLUGIN"}, {"label": "过滤器", "value": "SCRIPT_TYPE_FILTER"}]}, {"type": "input-number", "name": "priority", "label": "优先级", "min": 1, "max": 100}, {"type": "editor", "name": "content", "label": "脚本内容", "language": "lua", "required": true, "size": "xxl"}, {"type": "json-editor", "name": "config", "label": "配置参数"}, {"type": "switch", "name": "enabled", "label": "启用脚本"}]}}}, {"type": "button", "label": "执行", "level": "link", "className": "text-success", "actionType": "dialog", "dialog": {"title": "执行脚本", "body": {"type": "form", "api": "post:/api/v1/scripts/${id}/execute", "body": [{"type": "json-editor", "name": "input_data", "label": "输入数据", "placeholder": "JSON格式的输入数据"}, {"type": "json-editor", "name": "context", "label": "执行上下文", "placeholder": "JSON格式的上下文数据"}]}}}, {"type": "button", "label": "${enabled ? '禁用' : '启用'}", "level": "link", "className": "${enabled ? 'text-warning' : 'text-success'}", "actionType": "ajax", "confirmText": "${enabled ? '确定要禁用该脚本吗？' : '确定要启用该脚本吗？'}", "api": "put:/api/v1/scripts/${id}/toggle"}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "actionType": "ajax", "confirmText": "确定要删除该脚本吗？此操作不可恢复！", "api": "delete:/api/v1/scripts/${id}"}]}]}}}