package tests

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/models"
	"aike_go/internal/server"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// IntegrationTestSuite 集成测试套件
// 思路：测试整个系统的端到端功能
type IntegrationTestSuite struct {
	suite.Suite
	server     *server.Server
	testServer *httptest.Server
	db         *gorm.DB
}

// SetupSuite 集成测试初始化
// 思路：设置完整的测试环境，包括数据库、服务器等
func (suite *IntegrationTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)
	suite.db = db

	// 设置全局数据库连接
	database.SetDB(db)

	// 运行数据库迁移
	err = database.AutoMigrate()
	suite.Require().NoError(err)

	// 创建测试配置
	cfg := &config.Config{
		Host: "localhost",
		Port: 0, // 使用随机端口
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
		OpenAI: config.OpenAIConfig{
			APIKey:  "test-api-key",
			BaseURL: "https://api.openai.com/v1",
			Model:   "gpt-3.5-turbo",
		},
	}

	// 创建服务器
	suite.server = server.New(cfg)

	// 创建测试服务器
	suite.testServer = httptest.NewServer(suite.server.GetEngine())

	// 初始化测试数据
	suite.setupTestData()
}

// TearDownSuite 集成测试清理
func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.testServer != nil {
		suite.testServer.Close()
	}
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// setupTestData 设置测试数据
// 思路：创建测试所需的基础数据
func (suite *IntegrationTestSuite) setupTestData() {
	// 创建测试知识库条目
	knowledgeItems := []*models.Knowledge{
		{
			Title:     "工作时间",
			Content:   "我们的工作时间是周一至周五 9:00-18:00",
			Keywords:  "工作时间,上班时间,营业时间",
			Category:  "基础信息",
			Status:    "active",
			CreatedBy: "system",
		},
		{
			Title:     "联系方式",
			Content:   "客服电话：************",
			Keywords:  "联系方式,电话,客服",
			Category:  "基础信息",
			Status:    "active",
			CreatedBy: "system",
		},
	}

	for _, item := range knowledgeItems {
		err := suite.db.Create(item).Error
		suite.Require().NoError(err)
	}
}

// TestHealthCheck 测试健康检查接口
// 思路：验证基础的健康检查功能
func (suite *IntegrationTestSuite) TestHealthCheck() {
	resp, err := http.Get(suite.testServer.URL + "/health")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.Equal("healthy", response["status"])
}

// TestSendMessage 测试发送消息接口
// 思路：验证消息发送和处理的完整流程
func (suite *IntegrationTestSuite) TestSendMessage() {
	// 准备请求数据
	requestData := map[string]interface{}{
		"user_id":  "test_user_123",
		"platform": "test",
		"content":  "工作时间是什么？",
		"type":     "text",
	}

	jsonData, err := json.Marshal(requestData)
	suite.NoError(err)

	// 发送请求
	resp, err := http.Post(
		suite.testServer.URL+"/api/v1/messages/send",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.NoError(err)
	defer resp.Body.Close()

	// 验证响应
	suite.Equal(http.StatusOK, resp.StatusCode)

	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)

	suite.True(response["success"].(bool))
	
	data := response["data"].(map[string]interface{})
	suite.Contains(data["content"].(string), "9:00-18:00")
	suite.Equal("outgoing", data["direction"])
}

// TestKnowledgeAPI 测试知识库API
// 思路：验证知识库的增删改查功能
func (suite *IntegrationTestSuite) TestKnowledgeAPI() {
	// 测试创建知识库条目
	createData := map[string]interface{}{
		"title":      "测试问题",
		"content":    "测试答案",
		"keywords":   "测试,问题",
		"category":   "测试分类",
		"status":     "active",
		"created_by": "test_user",
	}

	jsonData, err := json.Marshal(createData)
	suite.NoError(err)

	resp, err := http.Post(
		suite.testServer.URL+"/api/v1/knowledge/",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var createResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&createResponse)
	suite.NoError(err)
	suite.True(createResponse["success"].(bool))

	// 获取创建的条目ID
	createdData := createResponse["data"].(map[string]interface{})
	knowledgeID := int(createdData["id"].(float64))

	// 测试获取知识库列表
	resp, err = http.Get(suite.testServer.URL + "/api/v1/knowledge/")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var listResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&listResponse)
	suite.NoError(err)
	suite.True(listResponse["success"].(bool))

	data := listResponse["data"].(map[string]interface{})
	items := data["items"].([]interface{})
	suite.GreaterOrEqual(len(items), 1)

	// 测试搜索知识库
	searchData := map[string]interface{}{
		"query": "测试",
		"limit": 10,
	}

	jsonData, err = json.Marshal(searchData)
	suite.NoError(err)

	resp, err = http.Post(
		suite.testServer.URL+"/api/v1/knowledge/search",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var searchResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&searchResponse)
	suite.NoError(err)
	suite.True(searchResponse["success"].(bool))

	searchResults := searchResponse["data"].([]interface{})
	suite.GreaterOrEqual(len(searchResults), 1)

	// 测试删除知识库条目
	req, err := http.NewRequest("DELETE", 
		fmt.Sprintf("%s/api/v1/knowledge/%d", suite.testServer.URL, knowledgeID), 
		nil)
	suite.NoError(err)

	client := &http.Client{}
	resp, err = client.Do(req)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)
}

// TestPlatformAPI 测试平台API
// 思路：验证平台管理和统计功能
func (suite *IntegrationTestSuite) TestPlatformAPI() {
	// 测试获取平台列表
	resp, err := http.Get(suite.testServer.URL + "/api/v1/platforms/")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.True(response["success"].(bool))

	// 测试获取平台统计
	resp, err = http.Get(suite.testServer.URL + "/api/v1/platforms/stats")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.True(response["success"].(bool))
}

// TestMonitoringAPI 测试监控API
// 思路：验证系统监控和指标功能
func (suite *IntegrationTestSuite) TestMonitoringAPI() {
	// 先发送几个请求生成一些指标数据
	for i := 0; i < 3; i++ {
		http.Get(suite.testServer.URL + "/health")
		time.Sleep(10 * time.Millisecond)
	}

	// 测试获取系统指标
	resp, err := http.Get(suite.testServer.URL + "/api/v1/monitor/metrics")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.True(response["success"].(bool))

	data := response["data"].(map[string]interface{})
	suite.Contains(data, "total_requests")
	suite.Contains(data, "active_requests")

	// 测试详细健康检查
	resp, err = http.Get(suite.testServer.URL + "/api/v1/monitor/health/detailed")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.True(response["success"].(bool))

	healthData := response["data"].(map[string]interface{})
	suite.Contains(healthData, "status")
	suite.Contains(healthData, "database")
	suite.Contains(healthData, "metrics")
}

// TestWebHookFlow 测试WebHook流程
// 思路：验证平台WebHook的完整处理流程
func (suite *IntegrationTestSuite) TestWebHookFlow() {
	// 模拟QQ平台的WebHook数据
	webhookData := map[string]interface{}{
		"post_type":    "message",
		"message_type": "private",
		"time":         time.Now().Unix(),
		"self_id":      123456,
		"user_id":      789012,
		"message":      "联系方式",
		"raw_message":  "联系方式",
		"message_id":   12345,
		"sender": map[string]interface{}{
			"user_id":  789012,
			"nickname": "测试用户",
			"card":     "",
			"role":     "member",
		},
	}

	jsonData, err := json.Marshal(webhookData)
	suite.NoError(err)

	// 发送WebHook请求
	resp, err := http.Post(
		suite.testServer.URL+"/api/v1/platforms/qq/webhook",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusOK, resp.StatusCode)

	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.True(response["success"].(bool))

	// 验证用户和消息已创建
	var user models.User
	err = suite.db.Where("platform_user_id = ?", "789012").First(&user).Error
	suite.NoError(err)
	suite.Equal("测试用户", user.Nickname)

	var messages []models.Message
	err = suite.db.Where("user_id = ?", user.ID).Find(&messages).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(messages), 2) // 输入消息 + 回复消息
}

// TestErrorHandling 测试错误处理
// 思路：验证系统的错误处理机制
func (suite *IntegrationTestSuite) TestErrorHandling() {
	// 测试无效的JSON请求
	resp, err := http.Post(
		suite.testServer.URL+"/api/v1/messages/send",
		"application/json",
		bytes.NewBuffer([]byte("invalid json")),
	)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusBadRequest, resp.StatusCode)

	// 测试不存在的路径
	resp, err = http.Get(suite.testServer.URL + "/api/v1/nonexistent")
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusNotFound, resp.StatusCode)

	// 测试无效的知识库ID
	req, err := http.NewRequest("DELETE", 
		suite.testServer.URL+"/api/v1/knowledge/99999", 
		nil)
	suite.NoError(err)

	client := &http.Client{}
	resp, err = client.Do(req)
	suite.NoError(err)
	defer resp.Body.Close()

	suite.Equal(http.StatusNotFound, resp.StatusCode)
}

// 运行集成测试套件
func TestIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}

// 性能测试

// TestConcurrentRequests 测试并发请求处理
// 思路：验证系统在并发请求下的表现
func TestConcurrentRequests(t *testing.T) {
	// 设置测试环境
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	database.SetDB(db)
	database.AutoMigrate()

	cfg := &config.Config{
		Host: "localhost",
		Port: 0,
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	srv := server.New(cfg)
	testServer := httptest.NewServer(srv.GetEngine())
	defer testServer.Close()

	// 并发发送请求
	concurrency := 10
	requests := 50

	done := make(chan bool, concurrency)
	errors := make(chan error, requests)

	for i := 0; i < concurrency; i++ {
		go func() {
			defer func() { done <- true }()
			
			for j := 0; j < requests/concurrency; j++ {
				resp, err := http.Get(testServer.URL + "/health")
				if err != nil {
					errors <- err
					continue
				}
				resp.Body.Close()
				
				if resp.StatusCode != http.StatusOK {
					errors <- fmt.Errorf("unexpected status code: %d", resp.StatusCode)
				}
			}
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < concurrency; i++ {
		<-done
	}

	// 检查错误
	close(errors)
	for err := range errors {
		t.Errorf("Request failed: %v", err)
	}
}
