go开发一个 客服服务端
可以接入openai api接口
有自己的知识库
存储聊天记录
有http ws接口
通过包装接口 可以支持 qq 微信 tg 千牛 ,proto
集成github.com/yuin/gopher-lua来编写脚本


NapCat.openapi.json 是napcat的接口文档,现在napcat通过一个ws客户端反向连接到我的接口,应该怎么做才能调用这个qq的包装
[x] 把存储聊天记录插件化 lua基本插件化 可以启用 禁用 配置
[x] sqlite存储聊天记录按平台,私聊群聊 划分目录,群号划分db,私聊考虑单db,但是按大小分文件
[x] 现在调试信息太多,应该用开关控制 不显示管太多的调试信息
[x] 吧lua引擎独立一个包出来,方便统一注册调用的 后续集成到其他服务端
[x] ai增加硅基流动的接口
[x]数据库存储的信息有很多不需要 ,比如 messsages表 ,updatede_at session_id  plaatform(因为路径就提现了)  platform_msg_id  metadata,还有一些类型用数字减少占用 比如 type  direction status用整数代替状态,created_at用时间戳减少文本长度
[ ] 发送消息接口没有真正的实现
[ ] 每个信息ai没找到数据都在回复,这不对,比如这个例子
2025/07/16 07:38:27 [Lua:info] AI客服处理用户 349916136 的消息: 10公里的pb还是3月份跑的，夏天我能力下降明显
2025/07/16 07:38:27 [Lua:info] 开始知识库搜索: 10公里的pb还是3月份跑的，夏天我能力下降明显
2025/07/16 07:38:27 [Lua] API调用: POST /api/v1/knowledge/search
2025/07/16 07:38:27 [Lua] 发送消息给 349916136: 📚 这里是常见问题的详细解答内容
[x] 配置文件设置openai接口 或者使用openai接口规范的api是全局的,如果我想给某个平台或者某个私聊某个群聊设置不同的ai api就不行了.应该可以通过lua脚本来实现这个控制
[x] getOrCreateSession 里 关于sessions的操作估计有错误,现在所有表的sessions都是空记录 ,其实也没有用到
[x] 把数据库结构和 api访问返回 总结成proto3文件
[ ] 根据proto下单proto文件定义的api 实现一个前端来管理ai客服
