package napcat

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"sync"
	"time"

	"aike_go/internal/interfaces"
	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// NapCatWebSocketHandler NapCat WebSocket反向连接处理器
// 思路：处理NapCat通过WebSocket反向连接发送的QQ消息和事件
type NapCatWebSocketHandler struct {
	upgrader      websocket.Upgrader
	connections   map[string]*websocket.Conn // 连接池，key为连接ID
	connMutex     sync.RWMutex
	chatService   *services.ChatService
	scriptEngine  interfaces.ScriptEngine
	messageFilter interface{} // 消息过滤器，避免循环导入使用interface{}
}

// NewNapCatWebSocketHandler 创建NapCat WebSocket处理器
// 思路：初始化WebSocket升级器和连接管理
func NewNapCatWebSocketHandler(chatService *services.ChatService, scriptEngine interfaces.ScriptEngine) *NapCatWebSocketHandler {
	handler := &NapCatWebSocketHandler{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 允许所有来源，生产环境应该限制
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		connections:  make(map[string]*websocket.Conn),
		chatService:  chatService,
		scriptEngine: scriptEngine,
	}

	// 初始化消息过滤器
	handler.initMessageFilter()

	return handler
}

// initMessageFilter 初始化消息过滤器
// 思路：加载配置文件并创建过滤器
func (h *NapCatWebSocketHandler) initMessageFilter() {
	// 读取配置文件
	configData, err := ioutil.ReadFile("config/message_rules.yaml")
	if err != nil {
		log.Printf("读取消息规则配置失败，使用默认规则: %v", err)
		// 使用默认配置
		configData = []byte(`
global:
  enabled: true
  debug_mode: true

private_message:
  enabled: true
  response_conditions:
    keywords: ["客服", "帮助", "help", "问题"]
    min_length: 5

group_message:
  enabled: true
  response_conditions:
    mention_required: true
    bot_names: ["AI客服", "机器人"]
    keywords: ["客服", "帮助", "问题"]
`)
	}

	// 创建消息过滤器（避免循环导入，使用反射或接口）
	// 这里暂时设置为nil，后续可以通过setter注入
	h.messageFilter = nil

	// 记录配置大小用于调试
	log.Printf("消息过滤器初始化完成，配置大小: %d bytes", len(configData))
}

// HandleWebSocket 处理WebSocket连接
// 思路：升级HTTP连接为WebSocket，处理NapCat的反向连接
func (h *NapCatWebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 生成连接ID
	connID := fmt.Sprintf("napcat_%d", time.Now().UnixNano())

	// 添加到连接池
	h.connMutex.Lock()
	h.connections[connID] = conn
	h.connMutex.Unlock()

	// 连接关闭时清理
	defer func() {
		h.connMutex.Lock()
		delete(h.connections, connID)
		h.connMutex.Unlock()
		log.Printf("NapCat连接已断开: %s", connID)
	}()

	log.Printf("NapCat连接已建立: %s", connID)

	// 处理消息循环
	for {
		// 读取消息
		messageType, data, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}

		if messageType == websocket.TextMessage {
			// 处理文本消息
			go h.handleMessage(connID, data)
		}
	}
}

// NapCatMessage NapCat消息结构
// 思路：根据NapCat的OneBot协议定义消息结构
type NapCatMessage struct {
	PostType    string `json:"post_type"`    // 上报类型：message, notice, request, meta_event
	MessageType string `json:"message_type"` // 消息类型：private, group
	SubType     string `json:"sub_type"`     // 子类型
	MessageID   int64  `json:"message_id"`   // 消息ID
	UserID      int64  `json:"user_id"`      // 发送者QQ号
	GroupID     int64  `json:"group_id"`     // 群号（群消息时）
	Message     string `json:"message"`      // 消息内容
	RawMessage  string `json:"raw_message"`  // 原始消息内容
	Font        int    `json:"font"`         // 字体
	Sender      struct {
		UserID   int64  `json:"user_id"`
		Nickname string `json:"nickname"`
		Card     string `json:"card"`  // 群名片
		Sex      string `json:"sex"`   // 性别
		Age      int    `json:"age"`   // 年龄
		Area     string `json:"area"`  // 地区
		Level    string `json:"level"` // 等级
		Role     string `json:"role"`  // 群角色
		Title    string `json:"title"` // 专属头衔
	} `json:"sender"`
	Time int64 `json:"time"` // 时间戳
}

// handleMessage 处理接收到的消息
// 思路：解析NapCat消息，转换为内部格式，调用脚本引擎处理
func (h *NapCatWebSocketHandler) handleMessage(connID string, data []byte) {
	// 添加panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			log.Printf("处理消息时发生panic: %v", r)
		}
	}()
	// log.Printf("开始处理消息，连接ID: %s，数据长度: %d", connID, len(data))

	var napCatMsg NapCatMessage
	if err := json.Unmarshal(data, &napCatMsg); err != nil {
		log.Printf("解析NapCat消息失败: %v data:%s", err, string(data))
		return
	}

	// log.Printf("收到NapCat消息: PostType=%s, MessageType=%s, UserID=%d, Content=%s",		napCatMsg.PostType, napCatMsg.MessageType, napCatMsg.UserID, napCatMsg.Message)

	// 只处理消息类型的上报
	if napCatMsg.PostType != "message" {
		// log.Printf("忽略非消息类型: %s", napCatMsg.PostType)
		return
	}

	// 转换为内部消息格式
	incomingMsg := &interfaces.IncomingMessage{
		ID:       fmt.Sprintf("%d", napCatMsg.MessageID),
		Content:  napCatMsg.Message,
		Platform: "qq",
		From: &interfaces.UserInfo{
			PlatformUserID: fmt.Sprintf("%d", napCatMsg.UserID),
			Nickname:       napCatMsg.Sender.Nickname,
			Platform:       "qq",
		},
		Timestamp: time.Unix(napCatMsg.Time, 0),
		Metadata: map[string]string{
			"message_type": napCatMsg.MessageType,
			"group_id":     fmt.Sprintf("%d", napCatMsg.GroupID),
			"raw_message":  napCatMsg.RawMessage,
			"conn_id":      connID,
		},
	}

	// 消息过滤检查
	if h.messageFilter != nil {
		// 类型断言，检查是否有ShouldRespond方法
		if filter, ok := h.messageFilter.(interface {
			ShouldRespond(message *interfaces.IncomingMessage) interface{}
		}); ok {
			filterResult := filter.ShouldRespond(incomingMsg)

			// 检查过滤结果
			if result, ok := filterResult.(interface {
				ShouldRespond() bool
				Reason() string
			}); ok {
				if !result.ShouldRespond() {
					log.Printf("消息被过滤器拦截: %s, 原因: %s", incomingMsg.Content, result.Reason())
					return
				}
				log.Printf("消息通过过滤器检查: %s", result.Reason())
			}
		}
	} else {
		log.Printf("消息过滤器未初始化，跳过过滤检查")
	}

	// 调用脚本引擎处理消息
	ctx := context.Background()

	// 检查脚本引擎是否可用
	if h.scriptEngine == nil {
		log.Printf("脚本引擎未初始化，跳过脚本处理")
	} else {
		scriptCtx := &interfaces.ScriptContext{
			Message:   incomingMsg,
			Platform:  "qq",
			Variables: make(map[string]interface{}),
		}

		// 执行消息过滤器脚本
		filterResults, err := h.scriptEngine.ExecuteScriptsByType(ctx, "message_filter", scriptCtx)
		if err != nil {
			log.Printf("执行过滤器脚本失败: %v", err)
		} else {
			// 检查是否被过滤
			for _, result := range filterResults {
				if result.Success && result.Result == false {
					log.Printf("消息被过滤器拦截: %s", incomingMsg.Content)
					return
				}
			}
		}

		// 执行业务规则脚本
		_, err = h.scriptEngine.ExecuteScriptsByType(ctx, "business_rule", scriptCtx)
		if err != nil {
			log.Printf("执行业务规则脚本失败: %v", err)
		} else {
			// log.Printf("业务规则执行结果: %+v", ruleResults)
		}

		// 执行消息处理器脚本
		_, err = h.scriptEngine.ExecuteScriptsByType(ctx, "message_handler", scriptCtx)
		if err != nil {
			log.Printf("执行消息处理器脚本失败: %v", err)
		} else {
			// log.Printf("消息处理器执行结果: %+v", handlerResults)
		}
	}

	// 保存消息到数据库
	if h.chatService != nil {
		err := h.chatService.HandleIncomingMessage(ctx, incomingMsg)
		if err != nil {
			log.Printf("保存消息失败: %v", err)
		}
	}
}

// SendMessage 发送消息到QQ
// 思路：通过WebSocket连接发送消息到NapCat，调用QQ发送消息API
func (h *NapCatWebSocketHandler) SendMessage(userID, message string, groupID ...string) error {
	// 构建发送消息的API调用
	apiCall := map[string]interface{}{
		"action": "send_msg",
		"params": map[string]interface{}{
			"message": message,
		},
		"echo": fmt.Sprintf("send_%d", time.Now().UnixNano()),
	}

	// 判断是私聊还是群聊
	if len(groupID) > 0 && groupID[0] != "" {
		// 群消息
		apiCall["params"].(map[string]interface{})["group_id"] = groupID[0]
		apiCall["params"].(map[string]interface{})["message_type"] = "group"
	} else {
		// 私聊消息
		apiCall["params"].(map[string]interface{})["user_id"] = userID
		apiCall["params"].(map[string]interface{})["message_type"] = "private"
	}

	// 序列化为JSON
	data, err := json.Marshal(apiCall)
	if err != nil {
		return fmt.Errorf("序列化API调用失败: %w", err)
	}

	// 发送到所有连接的NapCat实例
	h.connMutex.RLock()
	defer h.connMutex.RUnlock()

	if len(h.connections) == 0 {
		return fmt.Errorf("没有可用的NapCat连接")
	}

	for connID, conn := range h.connections {
		err := conn.WriteMessage(websocket.TextMessage, data)
		if err != nil {
			log.Printf("发送消息到连接 %s 失败: %v", connID, err)
			continue
		}
		log.Printf("消息已发送到NapCat连接 %s: %s", connID, message)
		break // 发送到第一个可用连接即可
	}

	return nil
}

// GetConnectionCount 获取连接数量
// 思路：返回当前活跃的NapCat连接数量
func (h *NapCatWebSocketHandler) GetConnectionCount() int {
	h.connMutex.RLock()
	defer h.connMutex.RUnlock()
	return len(h.connections)
}
