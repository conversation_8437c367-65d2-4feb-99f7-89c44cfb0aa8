package scripting

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"aike_go/internal/interfaces"
	"aike_go/internal/plugins"

	"github.com/sashabaranov/go-openai"
	lua "github.com/yuin/gopher-lua"
)

// ScriptFileCache 脚本文件缓存
// 思路：缓存脚本内容和文件修改时间，避免重复读取
type ScriptFileCache struct {
	Content  string    // 脚本内容
	ModTime  time.Time // 文件修改时间
	FilePath string    // 文件路径
}

// Script 脚本结构体
// 思路：内部脚本表示，避免循环导入
type Script struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Content     string    `json:"content"`
	Type        string    `json:"type"`
	Priority    int       `json:"priority"`
	Enabled     bool      `json:"enabled"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SimpleLuaEngine 简化的Lua脚本执行引擎
// 思路：提供基本的脚本执行功能，避免循环导入
type SimpleLuaEngine struct {
	scripts          map[string]*Script
	scriptsMux       sync.RWMutex
	openaiService    interfaces.OpenAIService
	knowledgeService interfaces.KnowledgeService
	napCatHandler    interface{}                 // 避免循环导入，使用interface{}
	pluginAPI        interface{}                 // 插件API，避免循环导入使用interface{}
	fileCache        map[string]*ScriptFileCache // 文件缓存
	fileCacheMux     sync.RWMutex                // 文件缓存锁
}

// NewSimpleLuaEngine 创建简化的Lua脚本引擎
// 思路：初始化脚本引擎，避免复杂的依赖
func NewSimpleLuaEngine(openaiService interfaces.OpenAIService, knowledgeService interfaces.KnowledgeService) *SimpleLuaEngine {
	return &SimpleLuaEngine{
		scripts:          make(map[string]*Script),
		openaiService:    openaiService,
		knowledgeService: knowledgeService,
		fileCache:        make(map[string]*ScriptFileCache), // 初始化文件缓存
	}
}

// SetNapCatHandler 设置NapCat处理器
// 思路：避免循环导入，通过setter注入依赖
func (e *SimpleLuaEngine) SetNapCatHandler(handler interface{}) {
	e.napCatHandler = handler
}

// SetPluginAPI 设置插件API
// 思路：避免循环导入，通过setter注入插件API
func (e *SimpleLuaEngine) SetPluginAPI(api interface{}) {
	e.pluginAPI = api
	log.Printf("插件API已设置到Lua引擎")
}

// getScriptContent 获取脚本内容
// 思路：优先从文件读取，使用缓存避免重复读取，只在文件修改时才重新读取
func (e *SimpleLuaEngine) getScriptContent(script *Script) (string, error) {
	// 根据脚本名称推断文件路径
	var filePath string

	// 特殊处理已知的插件脚本和处理器脚本
	// 思路：由于数据库中的中文名称可能有编码问题，我们通过脚本内容特征来识别
	// 同时支持从文件读取最新的脚本内容，便于开发和调试
	switch script.Name {
	case "分布式存储管理器":
		filePath = "scripts/plugins/distributed_storage_manager.lua"
	case "聊天记录存储管理器":
		filePath = "scripts/plugins/chat_storage_manager.lua"
	case "插件配置管理器":
		filePath = "scripts/plugins/plugin_config_manager.lua"
	case "AI智能客服":
		filePath = "scripts/handlers/ai_customer_service.lua"
	case "垃圾消息过滤器":
		filePath = "scripts/filters/spam_filter.lua"
	case "工作时间检查":
		filePath = "scripts/rules/work_hours.lua"
	case "自动回复处理器":
		filePath = "scripts/handlers/auto_reply.lua"
	default:
		// 检查是否是乱码的分布式存储管理器脚本
		// 思路：通过脚本内容中的特征字符串来识别
		if strings.Contains(script.Content, "分布式存储管理器") ||
			strings.Contains(script.Content, "distributed_storage") ||
			strings.Contains(script.Name, "洢") { // 乱码特征
			filePath = "scripts/plugins/distributed_storage_manager.lua"
		} else {
			// 对于其他脚本，使用数据库中的内容
			return script.Content, nil
		}
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		log.Printf("文件不存在 %s: %v，使用数据库内容", filePath, err)
		return script.Content, nil
	}

	// 检查缓存
	e.fileCacheMux.RLock()
	cache, exists := e.fileCache[filePath]
	e.fileCacheMux.RUnlock()

	// 如果缓存存在且文件未修改，直接返回缓存内容
	if exists && cache.ModTime.Equal(fileInfo.ModTime()) {
		return cache.Content, nil
	}

	// 文件已修改或首次读取，重新读取文件
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		log.Printf("从文件读取脚本失败 %s: %v，使用数据库内容", filePath, err)
		return script.Content, nil
	}

	// 更新缓存
	e.fileCacheMux.Lock()
	e.fileCache[filePath] = &ScriptFileCache{
		Content:  string(content),
		ModTime:  fileInfo.ModTime(),
		FilePath: filePath,
	}
	e.fileCacheMux.Unlock()

	log.Printf("从文件读取脚本成功: %s (文件修改时间: %s)", filePath, fileInfo.ModTime().Format("2006-01-02 15:04:05"))
	return string(content), nil
}

// 实现interfaces.ScriptEngine接口

// ExecuteScript 执行脚本（接口实现）
// 思路：为适配器提供统一的脚本执行接口
func (e *SimpleLuaEngine) ExecuteScript(ctx context.Context, scriptID string, scriptCtx *interfaces.ScriptContext) (*interfaces.ScriptResult, error) {
	e.scriptsMux.RLock()
	script, exists := e.scripts[scriptID]
	e.scriptsMux.RUnlock()

	if !exists {
		return &interfaces.ScriptResult{
			Success: false,
			Error:   "脚本不存在: " + scriptID,
		}, nil
	}

	// 创建Lua状态
	L := lua.NewState()
	defer L.Close()

	// 注册API函数
	e.registerAPI(L)

	// 设置上下文变量
	e.setContext(L, scriptCtx)

	// 获取脚本内容（优先从文件读取）
	scriptContent, err := e.getScriptContent(script)
	if err != nil {
		return &interfaces.ScriptResult{
			Success: false,
			Error:   fmt.Sprintf("获取脚本内容失败: %v", err),
		}, nil
	}

	// 执行脚本
	start := time.Now()
	err = L.DoString(scriptContent)
	duration := time.Since(start)

	if err != nil {
		return &interfaces.ScriptResult{
			Success:  false,
			Error:    err.Error(),
			Duration: duration,
		}, nil
	}

	return &interfaces.ScriptResult{
		Success:  true,
		Result:   "脚本执行成功",
		Duration: duration,
	}, nil
}

// ExecuteScriptsByType 按类型执行脚本（接口实现）
// 思路：执行指定类型的所有脚本
func (e *SimpleLuaEngine) ExecuteScriptsByType(ctx context.Context, scriptType string, scriptCtx *interfaces.ScriptContext) ([]*interfaces.ScriptResult, error) {
	e.scriptsMux.RLock()
	var scripts []*Script
	for _, script := range e.scripts {
		if script.Type == scriptType && script.Enabled {
			scripts = append(scripts, script)
		}
	}
	e.scriptsMux.RUnlock()

	var results []*interfaces.ScriptResult
	for _, script := range scripts {
		result, err := e.ExecuteScript(ctx, script.ID, scriptCtx)
		if err != nil {
			continue // 跳过错误的脚本
		}
		results = append(results, result)

		// 如果脚本要求停止，则停止执行后续脚本
		if result.ShouldStop {
			break
		}
	}

	return results, nil
}

// LoadScript 加载脚本（接口实现）
// 思路：加载脚本到引擎
func (e *SimpleLuaEngine) LoadScript(script *interfaces.Script) error {
	e.scriptsMux.Lock()
	defer e.scriptsMux.Unlock()

	// 转换为内部类型
	internalScript := &Script{
		ID:          script.ID,
		Name:        script.Name,
		Description: script.Description,
		Content:     script.Content,
		Type:        script.Type,
		Priority:    script.Priority,
		Enabled:     script.Enabled,
		CreatedAt:   script.CreatedAt,
		UpdatedAt:   script.UpdatedAt,
	}

	// 获取脚本内容（优先从文件读取）
	scriptContent, err := e.getScriptContent(internalScript)
	if err != nil {
		return fmt.Errorf("获取脚本内容失败: %w", err)
	}

	// 验证脚本语法
	L := lua.NewState()
	defer L.Close()

	// 注册API函数以便语法验证
	e.registerAPI(L)

	// 设置模拟上下文以便语法验证
	e.setMockContext(L)

	if err := L.DoString(scriptContent); err != nil {
		return fmt.Errorf("脚本语法错误: %w", err)
	}

	e.scripts[script.ID] = internalScript
	log.Printf("加载脚本: %s (%s)", script.Name, script.ID)

	return nil
}

// UnloadScript 卸载脚本（接口实现）
// 思路：从引擎中移除脚本
func (e *SimpleLuaEngine) UnloadScript(scriptID string) error {
	e.scriptsMux.Lock()
	defer e.scriptsMux.Unlock()

	delete(e.scripts, scriptID)
	log.Printf("卸载脚本: %s", scriptID)
	return nil
}

// registerAPI 注册API函数
// 思路：为Lua脚本提供系统API
func (e *SimpleLuaEngine) registerAPI(L *lua.LState) {
	// 日志函数
	L.SetGlobal("log", L.NewFunction(func(L *lua.LState) int {
		level := L.ToString(1)
		message := L.ToString(2)
		log.Printf("[Lua:%s] %s", level, message)
		return 0
	}))

	// 发送消息函数
	L.SetGlobal("send_message", L.NewFunction(func(L *lua.LState) int {
		userID := L.ToString(1)
		message := L.ToString(2)
		groupID := L.ToString(3) // 可选的群ID参数

		log.Printf("[Lua] 发送消息给 %s: %s", userID, message)

		// 如果有NapCat处理器，尝试发送到QQ
		if e.napCatHandler != nil {
			// 类型断言，检查是否有SendMessage方法
			if handler, ok := e.napCatHandler.(interface {
				SendMessage(userID, message string, groupID ...string) error
			}); ok {
				var err error
				if groupID != "" {
					err = handler.SendMessage(userID, message, groupID)
				} else {
					err = handler.SendMessage(userID, message)
				}
				if err != nil {
					log.Printf("[Lua] 发送QQ消息失败: %v", err)
				} else {
					log.Printf("[Lua] QQ消息发送成功")
				}
			}
		}

		return 0
	}))

	// 变量管理
	L.SetGlobal("set_var", L.NewFunction(func(L *lua.LState) int {
		key := L.ToString(1)
		value := L.ToString(2)
		log.Printf("[Lua] 设置变量 %s = %s", key, value)
		return 0
	}))

	L.SetGlobal("get_var", L.NewFunction(func(L *lua.LState) int {
		key := L.ToString(1)
		// log.Printf("[Lua] 获取变量 %s", key)
		L.Push(lua.LString(key)) // TODO 没有实现
		return 1
	}))

	// 时间函数
	L.SetGlobal("now", L.NewFunction(func(L *lua.LState) int {
		L.Push(lua.LNumber(time.Now().Unix()))
		return 1
	}))

	// OpenAI API调用（支持自定义配置）
	// 思路：支持在Lua脚本中指定不同的API配置，实现多AI服务支持
	// 使用例子：call_openai({model="gpt-4", messages=msgs, api_key="sk-xxx", base_url="https://api.openai.com/v1"})
	L.SetGlobal("call_openai", L.NewFunction(func(L *lua.LState) int {
		log.Printf("[Lua] 调用OpenAI API")

		// 获取请求参数
		requestTable := L.ToTable(1)
		if requestTable == nil {
			// 返回错误结果
			result := L.NewTable()
			result.RawSetString("success", lua.LBool(false))
			result.RawSetString("error", lua.LString("请求参数无效"))
			L.Push(result)
			return 1
		}

		// 如果有OpenAI服务，尝试真实调用
		if e.openaiService != nil {
			// 解析参数
			model := ""
			apiKey := ""
			baseURL := ""
			temperature := float32(0.7)
			maxTokens := 500

			// 获取模型
			if modelValue := requestTable.RawGetString("model"); modelValue != lua.LNil {
				model = modelValue.String()
			}

			// 获取自定义API配置
			if apiKeyValue := requestTable.RawGetString("api_key"); apiKeyValue != lua.LNil {
				apiKey = apiKeyValue.String()
			}

			if baseURLValue := requestTable.RawGetString("base_url"); baseURLValue != lua.LNil {
				baseURL = baseURLValue.String()
			}

			// 获取其他参数
			if tempValue := requestTable.RawGetString("temperature"); tempValue != lua.LNil {
				if tempNum, ok := tempValue.(lua.LNumber); ok {
					temperature = float32(tempNum)
				}
			}

			if tokensValue := requestTable.RawGetString("max_tokens"); tokensValue != lua.LNil {
				if tokensNum, ok := tokensValue.(lua.LNumber); ok {
					maxTokens = int(tokensNum)
				}
			}

			// 解析messages参数
			messagesValue := requestTable.RawGetString("messages")
			if messagesValue != lua.LNil {
				if messagesTable, ok := messagesValue.(*lua.LTable); ok {
					// 调用增强的OpenAI服务
					result := e.callOpenAIWithCustomConfig(L, messagesTable, model, apiKey, baseURL, temperature, maxTokens)
					L.Push(result)
					return 1
				}
			}
		}

		// 返回模拟结果
		result := L.NewTable()
		result.RawSetString("success", lua.LBool(true))

		choices := L.NewTable()
		choice := L.NewTable()
		message := L.NewTable()
		message.RawSetString("content", lua.LString("这是AI的智能回复，基于您的问题进行了分析。"))
		choice.RawSetString("message", message)
		choices.Append(choice)
		result.RawSetString("choices", choices)

		L.Push(result)
		return 1
	}))

	// API调用（主要用于知识库搜索）
	L.SetGlobal("call_api", L.NewFunction(func(L *lua.LState) int {
		// method := L.ToString(1)
		url := L.ToString(2)
		// log.Printf("[Lua] API调用: %s %s", method, url)

		result := L.NewTable()
		result.RawSetString("success", lua.LBool(true))

		// 模拟知识库搜索结果
		if url == "/api/v1/knowledge/search" {
			data := L.NewTable()

			// 模拟搜索结果
			item1 := L.NewTable()
			item1.RawSetString("title", lua.LString("常见问题解答"))
			item1.RawSetString("content", lua.LString("这里是常见问题的详细解答内容"))
			item1.RawSetString("score", lua.LNumber(0.85))
			data.Append(item1)

			item2 := L.NewTable()
			item2.RawSetString("title", lua.LString("使用指南"))
			item2.RawSetString("content", lua.LString("这里是使用指南的详细内容"))
			item2.RawSetString("score", lua.LNumber(0.75))
			data.Append(item2)

			result.RawSetString("data", data)
		} else {
			result.RawSetString("data", L.NewTable())
		}

		L.Push(result)
		return 1
	}))

	// 通知管理员
	L.SetGlobal("notify_admin", L.NewFunction(func(L *lua.LState) int {
		message := L.ToString(1)
		log.Printf("[Lua] 管理员通知: %s", message)
		return 0
	}))

	// JSON处理
	L.SetGlobal("json_encode", L.NewFunction(func(L *lua.LState) int {
		L.Push(lua.LString("{}"))
		return 1
	}))

	L.SetGlobal("json_decode", L.NewFunction(func(L *lua.LState) int {
		L.Push(L.NewTable())
		return 1
	}))

	// 注册分布式存储API函数
	L.SetGlobal("store_distributed_message", L.NewFunction(func(L *lua.LState) int {
		// 获取参数
		messageTable := L.CheckTable(1)

		// 构建消息对象
		// 思路：从Lua表中提取所有必要字段，包括用户昵称和头像信息
		message := &plugins.ChatMessage{
			ID:           messageTable.RawGetString("id").String(),
			UserID:       messageTable.RawGetString("user_id").String(),
			Platform:     messageTable.RawGetString("platform").String(),
			Content:      messageTable.RawGetString("content").String(),
			MessageType:  messageTable.RawGetString("message_type").String(),
			UserNickname: messageTable.RawGetString("user_nickname").String(), // 添加昵称字段
			UserAvatar:   messageTable.RawGetString("user_avatar").String(),   // 添加头像字段
			Timestamp:    time.Now(),
		}

		// 获取元数据
		metadataValue := messageTable.RawGetString("metadata")
		if metadataTable, ok := metadataValue.(*lua.LTable); ok && metadataTable != nil {
			metadata := make(map[string]interface{})
			metadataTable.ForEach(func(k, v lua.LValue) {
				if k.Type() == lua.LTString {
					key := string(k.(lua.LString))
					switch v.Type() {
					case lua.LTString:
						metadata[key] = string(v.(lua.LString))
					case lua.LTNumber:
						metadata[key] = float64(v.(lua.LNumber))
					case lua.LTBool:
						metadata[key] = bool(v.(lua.LBool))
					}
				}
			})
			message.Metadata = metadata
		}

		// 调用分布式存储插件
		if e.pluginAPI != nil {
			// 类型断言，检查是否有StoreDistributedMessage方法
			if storageAPI, ok := e.pluginAPI.(interface {
				StoreDistributedMessage(context.Context, *plugins.ChatMessage) error
			}); ok {
				ctx := context.Background()
				err := storageAPI.StoreDistributedMessage(ctx, message)
				if err != nil {
					log.Printf("[Lua] 分布式存储失败: %v", err)
					L.Push(lua.LBool(false))
					L.Push(lua.LString(err.Error()))
				} else {
					// log.Printf("[Lua] 分布式存储成功: %s (用户: %s, 平台: %s)",						message.ID, message.UserID, message.Platform)
					L.Push(lua.LBool(true))
					L.Push(lua.LString(message.ID))
				}
			} else {
				log.Printf("[Lua] 插件API不支持分布式存储")
				L.Push(lua.LBool(false))
				L.Push(lua.LString("插件API不支持分布式存储"))
			}
		} else {
			log.Printf("[Lua] 插件API未设置，无法调用分布式存储")
			L.Push(lua.LBool(false))
			L.Push(lua.LString("插件API未设置"))
		}

		return 2 // 返回两个值
	}))

	// 获取分布式存储统计信息
	L.SetGlobal("get_distributed_storage_statistics", L.NewFunction(func(L *lua.LState) int {
		if e.pluginAPI != nil {
			// 类型断言，检查是否有GetDistributedStorageStats方法
			if storageAPI, ok := e.pluginAPI.(interface {
				GetDistributedStorageStats(context.Context) (plugins.StorageStats, error)
			}); ok {
				ctx := context.Background()
				stats, err := storageAPI.GetDistributedStorageStats(ctx)
				if err != nil {
					log.Printf("[Lua] 获取分布式存储统计失败: %v", err)
					L.Push(lua.LNil)
					return 1
				}

				// 创建统计信息表
				statsTable := L.NewTable()
				statsTable.RawSetString("total_messages", lua.LNumber(stats.TotalMessages))
				statsTable.RawSetString("total_users", lua.LNumber(stats.TotalUsers))
				statsTable.RawSetString("total_sessions", lua.LNumber(stats.TotalSessions))
				statsTable.RawSetString("storage_size", lua.LNumber(stats.StorageSize))

				// 创建最活跃平台列表
				platformsTable := L.NewTable()
				for _, platform := range stats.TopPlatforms {
					platformsTable.Append(lua.LString(platform))
				}
				statsTable.RawSetString("top_platforms", platformsTable)

				L.Push(statsTable)
				return 1
			}
		}

		// 如果无法获取真实统计，返回nil
		L.Push(lua.LNil)
		return 1
	}))

	// 备份分布式存储数据
	L.SetGlobal("backup_distributed_data", L.NewFunction(func(L *lua.LState) int {
		backupPath := L.ToString(1)

		if e.pluginAPI != nil {
			// 类型断言，检查是否有BackupDistributedData方法
			if storageAPI, ok := e.pluginAPI.(interface {
				BackupDistributedData(context.Context, string) error
			}); ok {
				ctx := context.Background()
				err := storageAPI.BackupDistributedData(ctx, backupPath)
				if err != nil {
					log.Printf("[Lua] 分布式存储备份失败: %v", err)
					L.Push(lua.LBool(false))
					L.Push(lua.LString(err.Error()))
				} else {
					log.Printf("[Lua] 分布式存储备份成功: %s", backupPath)
					L.Push(lua.LBool(true))
					L.Push(lua.LString(backupPath))
				}
			} else {
				log.Printf("[Lua] 插件API不支持分布式存储备份")
				L.Push(lua.LBool(false))
				L.Push(lua.LString("插件API不支持分布式存储备份"))
			}
		} else {
			log.Printf("[Lua] 插件API未设置，无法调用分布式存储备份")
			L.Push(lua.LBool(false))
			L.Push(lua.LString("插件API未设置"))
		}

		return 2
	}))

	// 注册插件API函数
	if e.pluginAPI != nil {
		// 类型断言，检查是否有RegisterLuaFunctions方法
		if api, ok := e.pluginAPI.(interface {
			RegisterLuaFunctions(L *lua.LState)
		}); ok {
			api.RegisterLuaFunctions(L)
			// log.Printf("[Lua] 插件API函数已注册")
		} else {
			log.Printf("[Lua] 插件API不支持RegisterLuaFunctions方法")
		}
	} else {
		log.Printf("[Lua] 插件API未设置，跳过插件函数注册")
	}
}

// setContext 设置脚本上下文
// 思路：将上下文数据传递给Lua脚本
func (e *SimpleLuaEngine) setContext(L *lua.LState, scriptCtx *interfaces.ScriptContext) {
	// 设置message对象
	if scriptCtx.Message != nil {
		messageTable := L.NewTable()
		messageTable.RawSetString("content", lua.LString(scriptCtx.Message.Content))
		messageTable.RawSetString("platform", lua.LString(scriptCtx.Message.Platform))

		// 设置from对象
		fromTable := L.NewTable()
		fromTable.RawSetString("platform_user_id", lua.LString(scriptCtx.Message.From.PlatformUserID))
		fromTable.RawSetString("nickname", lua.LString(scriptCtx.Message.From.Nickname))
		messageTable.RawSetString("from", fromTable)

		// 设置metadata对象
		// 思路：将消息的元数据传递给Lua脚本，用于群聊检测等功能
		if len(scriptCtx.Message.Metadata) > 0 {
			metadataTable := L.NewTable()
			for key, value := range scriptCtx.Message.Metadata {
				metadataTable.RawSetString(key, lua.LString(value))
			}
			messageTable.RawSetString("metadata", metadataTable)
		}

		L.SetGlobal("message", messageTable)
	}

	// 设置其他上下文变量
	L.SetGlobal("platform", lua.LString(scriptCtx.Platform))
}

// setMockContext 设置模拟上下文
// 思路：为语法验证提供模拟的上下文对象，避免nil访问错误
// 注意：这里只设置空的上下文对象，不设置具体内容，避免触发业务逻辑
func (e *SimpleLuaEngine) setMockContext(L *lua.LState) {
	// 设置空的message对象，只提供结构不提供内容
	messageTable := L.NewTable()
	messageTable.RawSetString("content", lua.LString(""))
	messageTable.RawSetString("platform", lua.LString(""))

	// 设置空的from对象
	fromTable := L.NewTable()
	fromTable.RawSetString("platform_user_id", lua.LString(""))
	fromTable.RawSetString("nickname", lua.LString(""))
	messageTable.RawSetString("from", fromTable)

	L.SetGlobal("message", messageTable)

	// 设置空的平台变量
	L.SetGlobal("platform", lua.LString(""))

	// 设置语法验证标志，让脚本知道这是语法验证模式
	L.SetGlobal("__syntax_check_mode", lua.LBool(true))
}

// callOpenAIWithCustomConfig 使用自定义配置调用OpenAI API
// 思路：支持在Lua脚本中指定不同的API配置，实现多AI服务支持
// 参数：L - Lua状态，messagesTable - Lua消息表，model - 模型名称，apiKey - API密钥，baseURL - API地址
func (e *SimpleLuaEngine) callOpenAIWithCustomConfig(L *lua.LState, messagesTable *lua.LTable, model, apiKey, baseURL string, temperature float32, maxTokens int) *lua.LTable {
	result := L.NewTable()

	// 如果没有提供自定义配置，使用默认配置
	if apiKey == "" && e.openaiService != nil {
		// 使用默认的OpenAI服务
		// 这里需要转换Lua表格为Go结构
		// 暂时返回模拟结果
		result.RawSetString("success", lua.LBool(true))
		choices := L.NewTable()
		choice := L.NewTable()
		message := L.NewTable()
		message.RawSetString("content", lua.LString("使用默认配置的AI回复"))
		choice.RawSetString("message", message)
		choices.Append(choice)
		result.RawSetString("choices", choices)
		return result
	}

	// 如果提供了自定义配置，创建临时客户端
	if apiKey != "" && baseURL != "" {
		// 创建自定义OpenAI客户端
		config := openai.DefaultConfig(apiKey)
		config.BaseURL = baseURL
		client := openai.NewClientWithConfig(config)

		// 转换Lua消息表为OpenAI消息
		var messages []openai.ChatCompletionMessage
		messagesTable.ForEach(func(key, value lua.LValue) {
			if msgTable, ok := value.(*lua.LTable); ok {
				role := msgTable.RawGetString("role").String()
				content := msgTable.RawGetString("content").String()
				messages = append(messages, openai.ChatCompletionMessage{
					Role:    role,
					Content: content,
				})
			}
		})

		// 设置默认模型
		if model == "" {
			model = "gpt-3.5-turbo"
		}

		// 构建请求
		req := openai.ChatCompletionRequest{
			Model:       model,
			Messages:    messages,
			Temperature: temperature,
			MaxTokens:   maxTokens,
		}

		// 调用API
		ctx := context.Background()
		resp, err := client.CreateChatCompletion(ctx, req)
		if err != nil {
			log.Printf("[Lua] OpenAI API调用失败: %v", err)
			result.RawSetString("success", lua.LBool(false))
			result.RawSetString("error", lua.LString(err.Error()))
			return result
		}

		// 构建成功响应
		result.RawSetString("success", lua.LBool(true))

		choices := L.NewTable()
		if len(resp.Choices) > 0 {
			choice := L.NewTable()
			message := L.NewTable()
			message.RawSetString("content", lua.LString(resp.Choices[0].Message.Content))
			choice.RawSetString("message", message)
			choices.Append(choice)
		}
		result.RawSetString("choices", choices)

		// 添加使用统计
		usage := L.NewTable()
		usage.RawSetString("total_tokens", lua.LNumber(resp.Usage.TotalTokens))
		usage.RawSetString("prompt_tokens", lua.LNumber(resp.Usage.PromptTokens))
		usage.RawSetString("completion_tokens", lua.LNumber(resp.Usage.CompletionTokens))
		result.RawSetString("usage", usage)

		log.Printf("[Lua] 自定义OpenAI API调用成功，模型: %s, tokens: %d", resp.Model, resp.Usage.TotalTokens)
		return result
	}

	// 如果没有有效配置，返回错误
	result.RawSetString("success", lua.LBool(false))
	result.RawSetString("error", lua.LString("缺少有效的API配置"))
	return result
}
