package napcat

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// NapCatAPIClient NapCat API客户端
// 思路：封装NapCat的HTTP API调用，支持发送消息、获取信息等操作
type NapCatAPIClient struct {
	baseURL    string
	httpClient *http.Client
	token      string // 访问令牌
}

// NewNapCatAPIClient 创建NapCat API客户端
// 思路：初始化HTTP客户端，配置基础URL和认证信息
func NewNapCatAPIClient(baseURL, token string) *NapCatAPIClient {
	return &NapCatAPIClient{
		baseURL: baseURL,
		token:   token,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// APIResponse API响应结构
// 思路：NapCat API的标准响应格式
type APIResponse struct {
	Status  string      `json:"status"`  // ok, failed
	RetCode int         `json:"retcode"` // 返回码
	Data    interface{} `json:"data"`    // 响应数据
	Message string      `json:"message"` // 错误信息
	Echo    string      `json:"echo"`    // 回声标识
}

// SendPrivateMessage 发送私聊消息
// 思路：调用send_private_msg API发送私聊消息
func (c *NapCatAPIClient) SendPrivateMessage(userID int64, message string) (*APIResponse, error) {
	params := map[string]interface{}{
		"user_id": userID,
		"message": message,
	}
	return c.callAPI("send_private_msg", params)
}

// SendGroupMessage 发送群消息
// 思路：调用send_group_msg API发送群消息
func (c *NapCatAPIClient) SendGroupMessage(groupID int64, message string) (*APIResponse, error) {
	params := map[string]interface{}{
		"group_id": groupID,
		"message":  message,
	}
	return c.callAPI("send_group_msg", params)
}

// SendMessage 通用发送消息
// 思路：根据消息类型自动选择私聊或群聊API
func (c *NapCatAPIClient) SendMessage(messageType string, targetID int64, message string) (*APIResponse, error) {
	params := map[string]interface{}{
		"message_type": messageType,
		"message":      message,
	}

	if messageType == "private" {
		params["user_id"] = targetID
	} else if messageType == "group" {
		params["group_id"] = targetID
	}

	return c.callAPI("send_msg", params)
}

// GetLoginInfo 获取登录信息
// 思路：获取当前登录的QQ账号信息
func (c *NapCatAPIClient) GetLoginInfo() (*APIResponse, error) {
	return c.callAPI("get_login_info", nil)
}

// GetUserInfo 获取用户信息
// 思路：获取指定用户的详细信息
func (c *NapCatAPIClient) GetUserInfo(userID int64) (*APIResponse, error) {
	params := map[string]interface{}{
		"user_id": userID,
	}
	return c.callAPI("get_stranger_info", params)
}

// GetGroupInfo 获取群信息
// 思路：获取指定群的详细信息
func (c *NapCatAPIClient) GetGroupInfo(groupID int64) (*APIResponse, error) {
	params := map[string]interface{}{
		"group_id": groupID,
	}
	return c.callAPI("get_group_info", params)
}

// GetGroupMemberInfo 获取群成员信息
// 思路：获取群成员的详细信息
func (c *NapCatAPIClient) GetGroupMemberInfo(groupID, userID int64) (*APIResponse, error) {
	params := map[string]interface{}{
		"group_id": groupID,
		"user_id":  userID,
	}
	return c.callAPI("get_group_member_info", params)
}

// GetGroupList 获取群列表
// 思路：获取机器人加入的所有群列表
func (c *NapCatAPIClient) GetGroupList() (*APIResponse, error) {
	return c.callAPI("get_group_list", nil)
}

// GetFriendList 获取好友列表
// 思路：获取机器人的好友列表
func (c *NapCatAPIClient) GetFriendList() (*APIResponse, error) {
	return c.callAPI("get_friend_list", nil)
}

// DeleteMessage 撤回消息
// 思路：撤回指定的消息
func (c *NapCatAPIClient) DeleteMessage(messageID int64) (*APIResponse, error) {
	params := map[string]interface{}{
		"message_id": messageID,
	}
	return c.callAPI("delete_msg", params)
}

// SetGroupKick 踢出群成员
// 思路：将指定用户踢出群聊
func (c *NapCatAPIClient) SetGroupKick(groupID, userID int64, rejectAddRequest bool) (*APIResponse, error) {
	params := map[string]interface{}{
		"group_id":           groupID,
		"user_id":            userID,
		"reject_add_request": rejectAddRequest,
	}
	return c.callAPI("set_group_kick", params)
}

// SetGroupBan 禁言群成员
// 思路：禁言指定群成员
func (c *NapCatAPIClient) SetGroupBan(groupID, userID int64, duration int) (*APIResponse, error) {
	params := map[string]interface{}{
		"group_id": groupID,
		"user_id":  userID,
		"duration": duration, // 禁言时长，单位秒，0表示解除禁言
	}
	return c.callAPI("set_group_ban", params)
}

// GetMessage 获取消息详情
// 思路：根据消息ID获取消息的详细信息
func (c *NapCatAPIClient) GetMessage(messageID int64) (*APIResponse, error) {
	params := map[string]interface{}{
		"message_id": messageID,
	}
	return c.callAPI("get_msg", params)
}

// GetForwardMessage 获取合并转发消息
// 思路：获取合并转发消息的内容
func (c *NapCatAPIClient) GetForwardMessage(messageID string) (*APIResponse, error) {
	params := map[string]interface{}{
		"message_id": messageID,
	}
	return c.callAPI("get_forward_msg", params)
}

// SendLike 发送好友赞
// 思路：给好友点赞
func (c *NapCatAPIClient) SendLike(userID int64, times int) (*APIResponse, error) {
	params := map[string]interface{}{
		"user_id": userID,
		"times":   times, // 赞的次数，每个好友每天最多10次
	}
	return c.callAPI("send_like", params)
}

// callAPI 调用NapCat API
// 思路：封装HTTP请求，处理认证和错误
func (c *NapCatAPIClient) callAPI(action string, params interface{}) (*APIResponse, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/%s", c.baseURL, action)

	// 构建请求体
	var requestBody []byte
	var err error
	
	if params != nil {
		requestBody, err = json.Marshal(params)
		if err != nil {
			return nil, fmt.Errorf("序列化请求参数失败: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if c.token != "" {
		req.Header.Set("Authorization", "Bearer "+c.token)
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return &apiResp, fmt.Errorf("API调用失败，状态码: %d, 消息: %s", resp.StatusCode, apiResp.Message)
	}

	// 检查API返回码
	if apiResp.Status == "failed" {
		return &apiResp, fmt.Errorf("API调用失败，返回码: %d, 消息: %s", apiResp.RetCode, apiResp.Message)
	}

	return &apiResp, nil
}
