# AI客服系统开发指南

## 概述

本指南将帮助开发者了解AI客服系统的架构、开发环境搭建、代码规范和扩展方法。

## 项目架构

### 目录结构
```
aike_go/
├── cmd/                    # 应用程序入口
│   ├── server/            # 服务器主程序
│   └── test_qq/           # QQ测试工具
├── internal/              # 内部包（不对外暴露）
│   ├── adapters/          # 平台适配器
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接和迁移
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   ├── server/            # HTTP服务器
│   └── services/          # 业务逻辑服务
├── web/                   # 静态文件
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── tests/                 # 测试文件
├── configs/               # 配置文件示例
├── go.mod                 # Go模块定义
├── go.sum                 # 依赖版本锁定
└── README.md              # 项目说明
```

### 架构设计

#### 分层架构
```
┌─────────────────────────────────────┐
│           HTTP/WebSocket            │  ← 接口层
├─────────────────────────────────────┤
│            Middleware               │  ← 中间件层
├─────────────────────────────────────┤
│            Controllers              │  ← 控制器层
├─────────────────────────────────────┤
│             Services                │  ← 业务逻辑层
├─────────────────────────────────────┤
│             Models                  │  ← 数据模型层
├─────────────────────────────────────┤
│            Database                 │  ← 数据访问层
└─────────────────────────────────────┘
```

#### 核心组件

1. **适配器模式** - 统一不同平台的消息处理接口
2. **服务层** - 封装业务逻辑，提供可复用的服务
3. **中间件** - 处理横切关注点（日志、认证、限流等）
4. **配置管理** - 统一的配置加载和管理
5. **数据库抽象** - 支持多种数据库的统一接口

## 开发环境搭建

### 1. 安装依赖

#### Go环境
```bash
# 安装Go 1.19+
wget https://go.dev/dl/go1.19.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.19.linux-amd64.tar.gz

# 设置环境变量
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export GO111MODULE=on
```

#### 开发工具
```bash
# 安装常用工具
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/swaggo/swag/cmd/swag@latest
go install github.com/air-verse/air@latest  # 热重载
```

### 2. 克隆和初始化项目
```bash
# 克隆项目
git clone https://github.com/example/aike_go.git
cd aike_go

# 安装依赖
go mod download

# 复制配置文件
cp .env.example .env

# 编辑配置
vim .env
```

### 3. 启动开发环境
```bash
# 使用air进行热重载开发
air

# 或直接运行
go run cmd/server/main.go
```

## 代码规范

### 1. 命名规范

#### 包命名
- 使用小写字母
- 简短且有意义
- 避免下划线和驼峰

```go
// 好的例子
package models
package services
package adapters

// 不好的例子
package Models
package user_service
package httpUtils
```

#### 变量和函数命名
- 使用驼峰命名法
- 导出的标识符首字母大写
- 私有标识符首字母小写

```go
// 导出的函数
func CreateUser(user *User) error

// 私有函数
func validateUserInput(input string) bool

// 常量
const MaxRetryCount = 3

// 变量
var defaultTimeout = 30 * time.Second
```

### 2. 注释规范

#### 包注释
```go
// Package services 提供业务逻辑服务
// 
// 本包包含了聊天服务、知识库服务等核心业务逻辑，
// 为上层控制器提供可复用的服务接口。
package services
```

#### 函数注释
```go
// ProcessMessage 处理用户消息并生成回复
// 思路：首先搜索知识库，如果没有匹配则调用OpenAI API
// 使用例子：response, err := chatService.ProcessMessage(ctx, message)
//
// 参数：
//   ctx - 上下文，用于控制超时和取消
//   message - 用户输入的消息
//
// 返回值：
//   *models.Message - 生成的回复消息
//   error - 处理过程中的错误
func (s *ChatService) ProcessMessage(ctx context.Context, message *models.Message) (*models.Message, error) {
    // 实现...
}
```

#### 结构体注释
```go
// User 用户模型
// 思路：统一管理不同平台的用户信息
type User struct {
    ID             uint      `json:"id" gorm:"primaryKey"`                    // 用户ID
    Platform       string    `json:"platform" gorm:"size:50;not null"`       // 平台名称（qq、微信等）
    PlatformID     string    `json:"platform_id" gorm:"size:100;not null"`   // 平台内部ID
    PlatformUserID string    `json:"platform_user_id" gorm:"size:100;not null"` // 平台用户ID
    Nickname       string    `json:"nickname" gorm:"size:100"`                // 用户昵称
    Avatar         string    `json:"avatar" gorm:"size:500"`                  // 头像URL
    IsBot          bool      `json:"is_bot" gorm:"default:false"`             // 是否为机器人
    CreatedAt      time.Time `json:"created_at"`                              // 创建时间
    UpdatedAt      time.Time `json:"updated_at"`                              // 更新时间
}
```

### 3. 错误处理

#### 错误定义
```go
// 使用自定义错误类型
var (
    ErrUserNotFound     = errors.New("用户不存在")
    ErrInvalidMessage   = errors.New("无效的消息格式")
    ErrDatabaseError    = errors.New("数据库操作失败")
)

// 或使用错误包装
func (s *ChatService) ProcessMessage(ctx context.Context, message *models.Message) (*models.Message, error) {
    if message == nil {
        return nil, fmt.Errorf("ProcessMessage: %w", ErrInvalidMessage)
    }
    
    // 处理逻辑...
    
    if err := s.saveMessage(ctx, message); err != nil {
        return nil, fmt.Errorf("ProcessMessage: 保存消息失败: %w", err)
    }
    
    return response, nil
}
```

#### 错误处理模式
```go
// 使用中间件统一处理错误
func (s *Server) sendMessage(c *gin.Context) {
    var req SendMessageRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        middleware.AbortWithError(c, middleware.ErrorTypeValidation, 
            "INVALID_REQUEST", "请求格式错误", err.Error())
        return
    }
    
    response, err := s.chatService.ProcessMessage(c.Request.Context(), &message)
    if err != nil {
        middleware.AbortWithError(c, middleware.ErrorTypeInternal,
            "PROCESS_FAILED", "消息处理失败", err.Error())
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "data":    response,
    })
}
```

### 4. 测试规范

#### 单元测试
```go
func TestChatService_ProcessMessage(t *testing.T) {
    // 使用testify进行断言
    assert := assert.New(t)
    
    // 设置测试环境
    chatService := setupTestChatService(t)
    
    // 测试用例
    tests := []struct {
        name    string
        message *models.Message
        want    string
        wantErr bool
    }{
        {
            name: "正常消息处理",
            message: &models.Message{
                Content: "工作时间",
                Type:    "text",
            },
            want:    "我们的工作时间是周一至周五 9:00-18:00",
            wantErr: false,
        },
        {
            name:    "空消息",
            message: nil,
            want:    "",
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            response, err := chatService.ProcessMessage(context.Background(), tt.message)
            
            if tt.wantErr {
                assert.Error(err)
                return
            }
            
            assert.NoError(err)
            assert.Contains(response.Content, tt.want)
        })
    }
}
```

#### 集成测试
```go
func TestIntegration_MessageFlow(t *testing.T) {
    // 设置测试服务器
    testServer := setupTestServer(t)
    defer testServer.Close()
    
    // 测试完整的消息流程
    requestData := map[string]interface{}{
        "user_id":  "test_user",
        "platform": "test",
        "content":  "测试消息",
        "type":     "text",
    }
    
    // 发送请求
    response := sendTestRequest(t, testServer, "POST", "/api/v1/messages/send", requestData)
    
    // 验证响应
    assert.Equal(t, http.StatusOK, response.StatusCode)
    
    var responseData map[string]interface{}
    err := json.NewDecoder(response.Body).Decode(&responseData)
    assert.NoError(t, err)
    assert.True(t, responseData["success"].(bool))
}
```

## 扩展开发

### 1. 添加新的平台适配器

#### 步骤1：实现适配器接口
```go
// internal/adapters/wechat.go
package adapters

import (
    "context"
    "fmt"
)

// WeChatAdapter 微信平台适配器
type WeChatAdapter struct {
    config PlatformConfig
    client *http.Client
}

// NewWeChatAdapter 创建微信适配器
func NewWeChatAdapter() PlatformAdapter {
    return &WeChatAdapter{
        client: &http.Client{Timeout: 30 * time.Second},
    }
}

// GetPlatformName 返回平台名称
func (w *WeChatAdapter) GetPlatformName() string {
    return "wechat"
}

// Initialize 初始化适配器
func (w *WeChatAdapter) Initialize(config PlatformConfig) error {
    w.config = config
    // 初始化微信API客户端
    return nil
}

// Start 启动适配器
func (w *WeChatAdapter) Start(ctx context.Context) error {
    // 启动微信消息监听
    return nil
}

// SendMessage 发送消息
func (w *WeChatAdapter) SendMessage(ctx context.Context, message *OutgoingMessage) error {
    // 调用微信API发送消息
    return nil
}

// GetUserInfo 获取用户信息
func (w *WeChatAdapter) GetUserInfo(ctx context.Context, platformUserID string) (*UserInfo, error) {
    // 调用微信API获取用户信息
    return nil, nil
}

// Stop 停止适配器
func (w *WeChatAdapter) Stop(ctx context.Context) error {
    // 清理资源
    return nil
}

// IsHealthy 健康检查
func (w *WeChatAdapter) IsHealthy(ctx context.Context) bool {
    // 检查微信API连接状态
    return true
}
```

#### 步骤2：注册适配器
```go
// internal/adapters/manager.go
func (m *AdapterManager) registerAdapters() {
    // 注册现有适配器
    m.RegisterAdapter(NewQQAdapter())
    
    // 注册新的微信适配器
    m.RegisterAdapter(NewWeChatAdapter())
}
```

#### 步骤3：添加配置
```go
// internal/config/config.go
type Config struct {
    // 现有配置...
    
    // 微信配置
    WeChat WeChatConfig `mapstructure:"wechat"`
}

type WeChatConfig struct {
    Enabled   bool   `mapstructure:"enabled"`
    AppID     string `mapstructure:"app_id"`
    AppSecret string `mapstructure:"app_secret"`
    Token     string `mapstructure:"token"`
}
```

### 2. 添加新的中间件

```go
// internal/middleware/cors.go
package middleware

import (
    "github.com/gin-gonic/gin"
)

// CORSMiddleware CORS中间件
// 思路：处理跨域请求，支持预检请求
func CORSMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        origin := c.Request.Header.Get("Origin")
        
        // 设置CORS头
        c.Header("Access-Control-Allow-Origin", origin)
        c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-API-Key")
        c.Header("Access-Control-Allow-Credentials", "true")
        
        // 处理预检请求
        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }
        
        c.Next()
    }
}
```

### 3. 添加新的服务

```go
// internal/services/notification.go
package services

import (
    "context"
    "aike_go/internal/models"
)

// NotificationService 通知服务
// 思路：统一管理各种通知渠道（邮件、短信、推送等）
type NotificationService struct {
    emailService *EmailService
    smsService   *SMSService
}

// NewNotificationService 创建通知服务
func NewNotificationService() *NotificationService {
    return &NotificationService{
        emailService: NewEmailService(),
        smsService:   NewSMSService(),
    }
}

// SendNotification 发送通知
func (s *NotificationService) SendNotification(ctx context.Context, notification *models.Notification) error {
    switch notification.Type {
    case "email":
        return s.emailService.Send(ctx, notification)
    case "sms":
        return s.smsService.Send(ctx, notification)
    default:
        return fmt.Errorf("不支持的通知类型: %s", notification.Type)
    }
}
```

## 性能优化

### 1. 数据库优化

#### 索引优化
```go
// 在模型中添加索引
type Message struct {
    // 字段定义...
    
    // 添加复合索引
    UserID    uint   `gorm:"index:idx_user_session"`
    SessionID string `gorm:"index:idx_user_session"`
    CreatedAt time.Time `gorm:"index:idx_created_at"`
}

// 在迁移中创建自定义索引
func createCustomIndexes(db *gorm.DB) error {
    // 创建复合索引
    if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_messages_user_created ON messages(user_id, created_at)").Error; err != nil {
        return err
    }
    
    return nil
}
```

#### 查询优化
```go
// 使用预加载减少N+1查询
func (s *ChatService) GetMessageHistory(ctx context.Context, userID uint, limit, offset int) ([]*models.Message, error) {
    var messages []*models.Message
    
    err := s.db.WithContext(ctx).
        Preload("User").                    // 预加载用户信息
        Where("user_id = ?", userID).
        Order("created_at DESC").
        Limit(limit).
        Offset(offset).
        Find(&messages).Error
    
    return messages, err
}

// 使用原生SQL进行复杂查询
func (s *KnowledgeService) GetPopularKnowledge(ctx context.Context, limit int) ([]*models.Knowledge, error) {
    var knowledge []*models.Knowledge
    
    err := s.db.WithContext(ctx).
        Raw(`
            SELECT * FROM knowledge 
            WHERE status = 'active' 
            ORDER BY usage_count DESC, updated_at DESC 
            LIMIT ?
        `, limit).
        Scan(&knowledge).Error
    
    return knowledge, err
}
```

### 2. 缓存策略

```go
// internal/services/cache.go
package services

import (
    "context"
    "encoding/json"
    "time"
    
    "github.com/go-redis/redis/v8"
)

// CacheService 缓存服务
type CacheService struct {
    client *redis.Client
}

// NewCacheService 创建缓存服务
func NewCacheService(redisURL string) *CacheService {
    opt, _ := redis.ParseURL(redisURL)
    client := redis.NewClient(opt)
    
    return &CacheService{client: client}
}

// Set 设置缓存
func (s *CacheService) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
    data, err := json.Marshal(value)
    if err != nil {
        return err
    }
    
    return s.client.Set(ctx, key, data, expiration).Err()
}

// Get 获取缓存
func (s *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
    data, err := s.client.Get(ctx, key).Result()
    if err != nil {
        return err
    }
    
    return json.Unmarshal([]byte(data), dest)
}

// 在知识库服务中使用缓存
func (s *KnowledgeService) SearchKnowledge(ctx context.Context, query string, limit int) ([]*models.Knowledge, error) {
    // 尝试从缓存获取
    cacheKey := fmt.Sprintf("knowledge:search:%s:%d", query, limit)
    var cached []*models.Knowledge
    
    if err := s.cache.Get(ctx, cacheKey, &cached); err == nil {
        return cached, nil
    }
    
    // 从数据库查询
    results, err := s.searchFromDatabase(ctx, query, limit)
    if err != nil {
        return nil, err
    }
    
    // 缓存结果
    s.cache.Set(ctx, cacheKey, results, 5*time.Minute)
    
    return results, nil
}
```

## 调试和分析

### 1. 日志分析
```bash
# 查看错误日志
grep "ERROR" logs/app-*.log

# 分析响应时间
grep "duration" logs/app-*.log | awk '{print $8}' | sort -n

# 统计API调用次数
grep "GET\|POST\|PUT\|DELETE" logs/app-*.log | awk '{print $9}' | sort | uniq -c
```

### 2. 性能分析
```go
// 使用pprof进行性能分析
import _ "net/http/pprof"

func main() {
    // 启动pprof服务器
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 启动主服务器
    server.Start()
}
```

访问性能分析：
```bash
# CPU分析
go tool pprof http://localhost:6060/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:6060/debug/pprof/heap

# goroutine分析
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

## 贡献指南

### 1. 提交代码
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: 添加新功能"

# 推送分支
git push origin feature/new-feature

# 创建Pull Request
```

### 2. 代码审查清单
- [ ] 代码符合项目规范
- [ ] 添加了适当的注释
- [ ] 包含单元测试
- [ ] 通过所有测试
- [ ] 更新了相关文档
- [ ] 没有引入安全漏洞

### 3. 发布流程
```bash
# 更新版本号
git tag v1.1.0

# 推送标签
git push origin v1.1.0

# 构建发布包
make build-release

# 发布到GitHub Releases
```

## 相关资源

- [Go官方文档](https://golang.org/doc/)
- [Gin框架文档](https://gin-gonic.com/docs/)
- [GORM文档](https://gorm.io/docs/)
- [项目Wiki](https://github.com/example/aike_go/wiki)
- [API文档](api_documentation.md)
- [部署指南](deployment_guide.md)
