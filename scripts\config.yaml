scripts:
  # 消息过滤器 - 优先级最高
  - name: "垃圾消息过滤器"
    description: "过滤包含广告、垃圾信息的消息"
    type: "message_filter"
    priority: 100
    enabled: true
    file: "filters/spam_filter.lua"

  # 业务规则 - 中等优先级
  - name: "工作时间检查"
    description: "检查是否在工作时间内，非工作时间自动回复"
    type: "business_rule"
    priority: 150
    enabled: true
    file: "rules/work_hours.lua"

  # 消息处理器 - 优先级较低，让AI客服最后处理
  - name: "AI智能客服"
    description: "集成OpenAI、知识库和平台接口的完整客服解决方案"
    type: "message_handler"
    priority: 300
    enabled: false  # 禁用原版，使用多AI版本
    file: "handlers/ai_customer_service.lua"

  - name: "多AI智能客服"
    description: "支持多AI配置的智能客服系统，可为不同平台配置不同AI服务"
    type: "message_handler"
    priority: 290
    enabled: true
    file: "handlers/multi_ai_customer_service.lua"

  - name: "自动回复处理器"
    description: "根据关键词自动回复消息（备用）"
    type: "message_handler"
    priority: 400
    enabled: false  # 禁用，让AI客服处理
    file: "handlers/auto_reply.lua"
