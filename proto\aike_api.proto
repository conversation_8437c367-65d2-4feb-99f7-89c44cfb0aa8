syntax = "proto3";

package aike.v1;

option go_package = "aike_go/proto/aike/v1;aikev1";

import "aike_service.proto";
import "google/protobuf/empty.proto";

// ============================================================================
// AI客服系统 gRPC 服务定义
// ============================================================================

// 消息处理服务
// 思路：处理用户消息，生成AI回复
service MessageService {
  // 处理消息
  rpc ProcessMessage(ProcessMessageRequest) returns (ProcessMessageResponse);
  
  // 发送消息
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  
  // 获取消息历史
  rpc GetMessageHistory(GetMessageHistoryRequest) returns (GetMessageHistoryResponse);
  
  // 获取会话列表
  rpc GetSessions(GetSessionsRequest) returns (GetSessionsResponse);
  
  // 创建会话
  rpc CreateSession(CreateSessionRequest) returns (Session);
  
  // 关闭会话
  rpc CloseSession(CloseSessionRequest) returns (google.protobuf.Empty);
}

// 用户管理服务
// 思路：管理用户信息和状态
service UserService {
  // 创建用户
  rpc CreateUser(CreateUserRequest) returns (User);
  
  // 获取用户信息
  rpc GetUser(GetUserRequest) returns (User);
  
  // 更新用户信息
  rpc UpdateUser(UpdateUserRequest) returns (User);
  
  // 删除用户
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty);
  
  // 获取用户列表
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse);
  
  // 更新用户活跃时间
  rpc UpdateUserActivity(UpdateUserActivityRequest) returns (google.protobuf.Empty);
}

// 知识库管理服务
// 思路：管理知识库内容和搜索
service KnowledgeService {
  // 创建知识库条目
  rpc CreateKnowledge(CreateKnowledgeRequest) returns (Knowledge);
  
  // 获取知识库条目
  rpc GetKnowledge(GetKnowledgeRequest) returns (Knowledge);
  
  // 更新知识库条目
  rpc UpdateKnowledge(UpdateKnowledgeRequest) returns (Knowledge);
  
  // 删除知识库条目
  rpc DeleteKnowledge(DeleteKnowledgeRequest) returns (google.protobuf.Empty);
  
  // 搜索知识库
  rpc SearchKnowledge(SearchKnowledgeRequest) returns (SearchKnowledgeResponse);
  
  // 获取知识库列表
  rpc ListKnowledge(ListKnowledgeRequest) returns (ListKnowledgeResponse);
}

// 脚本管理服务
// 思路：管理Lua脚本的配置和执行
service ScriptService {
  // 创建脚本
  rpc CreateScript(CreateScriptRequest) returns (Script);
  
  // 获取脚本
  rpc GetScript(GetScriptRequest) returns (Script);
  
  // 更新脚本
  rpc UpdateScript(UpdateScriptRequest) returns (Script);
  
  // 删除脚本
  rpc DeleteScript(DeleteScriptRequest) returns (google.protobuf.Empty);
  
  // 获取脚本列表
  rpc ListScripts(ListScriptsRequest) returns (ListScriptsResponse);
  
  // 执行脚本
  rpc ExecuteScript(ExecuteScriptRequest) returns (ExecuteScriptResponse);
  
  // 启用/禁用脚本
  rpc ToggleScript(ToggleScriptRequest) returns (google.protobuf.Empty);
}

// 平台集成服务
// 思路：管理不同平台的集成和消息转发
service PlatformService {
  // 注册平台
  rpc RegisterPlatform(RegisterPlatformRequest) returns (google.protobuf.Empty);
  
  // 获取平台状态
  rpc GetPlatformStatus(GetPlatformStatusRequest) returns (PlatformStatusResponse);
  
  // 获取平台列表
  rpc ListPlatforms(google.protobuf.Empty) returns (ListPlatformsResponse);
  
  // 发送平台消息
  rpc SendPlatformMessage(SendPlatformMessageRequest) returns (SendPlatformMessageResponse);
  
  // 处理平台WebHook
  rpc HandleWebhook(WebhookRequest) returns (WebhookResponse);
}

// 统计监控服务
// 思路：提供系统统计和监控数据
service StatsService {
  // 获取系统统计
  rpc GetSystemStats(google.protobuf.Empty) returns (SystemStats);
  
  // 获取平台统计
  rpc GetPlatformStats(GetPlatformStatsRequest) returns (PlatformStats);
  
  // 获取用户统计
  rpc GetUserStats(GetUserStatsRequest) returns (UserStatsResponse);
  
  // 获取消息统计
  rpc GetMessageStats(GetMessageStatsRequest) returns (MessageStatsResponse);
}

// ============================================================================
// 请求和响应消息定义
// ============================================================================

// 获取消息历史请求
message GetMessageHistoryRequest {
  string session_id = 1;                            // 会话ID
  PaginationRequest pagination = 2;                 // 分页参数
  string platform = 3;                              // 平台过滤
  string user_id = 4;                                // 用户ID过滤
}

// 获取消息历史响应
message GetMessageHistoryResponse {
  repeated Message messages = 1;                    // 消息列表
  PaginationResponse pagination = 2;                // 分页信息
}

// 获取会话列表请求
message GetSessionsRequest {
  string user_id = 1;                                // 用户ID
  string platform = 2;                              // 平台过滤
  SessionStatus status = 3;                         // 状态过滤
  PaginationRequest pagination = 4;                 // 分页参数
}

// 获取会话列表响应
message GetSessionsResponse {
  repeated Session sessions = 1;                    // 会话列表
  PaginationResponse pagination = 2;                // 分页信息
}

// 创建会话请求
message CreateSessionRequest {
  uint32 user_id = 1;                               // 用户ID
  string platform = 2;                              // 平台类型
  string title = 3;                                 // 会话标题
  string system_prompt = 4;                         // 系统提示词
}

// 关闭会话请求
message CloseSessionRequest {
  string session_id = 1;                            // 会话ID
}

// 创建用户请求
message CreateUserRequest {
  string platform_id = 1;                           // 平台用户ID
  string platform = 2;                              // 平台类型
  string nickname = 3;                               // 用户昵称
  string avatar = 4;                                 // 头像URL
  string email = 5;                                  // 邮箱
  string phone = 6;                                  // 手机号
}

// 获取用户请求
message GetUserRequest {
  oneof identifier {
    uint32 id = 1;                                   // 用户ID
    string platform_key = 2;                        // 平台键（platform_platformid）
  }
}

// 更新用户请求
message UpdateUserRequest {
  uint32 id = 1;                                     // 用户ID
  string nickname = 2;                               // 用户昵称
  string avatar = 3;                                 // 头像URL
  string email = 4;                                  // 邮箱
  string phone = 5;                                  // 手机号
  UserStatus status = 6;                            // 用户状态
  bool is_vip = 7;                                   // 是否VIP用户
}

// 删除用户请求
message DeleteUserRequest {
  uint32 id = 1;                                     // 用户ID
}

// 获取用户列表请求
message ListUsersRequest {
  string platform = 1;                              // 平台过滤
  UserStatus status = 2;                            // 状态过滤
  bool is_vip = 3;                                   // VIP过滤
  PaginationRequest pagination = 4;                 // 分页参数
}

// 获取用户列表响应
message ListUsersResponse {
  repeated User users = 1;                          // 用户列表
  PaginationResponse pagination = 2;                // 分页信息
}

// 更新用户活跃时间请求
message UpdateUserActivityRequest {
  uint32 user_id = 1;                               // 用户ID
}

// 创建知识库请求
message CreateKnowledgeRequest {
  string title = 1;                                 // 标题
  string content = 2;                               // 内容
  string summary = 3;                               // 摘要
  string category = 4;                              // 分类
  string keywords = 5;                              // 关键词
  string tags = 6;                                  // 标签
  int32 priority = 7;                               // 优先级
  bool is_public = 8;                               // 是否公开
}

// 获取知识库请求
message GetKnowledgeRequest {
  uint32 id = 1;                                    // 知识库ID
}

// 更新知识库请求
message UpdateKnowledgeRequest {
  uint32 id = 1;                                    // 知识库ID
  string title = 2;                                 // 标题
  string content = 3;                               // 内容
  string summary = 4;                               // 摘要
  string category = 5;                              // 分类
  string keywords = 6;                              // 关键词
  string tags = 7;                                  // 标签
  int32 priority = 8;                               // 优先级
  bool is_public = 9;                               // 是否公开
  KnowledgeStatus status = 10;                      // 状态
}

// 删除知识库请求
message DeleteKnowledgeRequest {
  uint32 id = 1;                                    // 知识库ID
}

// 搜索知识库请求
message SearchKnowledgeRequest {
  string query = 1;                                 // 搜索查询
  string category = 2;                              // 分类过滤
  repeated string tags = 3;                         // 标签过滤
  KnowledgeStatus status = 4;                       // 状态过滤
  int32 limit = 5;                                  // 结果限制
}

// 搜索知识库响应
message SearchKnowledgeResponse {
  repeated KnowledgeSearchResult results = 1;       // 搜索结果
  int32 total_count = 2;                            // 总结果数
}

// 知识库搜索结果
message KnowledgeSearchResult {
  Knowledge knowledge = 1;                          // 知识库条目
  float score = 2;                                  // 相关性分数
  string highlight = 3;                             // 高亮片段
}

// 获取知识库列表请求
message ListKnowledgeRequest {
  string category = 1;                              // 分类过滤
  KnowledgeStatus status = 2;                       // 状态过滤
  PaginationRequest pagination = 3;                 // 分页参数
}

// 获取知识库列表响应
message ListKnowledgeResponse {
  repeated Knowledge knowledge_list = 1;            // 知识库列表
  PaginationResponse pagination = 2;                // 分页信息
}

// 创建脚本请求
message CreateScriptRequest {
  string name = 1;                                  // 脚本名称
  string description = 2;                           // 脚本描述
  string content = 3;                               // 脚本内容
  ScriptType type = 4;                              // 脚本类型
  int32 priority = 5;                               // 优先级
  string config = 6;                                // 配置参数
}

// 获取脚本请求
message GetScriptRequest {
  uint32 id = 1;                                    // 脚本ID
}

// 更新脚本请求
message UpdateScriptRequest {
  uint32 id = 1;                                    // 脚本ID
  string name = 2;                                  // 脚本名称
  string description = 3;                           // 脚本描述
  string content = 4;                               // 脚本内容
  ScriptType type = 5;                              // 脚本类型
  bool enabled = 6;                                 // 是否启用
  int32 priority = 7;                               // 优先级
  string config = 8;                                // 配置参数
}

// 删除脚本请求
message DeleteScriptRequest {
  uint32 id = 1;                                    // 脚本ID
}

// 获取脚本列表请求
message ListScriptsRequest {
  ScriptType type = 1;                              // 类型过滤
  bool enabled = 2;                                 // 启用状态过滤
  PaginationRequest pagination = 3;                 // 分页参数
}

// 获取脚本列表响应
message ListScriptsResponse {
  repeated Script scripts = 1;                      // 脚本列表
  PaginationResponse pagination = 2;                // 分页信息
}

// 执行脚本请求
message ExecuteScriptRequest {
  uint32 script_id = 1;                             // 脚本ID
  string input_data = 2;                            // 输入数据（JSON格式）
  map<string, string> context = 3;                  // 执行上下文
}

// 执行脚本响应
message ExecuteScriptResponse {
  bool success = 1;                                 // 是否成功
  string result = 2;                                // 执行结果
  string error_message = 3;                         // 错误信息
  int64 execution_time_ms = 4;                      // 执行时间（毫秒）
}

// 切换脚本状态请求
message ToggleScriptRequest {
  uint32 script_id = 1;                             // 脚本ID
  bool enabled = 2;                                 // 启用状态
}

// 注册平台请求
message RegisterPlatformRequest {
  string platform = 1;                              // 平台名称
  string endpoint = 2;                              // 平台端点
  string token = 3;                                 // 平台令牌
  map<string, string> config = 4;                   // 平台配置
}

// 获取平台状态请求
message GetPlatformStatusRequest {
  string platform = 1;                              // 平台名称
}

// 平台状态响应
message PlatformStatusResponse {
  string platform = 1;                              // 平台名称
  bool is_connected = 2;                            // 是否连接
  string status = 3;                                // 状态描述
  google.protobuf.Timestamp last_heartbeat = 4;    // 最后心跳时间
  map<string, string> metadata = 5;                 // 元数据
}

// 获取平台列表响应
message ListPlatformsResponse {
  repeated PlatformStatusResponse platforms = 1;    // 平台列表
}

// 发送平台消息请求
message SendPlatformMessageRequest {
  string platform = 1;                              // 平台名称
  OutgoingMessage message = 2;                      // 消息内容
}

// 发送平台消息响应
message SendPlatformMessageResponse {
  bool success = 1;                                 // 是否成功
  string message_id = 2;                            // 消息ID
  string error_message = 3;                         // 错误信息
}

// WebHook请求
message WebhookRequest {
  string platform = 1;                              // 平台名称
  string event_type = 2;                            // 事件类型
  string payload = 3;                               // 载荷数据（JSON格式）
  map<string, string> headers = 4;                  // 请求头
}

// WebHook响应
message WebhookResponse {
  bool success = 1;                                 // 是否成功
  string message = 2;                               // 响应消息
  map<string, string> metadata = 3;                 // 元数据
}

// 获取平台统计请求
message GetPlatformStatsRequest {
  string platform = 1;                              // 平台名称
}

// 获取用户统计请求
message GetUserStatsRequest {
  string platform = 1;                              // 平台过滤
  string time_range = 2;                            // 时间范围：day, week, month, year
}

// 用户统计响应
message UserStatsResponse {
  int32 total_users = 1;                            // 总用户数
  int32 active_users = 2;                           // 活跃用户数
  int32 new_users = 3;                              // 新用户数
  int32 vip_users = 4;                              // VIP用户数
  map<string, int32> platform_distribution = 5;    // 平台分布
}

// 获取消息统计请求
message GetMessageStatsRequest {
  string platform = 1;                              // 平台过滤
  string time_range = 2;                            // 时间范围
}

// 消息统计响应
message MessageStatsResponse {
  int32 total_messages = 1;                         // 总消息数
  int32 incoming_messages = 2;                      // 接收消息数
  int32 outgoing_messages = 3;                      // 发送消息数
  int32 ai_generated_messages = 4;                  // AI生成消息数
  int32 total_tokens_used = 5;                      // 总token使用量
  map<string, int32> message_type_distribution = 6; // 消息类型分布
}
