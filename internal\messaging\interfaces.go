package messaging

import (
	"context"
	"time"
)

// IncomingMessage 接收到的消息
// 思路：统一的消息格式，适用于所有平台
type IncomingMessage struct {
	ID          string                 `json:"id"`
	Platform    string                 `json:"platform"`
	Content     string                 `json:"content"`
	MessageType string                 `json:"message_type"` // text, image, voice, file, etc.
	Timestamp   time.Time              `json:"timestamp"`
	From        *UserInfo              `json:"from"`
	Metadata    map[string]string      `json:"metadata"`
}

// OutgoingMessage 发送的消息
// 思路：统一的发送消息格式
type OutgoingMessage struct {
	PlatformUserID string            `json:"platform_user_id"`
	PlatformChatID string            `json:"platform_chat_id,omitempty"`
	Content        string            `json:"content"`
	MessageType    string            `json:"message_type"`
	Metadata       map[string]string `json:"metadata,omitempty"`
}

// UserInfo 用户信息
// 思路：统一的用户信息格式
type UserInfo struct {
	PlatformUserID string `json:"platform_user_id"`
	Platform       string `json:"platform"`
	Nickname       string `json:"nickname"`
	Avatar         string `json:"avatar"`
	IsBot          bool   `json:"is_bot"`
}

// MessageHandler 消息处理器接口
// 思路：定义消息处理的统一接口，避免循环依赖
type MessageHandler interface {
	// HandleIncomingMessage 处理接收到的消息
	// 思路：处理来自各个平台的消息
	HandleIncomingMessage(ctx context.Context, message *IncomingMessage) error
}

// MessageSender 消息发送器接口
// 思路：定义消息发送的统一接口
type MessageSender interface {
	// SendMessage 发送消息
	// 思路：向指定平台发送消息
	SendMessage(ctx context.Context, message *OutgoingMessage) error
}

// PlatformConfig 平台配置接口
// 思路：定义平台配置的统一接口
type PlatformConfig interface {
	// GetSetting 获取配置项
	GetSetting(key string) (string, bool)
	
	// GetAllSettings 获取所有配置项
	GetAllSettings() map[string]string
}

// AdapterStats 适配器统计信息
// 思路：统一的适配器状态和统计信息
type AdapterStats struct {
	Platform         string    `json:"platform"`
	Status           string    `json:"status"`
	ConnectionStatus string    `json:"connection_status"`
	StartTime        time.Time `json:"start_time"`
	LastMessageTime  time.Time `json:"last_message_time"`
	MessagesReceived int64     `json:"messages_received"`
	MessagesSent     int64     `json:"messages_sent"`
	ErrorCount       int64     `json:"error_count"`
	LastError        string    `json:"last_error,omitempty"`
}

// PlatformAdapter 平台适配器接口
// 思路：定义统一的平台接口，所有平台适配器都需要实现这些方法
type PlatformAdapter interface {
	// GetPlatformName 获取平台名称
	GetPlatformName() string
	
	// Initialize 初始化适配器
	// 思路：使用配置初始化适配器
	Initialize(config PlatformConfig) error
	
	// Start 启动适配器
	// 思路：启动适配器，开始接收和发送消息
	Start(ctx context.Context) error
	
	// SendMessage 发送消息
	// 思路：向平台发送消息
	SendMessage(ctx context.Context, message *OutgoingMessage) error
	
	// GetUserInfo 获取用户信息
	// 思路：从平台获取用户详细信息
	GetUserInfo(ctx context.Context, platformUserID string) (*UserInfo, error)
	
	// Stop 停止适配器
	// 思路：优雅停止适配器
	Stop(ctx context.Context) error
	
	// IsHealthy 健康检查
	// 思路：检查适配器是否正常工作
	IsHealthy() bool
	
	// GetStats 获取适配器统计信息
	// 思路：返回消息发送、接收等统计数据
	GetStats() *AdapterStats
	
	// SetMessageHandler 设置消息处理器
	// 思路：设置处理接收消息的处理器
	SetMessageHandler(handler MessageHandler)
}

// ScriptContext 脚本执行上下文
// 思路：为脚本提供必要的运行时信息和API
type ScriptContext struct {
	Message     *IncomingMessage       `json:"message"`
	Variables   map[string]interface{} `json:"variables"`
	Platform    string                 `json:"platform"`
	ChatID      string                 `json:"chat_id"`
	Timestamp   time.Time              `json:"timestamp"`
	RequestID   string                 `json:"request_id"`
}

// ScriptResult 脚本执行结果
// 思路：返回脚本执行的结果和状态
type ScriptResult struct {
	Success     bool                   `json:"success"`
	Result      interface{}            `json:"result"`
	Error       string                 `json:"error,omitempty"`
	Duration    time.Duration          `json:"duration"`
	Output      string                 `json:"output,omitempty"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	Actions     []ScriptAction         `json:"actions,omitempty"`
	ShouldStop  bool                   `json:"should_stop"`  // 是否停止后续脚本执行
}

// ScriptAction 脚本动作
// 思路：脚本可以执行的动作类型
type ScriptAction struct {
	Type   string                 `json:"type"`   // send_message, log, set_variable, call_api
	Target string                 `json:"target"` // 动作目标
	Data   map[string]interface{} `json:"data"`   // 动作数据
}

// ScriptEngine 脚本引擎接口
// 思路：定义脚本执行的统一接口
type ScriptEngine interface {
	// ExecuteScript 执行脚本
	ExecuteScript(ctx context.Context, scriptID string, scriptCtx *ScriptContext) (*ScriptResult, error)
	
	// ExecuteScriptsByType 按类型执行脚本
	ExecuteScriptsByType(ctx context.Context, scriptType string, scriptCtx *ScriptContext) ([]*ScriptResult, error)
}
