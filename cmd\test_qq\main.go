package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// TestMessage 测试消息结构体
// 思路：模拟NapCat发送的消息格式
type TestMessage struct {
	PostType    string `json:"post_type"`
	MessageType string `json:"message_type"`
	Time        int64  `json:"time"`
	SelfID      int64  `json:"self_id"`
	UserID      int64  `json:"user_id"`
	GroupID     int64  `json:"group_id,omitempty"`
	Message     string `json:"message"`
	RawMessage  string `json:"raw_message"`
	MessageID   int32  `json:"message_id"`
	Sender      struct {
		UserID   int64  `json:"user_id"`
		Nickname string `json:"nickname"`
		Card     string `json:"card"`
		Role     string `json:"role"`
	} `json:"sender"`
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	PlatformUserID string `json:"platform_user_id"`
	PlatformChatID string `json:"platform_chat_id,omitempty"`
	Content        string `json:"content"`
	MessageType    string `json:"message_type"`
}

var (
	serverURL = flag.String("server", "http://localhost:8080", "客服系统服务器地址")
	userID    = flag.Int64("user", 123456789, "测试用户QQ号")
	groupID   = flag.Int64("group", 0, "测试群号（可选）")
	message   = flag.String("msg", "你好，这是测试消息", "测试消息内容")
	mode      = flag.String("mode", "webhook", "测试模式：webhook, send, status")
)

func main() {
	flag.Parse()

	switch *mode {
	case "webhook":
		testWebHook()
	case "send":
		testSendMessage()
	case "status":
		testStatus()
	default:
		fmt.Println("支持的测试模式：webhook, send, status")
		flag.Usage()
	}
}

// testWebHook 测试WebHook接收
// 思路：模拟NapCat发送消息到客服系统
func testWebHook() {
	fmt.Println("=== 测试QQ WebHook ===")

	// 构建测试消息
	testMsg := TestMessage{
		PostType:    "message",
		MessageType: "private",
		Time:        time.Now().Unix(),
		SelfID:      987654321,
		UserID:      *userID,
		Message:     *message,
		RawMessage:  *message,
		MessageID:   int32(time.Now().Unix()),
	}

	// 如果指定了群号，设置为群消息
	if *groupID != 0 {
		testMsg.MessageType = "group"
		testMsg.GroupID = *groupID
	}

	// 设置发送者信息
	testMsg.Sender.UserID = *userID
	testMsg.Sender.Nickname = "测试用户"
	testMsg.Sender.Role = "member"

	// 序列化消息
	msgBytes, err := json.Marshal(testMsg)
	if err != nil {
		log.Fatalf("序列化消息失败: %v", err)
	}

	// 发送到WebHook
	webhookURL := *serverURL + "/api/v1/platforms/qq/webhook"
	fmt.Printf("发送WebHook到: %s\n", webhookURL)
	fmt.Printf("消息内容: %s\n", string(msgBytes))

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(msgBytes))
	if err != nil {
		log.Fatalf("发送WebHook失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ WebHook测试成功")
	} else {
		fmt.Println("❌ WebHook测试失败")
	}
}

// testSendMessage 测试发送消息
// 思路：通过API发送消息到QQ平台
func testSendMessage() {
	fmt.Println("=== 测试发送QQ消息 ===")

	// 构建发送请求
	sendReq := SendMessageRequest{
		PlatformUserID: fmt.Sprintf("%d", *userID),
		Content:        *message,
		MessageType:    "text",
	}

	// 如果指定了群号，设置群消息
	if *groupID != 0 {
		sendReq.PlatformChatID = fmt.Sprintf("%d", *groupID)
	}

	// 序列化请求
	reqBytes, err := json.Marshal(sendReq)
	if err != nil {
		log.Fatalf("序列化请求失败: %v", err)
	}

	// 发送请求
	sendURL := *serverURL + "/api/v1/platforms/qq/send"
	fmt.Printf("发送消息到: %s\n", sendURL)
	fmt.Printf("请求内容: %s\n", string(reqBytes))

	resp, err := http.Post(sendURL, "application/json", bytes.NewBuffer(reqBytes))
	if err != nil {
		log.Fatalf("发送消息失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ 发送消息测试成功")
	} else {
		fmt.Println("❌ 发送消息测试失败")
	}
}

// testStatus 测试平台状态
// 思路：检查QQ适配器的运行状态
func testStatus() {
	fmt.Println("=== 测试平台状态 ===")

	// 获取平台列表
	platformsURL := *serverURL + "/api/v1/platforms/"
	fmt.Printf("获取平台状态: %s\n", platformsURL)

	resp, err := http.Get(platformsURL)
	if err != nil {
		log.Fatalf("获取平台状态失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("平台状态: %s\n", string(body))

	// 获取详细统计
	statsURL := *serverURL + "/api/v1/platforms/stats"
	fmt.Printf("\n获取详细统计: %s\n", statsURL)

	resp2, err := http.Get(statsURL)
	if err != nil {
		log.Fatalf("获取统计信息失败: %v", err)
	}
	defer resp2.Body.Close()

	body2, err := io.ReadAll(resp2.Body)
	if err != nil {
		log.Fatalf("读取统计响应失败: %v", err)
	}

	fmt.Printf("统计信息: %s\n", string(body2))

	if resp.StatusCode == 200 && resp2.StatusCode == 200 {
		fmt.Println("✅ 状态检查成功")
	} else {
		fmt.Println("❌ 状态检查失败")
	}
}

// 使用示例：
// 
// # 测试WebHook接收
// go run cmd/test_qq/main.go -mode=webhook -user=123456789 -msg="测试消息"
//
// # 测试群消息WebHook
// go run cmd/test_qq/main.go -mode=webhook -user=123456789 -group=987654321 -msg="群消息测试"
//
// # 测试发送消息（需要NapCat运行）
// go run cmd/test_qq/main.go -mode=send -user=123456789 -msg="发送测试"
//
// # 测试平台状态
// go run cmd/test_qq/main.go -mode=status
//
// # 指定服务器地址
// go run cmd/test_qq/main.go -server=http://localhost:8080 -mode=status
