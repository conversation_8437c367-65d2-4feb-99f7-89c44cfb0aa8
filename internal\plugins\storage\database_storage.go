package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"aike_go/internal/models"
	"aike_go/internal/plugins"

	"gorm.io/gorm"
)

// DatabaseStoragePlugin 数据库存储插件
// 思路：使用GORM将聊天记录存储到数据库中
type DatabaseStoragePlugin struct {
	name        string
	version     string
	description string
	enabled     bool
	config      map[string]interface{}
	db          *gorm.DB
}

// 注意：不再使用独立的ChatMessageModel，直接使用现有的models.Message
// 思路：利用现有的messages表和关联关系，避免数据冗余

// NewDatabaseStoragePlugin 创建数据库存储插件
// 思路：初始化插件实例
func NewDatabaseStoragePlugin(db *gorm.DB) *DatabaseStoragePlugin {
	return &DatabaseStoragePlugin{
		name:        "database_storage",
		version:     "1.0.0",
		description: "基于数据库的聊天记录存储插件",
		enabled:     true,
		config: map[string]interface{}{
			"table_name":      "plugin_chat_messages",
			"auto_migrate":    true,
			"retention_days":  365,
			"backup_enabled":  true,
			"backup_interval": "24h",
			"compression":     true,
			"encryption":      false,
		},
		db: db,
	}
}

// GetName 获取插件名称
func (p *DatabaseStoragePlugin) GetName() string {
	return p.name
}

// GetVersion 获取插件版本
func (p *DatabaseStoragePlugin) GetVersion() string {
	return p.version
}

// GetDescription 获取插件描述
func (p *DatabaseStoragePlugin) GetDescription() string {
	return p.description
}

// IsEnabled 检查插件是否启用
func (p *DatabaseStoragePlugin) IsEnabled() bool {
	return p.enabled
}

// Enable 启用插件
func (p *DatabaseStoragePlugin) Enable() error {
	p.enabled = true
	log.Printf("数据库存储插件已启用")
	return nil
}

// Disable 禁用插件
func (p *DatabaseStoragePlugin) Disable() error {
	p.enabled = false
	log.Printf("数据库存储插件已禁用")
	return nil
}

// GetConfig 获取插件配置
func (p *DatabaseStoragePlugin) GetConfig() map[string]interface{} {
	return p.config
}

// SetConfig 设置插件配置
func (p *DatabaseStoragePlugin) SetConfig(config map[string]interface{}) error {
	for k, v := range config {
		p.config[k] = v
	}
	log.Printf("数据库存储插件配置已更新: %+v", config)
	return nil
}

// Initialize 初始化插件
func (p *DatabaseStoragePlugin) Initialize(ctx context.Context) error {
	if !p.enabled {
		return nil
	}

	// 注意：不再需要迁移，直接使用现有的messages表
	// 思路：利用现有的数据库结构，避免重复迁移
	log.Printf("数据库存储插件初始化完成（使用现有messages表）")

	log.Printf("数据库存储插件初始化完成")
	return nil
}

// Cleanup 清理插件资源
func (p *DatabaseStoragePlugin) Cleanup(ctx context.Context) error {
	log.Printf("数据库存储插件清理完成")
	return nil
}

// StoreMessage 存储消息
func (p *DatabaseStoragePlugin) StoreMessage(ctx context.Context, message *plugins.ChatMessage) error {
	if !p.enabled {
		return fmt.Errorf("插件未启用")
	}

	// 序列化元数据
	metadataJSON, err := json.Marshal(message.Metadata)
	if err != nil {
		return fmt.Errorf("序列化元数据失败: %w", err)
	}

	// 首先查找或创建用户
	// 思路：message.UserID 始终是 platform_id，不是数据库ID
	// 修复：统一通过 platform_id 查找用户，避免混淆
	var user models.User
	err = p.db.Where("platform_id = ? AND platform = ?", message.UserID, message.Platform).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新用户
			user = models.User{
				PlatformID: message.UserID,
				Platform:   message.Platform,
				Status:     "active",
			}
			if err := p.db.Create(&user).Error; err != nil {
				return fmt.Errorf("创建用户失败: %w", err)
			}
			// log.Printf("为平台用户创建新用户记录: %s@%s -> ID:%d", message.UserID, message.Platform, user.ID)
		} else {
			return fmt.Errorf("查询用户失败: %w", err)
		}
	}

	// 转换为现有的Message模型
	// 思路：使用现有的messages表结构，保持数据一致性
	model := &models.Message{
		UserID:        user.ID, // 使用真实的用户ID
		SessionID:     message.SessionID,
		Content:       message.Content,
		Type:          message.MessageType,
		Direction:     message.Direction,
		Platform:      message.Platform,
		PlatformMsgID: message.ID, // 将插件消息ID存储为平台消息ID
		Status:        "sent",
		Metadata:      string(metadataJSON),
	}

	// 设置AI相关字段（如果有）
	// 思路：从元数据中提取AI相关信息
	if message.Metadata != nil {
		if isAI, exists := message.Metadata["is_ai_generated"]; exists {
			if aiFlag, ok := isAI.(bool); ok {
				model.IsAIGenerated = aiFlag
			}
		}
		if aiModel, exists := message.Metadata["ai_model"]; exists {
			if modelStr, ok := aiModel.(string); ok {
				model.AIModel = modelStr
			}
		}
		if tokens, exists := message.Metadata["tokens_used"]; exists {
			if tokenCount, ok := tokens.(float64); ok {
				model.TokensUsed = int(tokenCount)
			}
		}
	}

	// 保存到数据库
	if err := p.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("保存消息失败: %w", err)
	}

	log.Printf("消息已存储: %s (用户: %s, 平台: %s)", message.ID, message.UserID, message.Platform)
	return nil
}

// GetMessages 获取消息列表
func (p *DatabaseStoragePlugin) GetMessages(ctx context.Context, filter *plugins.MessageFilter) ([]*plugins.ChatMessage, error) {
	if !p.enabled {
		return nil, fmt.Errorf("插件未启用")
	}

	query := p.db.WithContext(ctx).Model(&models.Message{}).Preload("User")

	// 应用过滤条件
	// 思路：根据用户平台ID或数据库ID查询
	if filter.UserID != "" {
		// 尝试解析为数字ID，如果失败则按平台ID查询
		if userID, err := strconv.ParseUint(filter.UserID, 10, 32); err == nil {
			query = query.Where("user_id = ?", uint(userID))
		} else {
			// 通过关联用户表查询
			query = query.Joins("JOIN users ON messages.user_id = users.id").
				Where("users.platform_id = ?", filter.UserID)
		}
	}
	if filter.Platform != "" {
		query = query.Where("platform = ?", filter.Platform)
	}
	if filter.SessionID != "" {
		query = query.Where("session_id = ?", filter.SessionID)
	}
	if filter.MessageType != "" {
		query = query.Where("type = ?", filter.MessageType)
	}
	if filter.Direction != "" {
		query = query.Where("direction = ?", filter.Direction)
	}
	if !filter.StartTime.IsZero() {
		query = query.Where("created_at >= ?", filter.StartTime)
	}
	if !filter.EndTime.IsZero() {
		query = query.Where("created_at <= ?", filter.EndTime)
	}
	if filter.Keyword != "" {
		query = query.Where("content LIKE ?", "%"+filter.Keyword+"%")
	}

	// 排序和分页
	query = query.Order("created_at DESC")
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var dbMessages []models.Message
	if err := query.Find(&dbMessages).Error; err != nil {
		return nil, fmt.Errorf("查询消息失败: %w", err)
	}

	// 转换为插件格式
	// 思路：将数据库Message模型转换为插件ChatMessage格式
	messages := make([]*plugins.ChatMessage, len(dbMessages))
	for i, dbMsg := range dbMessages {
		message, err := p.convertDBMessageToPlugin(&dbMsg)
		if err != nil {
			log.Printf("转换消息失败: %v", err)
			continue
		}
		messages[i] = message
	}

	return messages, nil
}

// GetMessageByID 根据ID获取消息
func (p *DatabaseStoragePlugin) GetMessageByID(ctx context.Context, messageID string) (*plugins.ChatMessage, error) {
	if !p.enabled {
		return nil, fmt.Errorf("插件未启用")
	}

	var dbMsg models.Message
	if err := p.db.WithContext(ctx).Preload("User").Where("platform_msg_id = ?", messageID).First(&dbMsg).Error; err != nil {
		return nil, fmt.Errorf("查询消息失败: %w", err)
	}

	return p.convertDBMessageToPlugin(&dbMsg)
}

// UpdateMessage 更新消息
func (p *DatabaseStoragePlugin) UpdateMessage(ctx context.Context, messageID string, updates map[string]interface{}) error {
	if !p.enabled {
		return fmt.Errorf("插件未启用")
	}

	if err := p.db.WithContext(ctx).Model(&models.Message{}).Where("platform_msg_id = ?", messageID).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新消息失败: %w", err)
	}

	log.Printf("消息已更新: %s", messageID)
	return nil
}

// DeleteMessage 删除消息
func (p *DatabaseStoragePlugin) DeleteMessage(ctx context.Context, messageID string) error {
	if !p.enabled {
		return fmt.Errorf("插件未启用")
	}

	if err := p.db.WithContext(ctx).Where("platform_msg_id = ?", messageID).Delete(&models.Message{}).Error; err != nil {
		return fmt.Errorf("删除消息失败: %w", err)
	}

	log.Printf("消息已删除: %s", messageID)
	return nil
}

// GetConversation 获取对话历史
func (p *DatabaseStoragePlugin) GetConversation(ctx context.Context, userID, platform string, limit int) ([]*plugins.ChatMessage, error) {
	filter := &plugins.MessageFilter{
		UserID:   userID,
		Platform: platform,
		Limit:    limit,
	}
	return p.GetMessages(ctx, filter)
}

// convertDBMessageToPlugin 转换数据库Message模型为插件消息
// 思路：将现有的Message模型转换为插件需要的ChatMessage格式
func (p *DatabaseStoragePlugin) convertDBMessageToPlugin(dbMsg *models.Message) (*plugins.ChatMessage, error) {
	var metadata map[string]interface{}
	if dbMsg.Metadata != "" {
		if err := json.Unmarshal([]byte(dbMsg.Metadata), &metadata); err != nil {
			return nil, fmt.Errorf("反序列化元数据失败: %w", err)
		}
	} else {
		metadata = make(map[string]interface{})
	}

	// 添加AI相关信息到元数据
	if dbMsg.IsAIGenerated {
		metadata["is_ai_generated"] = true
		metadata["ai_model"] = dbMsg.AIModel
		metadata["tokens_used"] = dbMsg.TokensUsed
	}

	// 获取用户平台ID
	userID := strconv.FormatUint(uint64(dbMsg.UserID), 10)
	if dbMsg.User.PlatformID != "" {
		userID = dbMsg.User.PlatformID
	}

	return &plugins.ChatMessage{
		ID:          dbMsg.PlatformMsgID, // 使用平台消息ID
		UserID:      userID,              // 使用平台用户ID
		Platform:    dbMsg.Platform,
		Content:     dbMsg.Content,
		MessageType: dbMsg.Type,
		Direction:   dbMsg.Direction,
		SessionID:   dbMsg.SessionID,
		GroupID:     "", // Message模型中没有GroupID，可以从元数据中获取
		ReplyToID:   "", // 可以从ParentID转换
		Metadata:    metadata,
		Timestamp:   dbMsg.CreatedAt,
		CreatedAt:   dbMsg.CreatedAt,
		UpdatedAt:   dbMsg.UpdatedAt,
	}, nil
}

// GetStats 获取存储统计信息
func (p *DatabaseStoragePlugin) GetStats(ctx context.Context) (*plugins.StorageStats, error) {
	if !p.enabled {
		return nil, fmt.Errorf("插件未启用")
	}

	stats := &plugins.StorageStats{}

	// 总消息数
	var totalMessages int64
	p.db.WithContext(ctx).Model(&models.Message{}).Count(&totalMessages)
	stats.TotalMessages = totalMessages

	// 总用户数
	var totalUsers int64
	p.db.WithContext(ctx).Model(&models.Message{}).Distinct("user_id").Count(&totalUsers)
	stats.TotalUsers = totalUsers

	// 总会话数
	var totalSessions int64
	p.db.WithContext(ctx).Model(&models.Message{}).Distinct("session_id").Count(&totalSessions)
	stats.TotalSessions = totalSessions

	// 最早和最新消息时间
	var oldest, newest models.Message
	p.db.WithContext(ctx).Model(&models.Message{}).Order("created_at ASC").First(&oldest)
	p.db.WithContext(ctx).Model(&models.Message{}).Order("created_at DESC").First(&newest)
	stats.OldestMessage = oldest.CreatedAt
	stats.NewestMessage = newest.CreatedAt

	// 最近7天的消息数量
	stats.MessagesByDay = make([]int64, 7)
	for i := 0; i < 7; i++ {
		date := time.Now().AddDate(0, 0, -i)
		startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		var count int64
		p.db.WithContext(ctx).Model(&models.Message{}).
			Where("created_at >= ? AND created_at < ?", startOfDay, endOfDay).
			Count(&count)
		stats.MessagesByDay[6-i] = count
	}

	// 最活跃的平台
	var platformStats []struct {
		Platform string
		Count    int64
	}
	p.db.WithContext(ctx).Model(&models.Message{}).
		Select("platform, COUNT(*) as count").
		Group("platform").
		Order("count DESC").
		Limit(5).
		Scan(&platformStats)

	stats.TopPlatforms = make([]string, len(platformStats))
	for i, stat := range platformStats {
		stats.TopPlatforms[i] = stat.Platform
	}

	return stats, nil
}

// Backup 备份数据
func (p *DatabaseStoragePlugin) Backup(ctx context.Context, backupPath string) error {
	if !p.enabled {
		return fmt.Errorf("插件未启用")
	}

	// 这里可以实现数据库备份逻辑
	// 例如导出为JSON文件或SQL文件
	log.Printf("开始备份聊天记录到: %s", backupPath)

	// 简单实现：导出为JSON
	var messages []models.Message
	if err := p.db.WithContext(ctx).Preload("User").Find(&messages).Error; err != nil {
		return fmt.Errorf("查询备份数据失败: %w", err)
	}

	// 这里可以添加实际的文件写入逻辑
	log.Printf("备份完成，共备份 %d 条消息", len(messages))
	return nil
}

// Restore 恢复数据
func (p *DatabaseStoragePlugin) Restore(ctx context.Context, backupPath string) error {
	if !p.enabled {
		return fmt.Errorf("插件未启用")
	}

	log.Printf("开始从备份恢复数据: %s", backupPath)

	// 这里可以实现数据恢复逻辑
	// 例如从JSON文件或SQL文件恢复

	log.Printf("数据恢复完成")
	return nil
}
