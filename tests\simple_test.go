package services

import (
	"context"
	"testing"

	"aike_go/internal/config"

	"github.com/stretchr/testify/assert"
)

// TestChatService_Creation 测试聊天服务创建
// 思路：验证聊天服务能够正确创建
func TestChatService_Creation(t *testing.T) {
	// 创建配置
	cfg := &config.Config{
		OpenAI: config.OpenAIConfig{
			APIKey:  "test-api-key",
			BaseURL: "https://api.openai.com/v1",
			Model:   "gpt-3.5-turbo",
		},
	}

	// 创建聊天服务
	chatService := NewChatService(cfg)

	// 验证服务创建成功
	assert.NotNil(t, chatService)
}

// TestKnowledgeService_Creation 测试知识库服务创建
// 思路：验证知识库服务能够正确创建
func TestKnowledgeService_Creation(t *testing.T) {
	// 创建知识库服务
	knowledgeService := NewKnowledgeService()

	// 验证服务创建成功
	assert.NotNil(t, knowledgeService)
}

// TestProcessMessageRequest_Validation 测试消息请求验证
// 思路：验证消息请求的基本验证逻辑
func TestProcessMessageRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request ProcessMessageRequest
		wantErr bool
	}{
		{
			name: "有效请求",
			request: ProcessMessageRequest{
				UserID:   "user123",
				Platform: "test",
				Content:  "测试消息",
				Type:     "text",
			},
			wantErr: false,
		},
		{
			name: "空用户ID",
			request: ProcessMessageRequest{
				UserID:   "",
				Platform: "test",
				Content:  "测试消息",
				Type:     "text",
			},
			wantErr: true,
		},
		{
			name: "空内容",
			request: ProcessMessageRequest{
				UserID:   "user123",
				Platform: "test",
				Content:  "",
				Type:     "text",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateProcessMessageRequest(&tt.request)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// validateProcessMessageRequest 验证消息处理请求
// 思路：简单的请求验证逻辑
func validateProcessMessageRequest(req *ProcessMessageRequest) error {
	if req.UserID == "" {
		return assert.AnError
	}
	if req.Content == "" {
		return assert.AnError
	}
	return nil
}

// TestSearchKnowledgeRequest_Validation 测试知识库搜索请求验证
// 思路：验证知识库搜索请求的基本验证逻辑
func TestSearchKnowledgeRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		query   string
		limit   int
		wantErr bool
	}{
		{
			name:    "有效搜索",
			query:   "工作时间",
			limit:   10,
			wantErr: false,
		},
		{
			name:    "空查询",
			query:   "",
			limit:   10,
			wantErr: true,
		},
		{
			name:    "无效限制",
			query:   "工作时间",
			limit:   0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateSearchRequest(tt.query, tt.limit)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// validateSearchRequest 验证搜索请求
// 思路：简单的搜索请求验证逻辑
func validateSearchRequest(query string, limit int) error {
	if query == "" {
		return assert.AnError
	}
	if limit <= 0 {
		return assert.AnError
	}
	return nil
}

// TestConfig_Validation 测试配置验证
// 思路：验证配置的基本验证逻辑
func TestConfig_Validation(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
	}{
		{
			name: "有效配置",
			config: &config.Config{
				Host: "localhost",
				Port: 8080,
				OpenAI: config.OpenAIConfig{
					APIKey: "test-key",
					Model:  "gpt-3.5-turbo",
				},
			},
			wantErr: false,
		},
		{
			name: "无效端口",
			config: &config.Config{
				Host: "localhost",
				Port: 0,
				OpenAI: config.OpenAIConfig{
					APIKey: "test-key",
					Model:  "gpt-3.5-turbo",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateConfig(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// validateConfig 验证配置
// 思路：简单的配置验证逻辑
func validateConfig(cfg *config.Config) error {
	if cfg.Port <= 0 {
		return assert.AnError
	}
	return nil
}

// BenchmarkChatService_Creation 聊天服务创建基准测试
// 思路：测试聊天服务创建的性能
func BenchmarkChatService_Creation(b *testing.B) {
	cfg := &config.Config{
		OpenAI: config.OpenAIConfig{
			APIKey: "test-key",
			Model:  "gpt-3.5-turbo",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewChatService(cfg)
	}
}

// BenchmarkKnowledgeService_Creation 知识库服务创建基准测试
// 思路：测试知识库服务创建的性能
func BenchmarkKnowledgeService_Creation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewKnowledgeService()
	}
}

// TestContext_Timeout 测试上下文超时处理
// 思路：验证服务能够正确处理上下文超时
func TestContext_Timeout(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 立即取消上下文
	cancel()
	
	// 验证上下文已取消
	select {
	case <-ctx.Done():
		assert.Error(t, ctx.Err())
	default:
		t.Error("上下文应该已经取消")
	}
}

// TestServiceInterface 测试服务接口
// 思路：验证服务实现了预期的接口
func TestServiceInterface(t *testing.T) {
	cfg := &config.Config{
		OpenAI: config.OpenAIConfig{
			APIKey: "test-key",
			Model:  "gpt-3.5-turbo",
		},
	}

	chatService := NewChatService(cfg)
	knowledgeService := NewKnowledgeService()

	// 验证服务不为空
	assert.NotNil(t, chatService)
	assert.NotNil(t, knowledgeService)

	// 验证服务类型
	assert.IsType(t, &ChatService{}, chatService)
	assert.IsType(t, &KnowledgeService{}, knowledgeService)
}
