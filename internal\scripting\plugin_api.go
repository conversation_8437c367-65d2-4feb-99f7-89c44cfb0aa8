package scripting

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"aike_go/internal/plugins"
	"aike_go/internal/plugins/storage"

	lua "github.com/yuin/gopher-lua"
)

// PluginAPI Lua插件管理API
// 思路：为Lua脚本提供插件管理功能
type PluginAPI struct {
	pluginManager plugins.PluginManager
	chatStorage   plugins.ChatStoragePlugin
	// 分布式存储插件
	distributedStorage plugins.Plugin
}

// NewPluginAPI 创建插件API
func NewPluginAPI(pluginManager plugins.PluginManager) *PluginAPI {
	return &PluginAPI{
		pluginManager: pluginManager,
	}
}

// SetChatStoragePlugin 设置聊天存储插件
func (api *PluginAPI) SetChatStoragePlugin(plugin plugins.ChatStoragePlugin) {
	api.chatStorage = plugin
}

// SetDistributedStoragePlugin 设置分布式存储插件
func (api *PluginAPI) SetDistributedStoragePlugin(plugin plugins.Plugin) {
	api.distributedStorage = plugin
}

// RegisterLuaFunctions 注册Lua函数
// 思路：将插件管理功能暴露给Lua脚本
func (api *PluginAPI) RegisterLuaFunctions(L *lua.LState) {
	// 插件管理函数
	L.SetGlobal("enable_plugin", L.NewFunction(api.luaEnablePlugin))
	L.SetGlobal("disable_plugin", L.NewFunction(api.luaDisablePlugin))
	L.SetGlobal("get_plugin_config", L.NewFunction(api.luaGetPluginConfig))
	L.SetGlobal("set_plugin_config", L.NewFunction(api.luaSetPluginConfig))
	L.SetGlobal("list_plugins", L.NewFunction(api.luaListPlugins))
	L.SetGlobal("get_plugin_stats", L.NewFunction(api.luaGetPluginStats))
	L.SetGlobal("reload_plugin", L.NewFunction(api.luaReloadPlugin))

	// 聊天记录存储函数
	L.SetGlobal("store_message", L.NewFunction(api.luaStoreMessage))
	L.SetGlobal("get_messages", L.NewFunction(api.luaGetMessages))
	L.SetGlobal("get_conversation", L.NewFunction(api.luaGetConversation))
	L.SetGlobal("search_messages", L.NewFunction(api.luaSearchMessages))
	L.SetGlobal("get_storage_stats", L.NewFunction(api.luaGetStorageStats))

	// 分布式存储函数
	L.SetGlobal("store_distributed_message", L.NewFunction(api.luaStoreDistributedMessage))
	L.SetGlobal("get_distributed_storage_statistics", L.NewFunction(api.luaGetDistributedStorageStats))
	L.SetGlobal("backup_distributed_data", L.NewFunction(api.luaBackupDistributedData))
	L.SetGlobal("backup_messages", L.NewFunction(api.luaBackupMessages))

	// log.Printf("插件管理Lua API已注册")
}

// luaEnablePlugin 启用插件
// 使用例子：enable_plugin("database_storage")
func (api *PluginAPI) luaEnablePlugin(L *lua.LState) int {
	pluginName := L.ToString(1)
	if pluginName == "" {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("插件名称不能为空"))
		return 2
	}

	err := api.pluginManager.EnablePlugin(pluginName)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString(fmt.Sprintf("插件 %s 已启用", pluginName)))
	return 2
}

// luaDisablePlugin 禁用插件
// 使用例子：disable_plugin("database_storage")
func (api *PluginAPI) luaDisablePlugin(L *lua.LState) int {
	pluginName := L.ToString(1)
	if pluginName == "" {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("插件名称不能为空"))
		return 2
	}

	err := api.pluginManager.DisablePlugin(pluginName)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString(fmt.Sprintf("插件 %s 已禁用", pluginName)))
	return 2
}

// luaGetPluginConfig 获取插件配置
// 使用例子：config = get_plugin_config("database_storage")
func (api *PluginAPI) luaGetPluginConfig(L *lua.LState) int {
	pluginName := L.ToString(1)
	if pluginName == "" {
		L.Push(lua.LNil)
		return 1
	}

	config, err := api.pluginManager.GetPluginConfig(pluginName)
	if err != nil {
		L.Push(lua.LNil)
		return 1
	}

	// 转换为Lua表
	luaTable := api.mapToLuaTable(L, config)
	L.Push(luaTable)
	return 1
}

// luaSetPluginConfig 设置插件配置
// 使用例子：set_plugin_config("database_storage", {retention_days = 30})
func (api *PluginAPI) luaSetPluginConfig(L *lua.LState) int {
	pluginName := L.ToString(1)
	configTable := L.ToTable(2)

	if pluginName == "" || configTable == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("参数无效"))
		return 2
	}

	// 转换Lua表为Go map
	config := api.luaTableToMap(configTable)

	err := api.pluginManager.SetPluginConfig(pluginName, config)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString(fmt.Sprintf("插件 %s 配置已更新", pluginName)))
	return 2
}

// luaListPlugins 列出所有插件
// 使用例子：plugins = list_plugins()
func (api *PluginAPI) luaListPlugins(L *lua.LState) int {
	plugins := api.pluginManager.GetAllPlugins()

	luaTable := L.NewTable()
	for i, plugin := range plugins {
		pluginInfo := L.NewTable()
		pluginInfo.RawSetString("name", lua.LString(plugin.GetName()))
		pluginInfo.RawSetString("version", lua.LString(plugin.GetVersion()))
		pluginInfo.RawSetString("description", lua.LString(plugin.GetDescription()))
		pluginInfo.RawSetString("enabled", lua.LBool(plugin.IsEnabled()))

		luaTable.RawSetInt(i+1, pluginInfo)
	}

	L.Push(luaTable)
	return 1
}

// luaGetPluginStats 获取插件统计信息
// 使用例子：stats = get_plugin_stats()
func (api *PluginAPI) luaGetPluginStats(L *lua.LState) int {
	stats := api.pluginManager.GetPluginStats()
	luaTable := api.mapToLuaTable(L, stats)
	L.Push(luaTable)
	return 1
}

// luaReloadPlugin 重新加载插件
// 使用例子：reload_plugin("database_storage")
func (api *PluginAPI) luaReloadPlugin(L *lua.LState) int {
	pluginName := L.ToString(1)
	if pluginName == "" {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("插件名称不能为空"))
		return 2
	}

	err := api.pluginManager.ReloadPlugin(pluginName)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString(fmt.Sprintf("插件 %s 已重新加载", pluginName)))
	return 2
}

// luaStoreMessage 存储消息
// 使用例子：store_message(message_data)
func (api *PluginAPI) luaStoreMessage(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("聊天存储插件未启用"))
		return 2
	}

	messageTable := L.ToTable(1)
	if messageTable == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("消息数据无效"))
		return 2
	}

	// 转换为ChatMessage
	message := api.luaTableToChatMessage(messageTable)
	if message == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("消息格式无效"))
		return 2
	}

	err := api.chatStorage.StoreMessage(context.Background(), message)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString("消息已存储"))
	return 2
}

// luaGetMessages 获取消息列表
// 使用例子：messages = get_messages({user_id = "123", limit = 10})
func (api *PluginAPI) luaGetMessages(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LNil)
		return 1
	}

	filterTable := L.ToTable(1)
	filter := &plugins.MessageFilter{}

	if filterTable != nil {
		// 解析过滤条件
		if userID := filterTable.RawGetString("user_id"); userID != lua.LNil {
			filter.UserID = userID.String()
		}
		if platform := filterTable.RawGetString("platform"); platform != lua.LNil {
			filter.Platform = platform.String()
		}
		if limit := filterTable.RawGetString("limit"); limit != lua.LNil {
			if limitNum, ok := limit.(lua.LNumber); ok {
				filter.Limit = int(limitNum)
			}
		}
		if keyword := filterTable.RawGetString("keyword"); keyword != lua.LNil {
			filter.Keyword = keyword.String()
		}
	}

	messages, err := api.chatStorage.GetMessages(context.Background(), filter)
	if err != nil {
		L.Push(lua.LNil)
		return 1
	}

	// 转换为Lua表
	luaTable := L.NewTable()
	for i, message := range messages {
		messageTable := api.chatMessageToLuaTable(L, message)
		luaTable.RawSetInt(i+1, messageTable)
	}

	L.Push(luaTable)
	return 1
}

// luaGetConversation 获取对话历史
// 使用例子：conversation = get_conversation("user123", "qq", 20)
func (api *PluginAPI) luaGetConversation(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LNil)
		return 1
	}

	userID := L.ToString(1)
	platform := L.ToString(2)
	limit := int(L.ToNumber(3))

	if userID == "" || platform == "" {
		L.Push(lua.LNil)
		return 1
	}

	messages, err := api.chatStorage.GetConversation(context.Background(), userID, platform, limit)
	if err != nil {
		L.Push(lua.LNil)
		return 1
	}

	// 转换为Lua表
	luaTable := L.NewTable()
	for i, message := range messages {
		messageTable := api.chatMessageToLuaTable(L, message)
		luaTable.RawSetInt(i+1, messageTable)
	}

	L.Push(luaTable)
	return 1
}

// luaSearchMessages 搜索消息
// 使用例子：results = search_messages("关键词", {platform = "qq", limit = 10})
func (api *PluginAPI) luaSearchMessages(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LNil)
		return 1
	}

	keyword := L.ToString(1)
	optionsTable := L.ToTable(2)

	filter := &plugins.MessageFilter{
		Keyword: keyword,
		Limit:   10, // 默认限制
	}

	if optionsTable != nil {
		if platform := optionsTable.RawGetString("platform"); platform != lua.LNil {
			filter.Platform = platform.String()
		}
		if limit := optionsTable.RawGetString("limit"); limit != lua.LNil {
			if limitNum, ok := limit.(lua.LNumber); ok {
				filter.Limit = int(limitNum)
			}
		}
	}

	messages, err := api.chatStorage.GetMessages(context.Background(), filter)
	if err != nil {
		L.Push(lua.LNil)
		return 1
	}

	// 转换为Lua表
	luaTable := L.NewTable()
	for i, message := range messages {
		messageTable := api.chatMessageToLuaTable(L, message)
		luaTable.RawSetInt(i+1, messageTable)
	}

	L.Push(luaTable)
	return 1
}

// luaGetStorageStats 获取存储统计信息
// 使用例子：stats = get_storage_stats()
func (api *PluginAPI) luaGetStorageStats(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LNil)
		return 1
	}

	stats, err := api.chatStorage.GetStats(context.Background())
	if err != nil {
		L.Push(lua.LNil)
		return 1
	}

	// 转换为Lua表
	luaTable := L.NewTable()
	luaTable.RawSetString("total_messages", lua.LNumber(stats.TotalMessages))
	luaTable.RawSetString("total_users", lua.LNumber(stats.TotalUsers))
	luaTable.RawSetString("total_sessions", lua.LNumber(stats.TotalSessions))
	luaTable.RawSetString("storage_size", lua.LNumber(stats.StorageSize))

	L.Push(luaTable)
	return 1
}

// luaBackupMessages 备份消息
// 使用例子：backup_messages("/path/to/backup")
func (api *PluginAPI) luaBackupMessages(L *lua.LState) int {
	if api.chatStorage == nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("聊天存储插件未启用"))
		return 2
	}

	backupPath := L.ToString(1)
	if backupPath == "" {
		L.Push(lua.LBool(false))
		L.Push(lua.LString("备份路径不能为空"))
		return 2
	}

	err := api.chatStorage.Backup(context.Background(), backupPath)
	if err != nil {
		L.Push(lua.LBool(false))
		L.Push(lua.LString(err.Error()))
		return 2
	}

	L.Push(lua.LBool(true))
	L.Push(lua.LString("备份完成"))
	return 2
}

// 辅助方法：将Go map转换为Lua表
func (api *PluginAPI) mapToLuaTable(L *lua.LState, data map[string]interface{}) *lua.LTable {
	table := L.NewTable()
	for k, v := range data {
		switch val := v.(type) {
		case string:
			table.RawSetString(k, lua.LString(val))
		case int:
			table.RawSetString(k, lua.LNumber(val))
		case int64:
			table.RawSetString(k, lua.LNumber(val))
		case float64:
			table.RawSetString(k, lua.LNumber(val))
		case bool:
			table.RawSetString(k, lua.LBool(val))
		case map[string]interface{}:
			table.RawSetString(k, api.mapToLuaTable(L, val))
		case []interface{}:
			arrayTable := L.NewTable()
			for i, item := range val {
				if itemStr, ok := item.(string); ok {
					arrayTable.RawSetInt(i+1, lua.LString(itemStr))
				}
			}
			table.RawSetString(k, arrayTable)
		default:
			// 尝试JSON序列化
			if jsonData, err := json.Marshal(val); err == nil {
				table.RawSetString(k, lua.LString(string(jsonData)))
			}
		}
	}
	return table
}

// 辅助方法：将Lua表转换为Go map
func (api *PluginAPI) luaTableToMap(table *lua.LTable) map[string]interface{} {
	result := make(map[string]interface{})
	table.ForEach(func(key, value lua.LValue) {
		keyStr := key.String()
		switch val := value.(type) {
		case lua.LString:
			result[keyStr] = string(val)
		case lua.LNumber:
			result[keyStr] = float64(val)
		case lua.LBool:
			result[keyStr] = bool(val)
		case *lua.LTable:
			result[keyStr] = api.luaTableToMap(val)
		}
	})
	return result
}

// 辅助方法：将Lua表转换为ChatMessage
func (api *PluginAPI) luaTableToChatMessage(table *lua.LTable) *plugins.ChatMessage {
	message := &plugins.ChatMessage{
		Metadata: make(map[string]interface{}),
	}

	table.ForEach(func(key, value lua.LValue) {
		keyStr := key.String()
		switch keyStr {
		case "id":
			message.ID = value.String()
		case "user_id":
			message.UserID = value.String()
		case "platform":
			message.Platform = value.String()
		case "content":
			message.Content = value.String()
		case "message_type":
			message.MessageType = value.String()
		case "direction":
			message.Direction = value.String()
		case "session_id":
			message.SessionID = value.String()
		case "group_id":
			message.GroupID = value.String()
		case "reply_to_id":
			message.ReplyToID = value.String()
		}
	})

	return message
}

// 辅助方法：将ChatMessage转换为Lua表
func (api *PluginAPI) chatMessageToLuaTable(L *lua.LState, message *plugins.ChatMessage) *lua.LTable {
	table := L.NewTable()
	table.RawSetString("id", lua.LString(message.ID))
	table.RawSetString("user_id", lua.LString(message.UserID))
	table.RawSetString("platform", lua.LString(message.Platform))
	table.RawSetString("content", lua.LString(message.Content))
	table.RawSetString("message_type", lua.LString(message.MessageType))
	table.RawSetString("direction", lua.LString(message.Direction))
	table.RawSetString("session_id", lua.LString(message.SessionID))
	table.RawSetString("group_id", lua.LString(message.GroupID))
	table.RawSetString("reply_to_id", lua.LString(message.ReplyToID))
	table.RawSetString("timestamp", lua.LString(message.Timestamp.Format("2006-01-02 15:04:05")))
	table.RawSetString("created_at", lua.LString(message.CreatedAt.Format("2006-01-02 15:04:05")))

	// 添加元数据
	if len(message.Metadata) > 0 {
		metadataTable := api.mapToLuaTable(L, message.Metadata)
		table.RawSetString("metadata", metadataTable)
	}

	return table
}

// luaStoreDistributedMessage 存储消息到分布式存储
// 思路：通过分布式存储插件存储消息
func (api *PluginAPI) luaStoreDistributedMessage(L *lua.LState) int {
	messageTable := L.CheckTable(1)

	// 构建消息对象
	// 思路：从Lua表中提取所有必要字段，包括用户昵称和头像信息
	message := &plugins.ChatMessage{
		ID:           messageTable.RawGetString("id").String(),
		UserID:       messageTable.RawGetString("user_id").String(),
		Platform:     messageTable.RawGetString("platform").String(),
		Content:      messageTable.RawGetString("content").String(),
		MessageType:  messageTable.RawGetString("message_type").String(),
		UserNickname: messageTable.RawGetString("user_nickname").String(), // 添加昵称字段
		UserAvatar:   messageTable.RawGetString("user_avatar").String(),   // 添加头像字段
		Timestamp:    time.Now(),
	}

	// 获取元数据
	metadataValue := messageTable.RawGetString("metadata")
	if metadataTable, ok := metadataValue.(*lua.LTable); ok && metadataTable != nil {
		metadata := make(map[string]interface{})
		metadataTable.ForEach(func(k, v lua.LValue) {
			if k.Type() == lua.LTString {
				key := string(k.(lua.LString))
				switch v.Type() {
				case lua.LTString:
					metadata[key] = string(v.(lua.LString))
				case lua.LTNumber:
					metadata[key] = float64(v.(lua.LNumber))
				case lua.LTBool:
					metadata[key] = bool(v.(lua.LBool))
				}
			}
		})
		message.Metadata = metadata
	}

	// 调用分布式存储插件
	if api.distributedStorage != nil {
		// 直接调用分布式存储插件的StoreMessage方法
		// 使用类型断言转换为具体的分布式存储插件类型
		if storagePlugin, ok := api.distributedStorage.(*storage.DistributedStoragePlugin); ok {
			ctx := context.Background()
			err := storagePlugin.StoreMessage(ctx, *message)
			if err != nil {
				log.Printf("[Lua] 分布式存储失败: %v", err)
				L.Push(lua.LBool(false))
				L.Push(lua.LString(err.Error()))
			} else {
				// log.Printf("[Lua] 分布式存储成功: %s (用户: %s, 平台: %s)",					message.ID, message.UserID, message.Platform)
				L.Push(lua.LBool(true))
				L.Push(lua.LString(message.ID))
			}
		} else {
			log.Printf("[Lua] 分布式存储插件类型断言失败，实际类型: %T", api.distributedStorage)
			L.Push(lua.LBool(false))
			L.Push(lua.LString("分布式存储插件类型断言失败"))
		}
	} else {
		log.Printf("[Lua] 分布式存储插件未设置")
		L.Push(lua.LBool(false))
		L.Push(lua.LString("分布式存储插件未设置"))
	}

	return 2 // 返回两个值
}

// luaGetDistributedStorageStats 获取分布式存储统计信息
// 思路：通过分布式存储插件获取统计信息
func (api *PluginAPI) luaGetDistributedStorageStats(L *lua.LState) int {
	// 调用分布式存储插件
	if api.distributedStorage != nil {
		// 直接调用分布式存储插件的GetStats方法
		if storagePlugin, ok := api.distributedStorage.(*storage.DistributedStoragePlugin); ok {
			ctx := context.Background()
			stats, err := storagePlugin.GetStats(ctx)
			if err != nil {
				log.Printf("[Lua] 获取分布式存储统计失败: %v", err)
				L.Push(lua.LNil)
			} else {
				// 创建统计信息表
				statsTable := L.NewTable()
				statsTable.RawSetString("total_messages", lua.LNumber(stats.TotalMessages))
				statsTable.RawSetString("total_users", lua.LNumber(stats.TotalUsers))
				statsTable.RawSetString("total_sessions", lua.LNumber(stats.TotalSessions))
				statsTable.RawSetString("storage_size", lua.LNumber(stats.StorageSize))

				// 创建最活跃平台列表
				platformsTable := L.NewTable()
				for _, platform := range stats.TopPlatforms {
					platformsTable.Append(lua.LString(platform))
				}
				statsTable.RawSetString("top_platforms", platformsTable)

				L.Push(statsTable)
			}
		} else {
			log.Printf("[Lua] 分布式存储插件类型断言失败，实际类型: %T", api.distributedStorage)
			L.Push(lua.LNil)
		}
	} else {
		log.Printf("[Lua] 分布式存储插件未设置")
		L.Push(lua.LNil)
	}

	return 1
}

// luaBackupDistributedData 备份分布式存储数据
// 思路：通过分布式存储插件备份数据
func (api *PluginAPI) luaBackupDistributedData(L *lua.LState) int {
	backupPath := L.ToString(1)

	// 调用分布式存储插件
	if api.distributedStorage != nil {
		// 直接调用分布式存储插件的BackupData方法
		if storagePlugin, ok := api.distributedStorage.(*storage.DistributedStoragePlugin); ok {
			ctx := context.Background()
			err := storagePlugin.BackupData(ctx, backupPath)
			if err != nil {
				log.Printf("[Lua] 分布式存储备份失败: %v", err)
				L.Push(lua.LBool(false))
				L.Push(lua.LString(err.Error()))
			} else {
				log.Printf("[Lua] 分布式存储备份成功: %s", backupPath)
				L.Push(lua.LBool(true))
				L.Push(lua.LString(backupPath))
			}
		} else {
			log.Printf("[Lua] 分布式存储插件类型断言失败，实际类型: %T", api.distributedStorage)
			L.Push(lua.LBool(false))
			L.Push(lua.LString("分布式存储插件类型断言失败"))
		}
	} else {
		log.Printf("[Lua] 分布式存储插件未设置")
		L.Push(lua.LBool(false))
		L.Push(lua.LString("分布式存储插件未设置"))
	}

	return 2
}
