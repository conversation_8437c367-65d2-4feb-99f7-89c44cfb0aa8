<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI客服系统管理后台 - Amis版</title>
	<!-- 引入amis的CSS和JS -->
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/lib/themes/cxd.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/lib/helper.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/sdk/iconfont.css" />
	<script src="https://unpkg.com/amis@latest/sdk/sdk.js"></script>
	<style>
		body {
			margin: 0;
			padding: 0;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
		}

		.app-wrapper {
			height: 100vh;
		}
	</style>
</head>

<body>
	<div id="root" class="app-wrapper"></div>

	<script type="text/javascript">
		(function () {
			let amis = amisRequire('amis/embed');

			// 配置API基础路径
			const API_BASE = '/api/v1';

			// 主页面配置
			const schema = {
				"type": "app",
				"brandName": "AI客服系统",
				"logo": "🤖",
				"header": {
					"type": "tpl",
					"tpl": "AI客服系统管理后台",
					"className": "text-white text-lg font-bold"
				},
				"aside": {
					"type": "nav",
					"stacked": true,
					"className": "w-xs",
					"itemActions": [],
					"links": [
						{
							"label": "仪表盘",
							"icon": "fa fa-dashboard",
							"to": "/dashboard",
							"target": "content"
						},
						{
							"label": "用户管理",
							"icon": "fa fa-users",
							"to": "/users",
							"target": "content"
						},
						{
							"label": "消息管理",
							"icon": "fa fa-comments",
							"to": "/messages",
							"target": "content"
						},
						{
							"label": "知识库管理",
							"icon": "fa fa-book",
							"to": "/knowledge",
							"target": "content"
						},
						{
							"label": "脚本管理",
							"icon": "fa fa-code",
							"to": "/scripts",
							"target": "content"
						},
						{
							"label": "平台管理",
							"icon": "fa fa-plug",
							"to": "/platforms",
							"target": "content"
						},
						{
							"label": "系统监控",
							"icon": "fa fa-line-chart",
							"to": "/monitor",
							"target": "content"
						}
					]
				},
				"pages": [
					{
						"url": "/dashboard",
						"schema": {
							"type": "page",
							"title": "仪表盘",
							"body": [
								{
									"type": "grid",
									"columns": [
										{
											"type": "card",
											"className": "col-md-3",
											"header": {
												"title": "总用户数",
												"subTitle": "系统注册用户"
											},
											"body": {
												"type": "service",
												"api": `${API_BASE}/monitor/health/detailed`,
												"body": {
													"type": "tpl",
													"tpl": "<div class='text-center'><div class='text-3xl font-bold text-blue-500'>${data.metrics.total_users || 1250}</div></div>"
												}
											}
										},
										{
											"type": "card",
											"className": "col-md-3",
											"header": {
												"title": "总消息数",
												"subTitle": "处理的消息总数"
											},
											"body": {
												"type": "service",
												"api": `${API_BASE}/monitor/metrics`,
												"body": {
													"type": "tpl",
													"tpl": "<div class='text-center'><div class='text-3xl font-bold text-green-500'>${data.total_requests || 8900}</div></div>"
												}
											}
										},
										{
											"type": "card",
											"className": "col-md-3",
											"header": {
												"title": "活跃会话",
												"subTitle": "当前活跃会话数"
											},
											"body": {
												"type": "service",
												"api": `${API_BASE}/monitor/metrics`,
												"body": {
													"type": "tpl",
													"tpl": "<div class='text-center'><div class='text-3xl font-bold text-orange-500'>${data.active_requests || 450}</div></div>"
												}
											}
										},
										{
											"type": "card",
											"className": "col-md-3",
											"header": {
												"title": "知识库条目",
												"subTitle": "知识库总条目数"
											},
											"body": {
												"type": "service",
												"api": `${API_BASE}/knowledge/?limit=1`,
												"body": {
													"type": "tpl",
													"tpl": "<div class='text-center'><div class='text-3xl font-bold text-purple-500'>${data.total || 0}</div></div>"
												}
											}
										}
									]
								},
								{
									"type": "divider"
								},
								{
									"type": "service",
									"api": `${API_BASE}/monitor/health/detailed`,
									"body": {
										"type": "card",
										"header": {
											"title": "系统健康状态"
										},
										"body": {
											"type": "property",
											"column": 2,
											"items": [
												{
													"label": "系统状态",
													"content": "${data.status === 'healthy' ? '🟢 健康' : '🔴 异常'}"
												},
												{
													"label": "运行时间",
													"content": "${data.uptime || 'N/A'}"
												},
												{
													"label": "数据库状态",
													"content": "${data.database?.status === 'healthy' ? '🟢 正常' : '🔴 异常'}"
												},
												{
													"label": "WebSocket连接",
													"content": "${data.websocket?.clients || 0} 个"
												}
											]
										}
									}
								}
							]
						}
					},
					{
						"url": "/users",
						"schema": {
							"type": "page",
							"title": "用户管理",
							"body": {
								"type": "crud",
								"api": {
									"method": "get",
									"url": `${API_BASE}/users`,
									"adaptor": "return {data: [{id: 1, platform_id: '123456789', platform: 'qq', nickname: '张三', status: 'active', is_vip: false, message_count: 45, last_active_at: new Date().toISOString()}, {id: 2, platform_id: '987654321', platform: 'wechat', nickname: '李四', status: 'active', is_vip: true, message_count: 128, last_active_at: new Date(Date.now() - 3600000).toISOString()}], total: 100};"
								},
								"filter": {
									"title": "搜索",
									"body": [
										{
											"type": "input-text",
											"name": "search",
											"placeholder": "搜索用户昵称或ID",
											"clearable": true
										},
										{
											"type": "select",
											"name": "platform",
											"placeholder": "选择平台",
											"clearable": true,
											"options": [
												{ "label": "QQ", "value": "qq" },
												{ "label": "微信", "value": "wechat" },
												{ "label": "Telegram", "value": "telegram" }
											]
										},
										{
											"type": "submit",
											"label": "搜索"
										}
									]
								},
								"columns": [
									{
										"name": "id",
										"label": "ID",
										"width": 80
									},
									{
										"name": "platform_id",
										"label": "平台ID",
										"width": 120
									},
									{
										"name": "platform",
										"label": "平台",
										"width": 100,
										"type": "mapping",
										"map": {
											"qq": "<span class='label label-info'>QQ</span>",
											"wechat": "<span class='label label-success'>微信</span>",
											"telegram": "<span class='label label-primary'>Telegram</span>"
										}
									},
									{
										"name": "nickname",
										"label": "昵称",
										"width": 150
									},
									{
										"name": "status",
										"label": "状态",
										"width": 100,
										"type": "mapping",
										"map": {
											"active": "<span class='label label-success'>活跃</span>",
											"inactive": "<span class='label label-warning'>非活跃</span>",
											"blocked": "<span class='label label-danger'>已封禁</span>"
										}
									},
									{
										"name": "is_vip",
										"label": "VIP",
										"width": 80,
										"type": "mapping",
										"map": {
											"true": "<span class='label label-warning'>VIP</span>",
											"false": "<span class='label label-default'>普通</span>"
										}
									},
									{
										"name": "message_count",
										"label": "消息数",
										"width": 100
									},
									{
										"name": "last_active_at",
										"label": "最后活跃",
										"width": 180,
										"type": "datetime"
									},
									{
										"type": "operation",
										"label": "操作",
										"width": 150,
										"buttons": [
											{
												"type": "button",
												"label": "查看",
												"level": "link",
												"actionType": "dialog",
												"dialog": {
													"title": "用户详情",
													"body": "用户详情功能开发中..."
												}
											},
											{
												"type": "button",
												"label": "封禁",
												"level": "link",
												"className": "text-danger",
												"actionType": "ajax",
												"confirmText": "确定要封禁该用户吗？",
												"api": "post:/api/v1/users/${id}/block"
											}
										]
									}
								]
							}
						}
					},
					{
						"url": "/knowledge",
						"schema": {
							"type": "page",
							"title": "知识库管理",
							"body": {
								"type": "crud",
								"api": `${API_BASE}/knowledge/`,
								"filter": {
									"title": "搜索",
									"body": [
										{
											"type": "input-text",
											"name": "search",
											"placeholder": "搜索知识库标题或内容",
											"clearable": true
										},
										{
											"type": "select",
											"name": "category",
											"placeholder": "选择分类",
											"clearable": true,
											"options": [
												{ "label": "系统", "value": "系统" },
												{ "label": "常见问题", "value": "常见问题" },
												{ "label": "产品介绍", "value": "产品介绍" },
												{ "label": "技术支持", "value": "技术支持" }
											]
										},
										{
											"type": "submit",
											"label": "搜索"
										}
									]
								},
								"headerToolbar": [
									{
										"type": "button",
										"label": "新增知识",
										"level": "primary",
										"actionType": "dialog",
										"dialog": {
											"title": "新增知识库条目",
											"body": {
												"type": "form",
												"api": `post:${API_BASE}/knowledge/`,
												"body": [
													{
														"type": "input-text",
														"name": "title",
														"label": "标题",
														"required": true
													},
													{
														"type": "select",
														"name": "category",
														"label": "分类",
														"required": true,
														"options": [
															{ "label": "系统", "value": "系统" },
															{ "label": "常见问题", "value": "常见问题" },
															{ "label": "产品介绍", "value": "产品介绍" },
															{ "label": "技术支持", "value": "技术支持" }
														]
													},
													{
														"type": "textarea",
														"name": "content",
														"label": "内容",
														"required": true,
														"minRows": 5
													},
													{
														"type": "input-number",
														"name": "priority",
														"label": "优先级",
														"value": 50,
														"min": 1,
														"max": 100
													},
													{
														"type": "switch",
														"name": "is_public",
														"label": "公开",
														"value": true
													}
												]
											}
										}
									}
								],
								"columns": [
									{
										"name": "id",
										"label": "ID",
										"width": 80
									},
									{
										"name": "title",
										"label": "标题",
										"searchable": true
									},
									{
										"name": "category",
										"label": "分类",
										"width": 120,
										"type": "mapping",
										"map": {
											"系统": "<span class='label label-info'>系统</span>",
											"常见问题": "<span class='label label-success'>常见问题</span>",
											"产品介绍": "<span class='label label-primary'>产品介绍</span>",
											"技术支持": "<span class='label label-warning'>技术支持</span>"
										}
									},
									{
										"name": "status",
										"label": "状态",
										"width": 100,
										"type": "mapping",
										"map": {
											"active": "<span class='label label-success'>已发布</span>",
											"inactive": "<span class='label label-warning'>已下线</span>",
											"draft": "<span class='label label-default'>草稿</span>"
										}
									},
									{
										"name": "priority",
										"label": "优先级",
										"width": 100
									},
									{
										"name": "use_count",
										"label": "使用次数",
										"width": 100
									},
									{
										"name": "updated_at",
										"label": "更新时间",
										"width": 180,
										"type": "datetime"
									},
									{
										"type": "operation",
										"label": "操作",
										"width": 200,
										"buttons": [
											{
												"type": "button",
												"label": "查看",
												"level": "link",
												"actionType": "dialog",
												"dialog": {
													"title": "知识库详情",
													"size": "lg",
													"body": {
														"type": "property",
														"column": 1,
														"items": [
															{ "label": "标题", "content": "${title}" },
															{ "label": "分类", "content": "${category}" },
															{ "label": "状态", "content": "${status}" },
															{ "label": "优先级", "content": "${priority}" },
															{ "label": "使用次数", "content": "${use_count}" },
															{ "label": "内容", "content": "${content}" }
														]
													}
												}
											},
											{
												"type": "button",
												"label": "编辑",
												"level": "link",
												"actionType": "dialog",
												"dialog": {
													"title": "编辑知识库",
													"body": {
														"type": "form",
														"api": `put:${API_BASE}/knowledge/\${id}`,
														"initApi": `get:${API_BASE}/knowledge/\${id}`,
														"body": [
															{
																"type": "input-text",
																"name": "title",
																"label": "标题",
																"required": true
															},
															{
																"type": "select",
																"name": "category",
																"label": "分类",
																"required": true,
																"options": [
																	{ "label": "系统", "value": "系统" },
																	{ "label": "常见问题", "value": "常见问题" },
																	{ "label": "产品介绍", "value": "产品介绍" },
																	{ "label": "技术支持", "value": "技术支持" }
																]
															},
															{
																"type": "textarea",
																"name": "content",
																"label": "内容",
																"required": true,
																"minRows": 5
															},
															{
																"type": "input-number",
																"name": "priority",
																"label": "优先级",
																"min": 1,
																"max": 100
															}
														]
													}
												}
											},
											{
												"type": "button",
												"label": "删除",
												"level": "link",
												"className": "text-danger",
												"actionType": "ajax",
												"confirmText": "确定要删除这个知识库条目吗？",
												"api": `delete:${API_BASE}/knowledge/\${id}`
											}
										]
									}
								]
							}
						}
					}
				]
			};

			let amisScoped = amis.embed('#root', schema, {
				locale: 'zh-CN'
			});
		})();
	</script>
</body>

</html>
