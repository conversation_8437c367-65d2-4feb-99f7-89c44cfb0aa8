{"url": "/settings", "schema": {"type": "page", "title": "系统设置", "body": [{"type": "tabs", "tabs": [{"title": "基本设置", "body": {"type": "form", "api": "put:/api/v1/settings/basic", "initApi": "get:/api/v1/settings/basic", "body": [{"type": "input-text", "name": "system_name", "label": "系统名称", "required": true, "placeholder": "AI客服系统"}, {"type": "textarea", "name": "system_description", "label": "系统描述", "minRows": 3, "placeholder": "智能客服系统，支持多平台接入"}, {"type": "input-text", "name": "admin_email", "label": "管理员邮箱", "format": "email", "placeholder": "<EMAIL>"}, {"type": "input-text", "name": "contact_phone", "label": "联系电话", "placeholder": "客服联系电话"}, {"type": "input-number", "name": "max_concurrent_sessions", "label": "最大并发会话数", "min": 1, "max": 10000, "value": 1000}, {"type": "input-number", "name": "session_timeout", "label": "会话超时时间(分钟)", "min": 1, "max": 1440, "value": 30}, {"type": "switch", "name": "enable_debug_mode", "label": "启用调试模式"}, {"type": "switch", "name": "enable_logging", "label": "启用日志记录", "value": true}]}}, {"title": "AI配置", "body": {"type": "form", "api": "put:/api/v1/settings/ai", "initApi": "get:/api/v1/settings/ai", "body": [{"type": "select", "name": "default_ai_provider", "label": "默认AI提供商", "required": true, "options": [{"label": "OpenAI", "value": "openai"}, {"label": "硅基流动", "value": "siliconflow"}, {"label": "本地模型", "value": "local"}]}, {"type": "input-password", "name": "openai_api_key", "label": "OpenAI API Key", "placeholder": "sk-..."}, {"type": "input-url", "name": "openai_base_url", "label": "OpenAI Base URL", "placeholder": "https://api.openai.com/v1"}, {"type": "input-text", "name": "default_model", "label": "默认模型", "placeholder": "gpt-3.5-turbo"}, {"type": "input-number", "name": "max_tokens", "label": "最大Token数", "min": 1, "max": 32000, "value": 2000}, {"type": "input-number", "name": "temperature", "label": "温度参数", "min": 0, "max": 2, "step": 0.1, "value": 0.7}, {"type": "textarea", "name": "system_prompt", "label": "系统提示词", "minRows": 5, "placeholder": "你是一个专业的客服助手..."}]}}, {"title": "数据库配置", "body": {"type": "form", "api": "put:/api/v1/settings/database", "initApi": "get:/api/v1/settings/database", "body": [{"type": "select", "name": "database_type", "label": "数据库类型", "required": true, "options": [{"label": "SQLite", "value": "sqlite"}, {"label": "MySQL", "value": "mysql"}, {"label": "PostgreSQL", "value": "postgresql"}]}, {"type": "input-text", "name": "database_host", "label": "数据库主机", "placeholder": "localhost", "visibleOn": "${database_type !== 'sqlite'}"}, {"type": "input-number", "name": "database_port", "label": "数据库端口", "placeholder": "3306", "visibleOn": "${database_type !== 'sqlite'}"}, {"type": "input-text", "name": "database_name", "label": "数据库名称", "required": true, "placeholder": "aike_db"}, {"type": "input-text", "name": "database_username", "label": "用户名", "visibleOn": "${database_type !== 'sqlite'}"}, {"type": "input-password", "name": "database_password", "label": "密码", "visibleOn": "${database_type !== 'sqlite'}"}, {"type": "input-number", "name": "max_connections", "label": "最大连接数", "min": 1, "max": 1000, "value": 100}, {"type": "input-number", "name": "connection_timeout", "label": "连接超时(秒)", "min": 1, "max": 300, "value": 30}, {"type": "switch", "name": "enable_auto_backup", "label": "启用自动备份", "value": true}, {"type": "input-number", "name": "backup_interval_hours", "label": "备份间隔(小时)", "min": 1, "max": 168, "value": 24, "visibleOn": "${enable_auto_backup}"}]}}, {"title": "安全设置", "body": {"type": "form", "api": "put:/api/v1/settings/security", "initApi": "get:/api/v1/settings/security", "body": [{"type": "switch", "name": "enable_rate_limiting", "label": "启用频率限制", "value": true}, {"type": "input-number", "name": "rate_limit_requests", "label": "每分钟最大请求数", "min": 1, "max": 10000, "value": 100, "visibleOn": "${enable_rate_limiting}"}, {"type": "switch", "name": "enable_ip_whitelist", "label": "启用IP白名单"}, {"type": "textarea", "name": "ip_whitelist", "label": "IP白名单", "placeholder": "每行一个IP地址或CIDR", "minRows": 3, "visibleOn": "${enable_ip_whitelist}"}, {"type": "switch", "name": "enable_api_key_auth", "label": "启用API密钥认证"}, {"type": "input-password", "name": "api_key", "label": "API密钥", "visibleOn": "${enable_api_key_auth}"}, {"type": "input-number", "name": "session_expire_hours", "label": "会话过期时间(小时)", "min": 1, "max": 720, "value": 24}, {"type": "switch", "name": "enable_https_only", "label": "仅允许HTTPS访问"}]}}, {"title": "系统维护", "body": [{"type": "card", "header": {"title": "数据清理"}, "body": {"type": "button-toolbar", "buttons": [{"type": "button", "label": "清理过期会话", "level": "warning", "actionType": "ajax", "api": "post:/api/v1/maintenance/clean-sessions", "confirmText": "确定要清理过期会话吗？"}, {"type": "button", "label": "清理旧日志", "level": "warning", "actionType": "ajax", "api": "post:/api/v1/maintenance/clean-logs", "confirmText": "确定要清理30天前的日志吗？"}, {"type": "button", "label": "清理缓存", "level": "info", "actionType": "ajax", "api": "post:/api/v1/maintenance/clear-cache", "confirmText": "确定要清理系统缓存吗？"}]}}, {"type": "divider"}, {"type": "card", "header": {"title": "数据备份"}, "body": {"type": "button-toolbar", "buttons": [{"type": "button", "label": "立即备份", "level": "primary", "actionType": "ajax", "api": "post:/api/v1/maintenance/backup", "confirmText": "确定要立即备份数据库吗？"}, {"type": "button", "label": "下载备份", "level": "success", "actionType": "download", "api": "/api/v1/maintenance/download-backup"}, {"type": "button", "label": "恢复数据", "level": "danger", "actionType": "dialog", "dialog": {"title": "恢复数据", "body": {"type": "form", "api": "post:/api/v1/maintenance/restore", "body": [{"type": "input-file", "name": "backup_file", "label": "备份文件", "required": true, "accept": ".sql,.db"}]}}}]}}, {"type": "divider"}, {"type": "card", "header": {"title": "系统控制"}, "body": {"type": "button-toolbar", "buttons": [{"type": "button", "label": "重启系统", "level": "danger", "actionType": "ajax", "api": "post:/api/v1/maintenance/restart", "confirmText": "确定要重启系统吗？这将中断所有连接！"}, {"type": "button", "label": "停止系统", "level": "danger", "actionType": "ajax", "api": "post:/api/v1/maintenance/shutdown", "confirmText": "确定要停止系统吗？这将关闭所有服务！"}]}}]}]}]}}