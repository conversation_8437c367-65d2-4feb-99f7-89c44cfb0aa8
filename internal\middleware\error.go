package middleware

import (
	"fmt"
	"log"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ErrorType 错误类型
// 思路：定义不同类型的错误，便于分类处理
type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "validation"    // 验证错误
	ErrorTypeNotFound     ErrorType = "not_found"     // 资源不存在
	ErrorTypeUnauthorized ErrorType = "unauthorized"  // 未授权
	ErrorTypeForbidden    ErrorType = "forbidden"     // 禁止访问
	ErrorTypeInternal     ErrorType = "internal"      // 内部错误
	ErrorTypeExternal     ErrorType = "external"      // 外部服务错误
	ErrorTypeRateLimit    ErrorType = "rate_limit"    // 限流错误
	ErrorTypeTimeout      ErrorType = "timeout"       // 超时错误
)

// AppError 应用错误结构体
// 思路：统一的错误格式，包含错误码、消息和详细信息
type AppError struct {
	Type       ErrorType              `json:"type"`        // 错误类型
	Code       string                 `json:"code"`        // 错误码
	Message    string                 `json:"message"`     // 错误消息
	Details    string                 `json:"details"`     // 详细信息
	StatusCode int                    `json:"status_code"` // HTTP状态码
	Timestamp  time.Time              `json:"timestamp"`   // 时间戳
	RequestID  string                 `json:"request_id"`  // 请求ID
	Path       string                 `json:"path"`        // 请求路径
	Method     string                 `json:"method"`      // 请求方法
	UserID     string                 `json:"user_id"`     // 用户ID
	Platform   string                 `json:"platform"`    // 平台
	Extra      map[string]interface{} `json:"extra"`       // 额外信息
	Stack      string                 `json:"stack"`       // 堆栈信息（仅开发环境）
}

// Error 实现error接口
func (e *AppError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
}

// NewAppError 创建应用错误
// 思路：便捷的错误创建函数
// 使用例子：err := NewAppError(ErrorTypeValidation, "INVALID_PARAM", "参数无效", "用户ID不能为空")
func NewAppError(errorType ErrorType, code, message, details string) *AppError {
	return &AppError{
		Type:       errorType,
		Code:       code,
		Message:    message,
		Details:    details,
		StatusCode: getStatusCodeByType(errorType),
		Timestamp:  time.Now(),
	}
}

// getStatusCodeByType 根据错误类型获取HTTP状态码
func getStatusCodeByType(errorType ErrorType) int {
	switch errorType {
	case ErrorTypeValidation:
		return http.StatusBadRequest
	case ErrorTypeNotFound:
		return http.StatusNotFound
	case ErrorTypeUnauthorized:
		return http.StatusUnauthorized
	case ErrorTypeForbidden:
		return http.StatusForbidden
	case ErrorTypeRateLimit:
		return http.StatusTooManyRequests
	case ErrorTypeTimeout:
		return http.StatusRequestTimeout
	case ErrorTypeExternal:
		return http.StatusBadGateway
	default:
		return http.StatusInternalServerError
	}
}

// ErrorHandlerMiddleware 错误处理中间件
// 思路：统一处理所有错误，提供一致的错误响应格式
// 使用例子：router.Use(middleware.ErrorHandlerMiddleware())
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 处理panic
				handlePanic(c, err)
			}
		}()

		c.Next()

		// 处理错误
		if len(c.Errors) > 0 {
			handleErrors(c)
		}
	}
}

// handlePanic 处理panic
// 思路：捕获panic并转换为错误响应
func handlePanic(c *gin.Context, err interface{}) {
	stack := string(debug.Stack())
	
	appErr := &AppError{
		Type:       ErrorTypeInternal,
		Code:       "INTERNAL_PANIC",
		Message:    "服务器内部错误",
		Details:    fmt.Sprintf("Panic: %v", err),
		StatusCode: http.StatusInternalServerError,
		Timestamp:  time.Now(),
		RequestID:  getRequestID(c),
		Path:       c.Request.URL.Path,
		Method:     c.Request.Method,
		UserID:     getUserID(c),
		Platform:   getPlatform(c),
		Stack:      stack,
	}

	// 记录错误日志
	logError(appErr)

	// 返回错误响应
	sendErrorResponse(c, appErr)
}

// handleErrors 处理错误列表
// 思路：处理gin.Context中的错误
func handleErrors(c *gin.Context) {
	lastError := c.Errors.Last()
	
	var appErr *AppError
	
	// 检查是否为AppError
	if ae, ok := lastError.Err.(*AppError); ok {
		appErr = ae
	} else {
		// 转换为AppError
		appErr = convertToAppError(lastError.Err, c)
	}

	// 填充请求信息
	fillRequestInfo(appErr, c)

	// 记录错误日志
	logError(appErr)

	// 返回错误响应
	sendErrorResponse(c, appErr)
}

// convertToAppError 转换为AppError
// 思路：将普通错误转换为AppError
func convertToAppError(err error, c *gin.Context) *AppError {
	message := err.Error()
	
	// 根据错误消息判断错误类型
	var errorType ErrorType
	var code string
	
	switch {
	case strings.Contains(message, "not found"):
		errorType = ErrorTypeNotFound
		code = "NOT_FOUND"
	case strings.Contains(message, "unauthorized"):
		errorType = ErrorTypeUnauthorized
		code = "UNAUTHORIZED"
	case strings.Contains(message, "forbidden"):
		errorType = ErrorTypeForbidden
		code = "FORBIDDEN"
	case strings.Contains(message, "validation"):
		errorType = ErrorTypeValidation
		code = "VALIDATION_ERROR"
	case strings.Contains(message, "timeout"):
		errorType = ErrorTypeTimeout
		code = "TIMEOUT"
	default:
		errorType = ErrorTypeInternal
		code = "INTERNAL_ERROR"
	}

	return &AppError{
		Type:       errorType,
		Code:       code,
		Message:    "请求处理失败",
		Details:    message,
		StatusCode: getStatusCodeByType(errorType),
		Timestamp:  time.Now(),
	}
}

// fillRequestInfo 填充请求信息
// 思路：将请求上下文信息添加到错误中
func fillRequestInfo(appErr *AppError, c *gin.Context) {
	appErr.RequestID = getRequestID(c)
	appErr.Path = c.Request.URL.Path
	appErr.Method = c.Request.Method
	appErr.UserID = getUserID(c)
	appErr.Platform = getPlatform(c)

	// 添加额外信息
	appErr.Extra = map[string]interface{}{
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}

	// 开发环境添加堆栈信息
	if gin.Mode() == gin.DebugMode {
		appErr.Stack = string(debug.Stack())
	}
}

// logError 记录错误日志
// 思路：将错误信息记录到日志中
func logError(appErr *AppError) {
	logLevel := "ERROR"
	if appErr.Type == ErrorTypeValidation {
		logLevel = "WARN"
	}

	logMsg := fmt.Sprintf("[%s] %s - %s | %s %s | User: %s | Platform: %s | Details: %s",
		logLevel,
		appErr.Code,
		appErr.Message,
		appErr.Method,
		appErr.Path,
		appErr.UserID,
		appErr.Platform,
		appErr.Details,
	)

	if appErr.Stack != "" {
		logMsg += fmt.Sprintf("\nStack: %s", appErr.Stack)
	}

	log.Println(logMsg)
}

// sendErrorResponse 发送错误响应
// 思路：根据环境返回不同详细程度的错误信息
func sendErrorResponse(c *gin.Context, appErr *AppError) {
	// 生产环境隐藏敏感信息
	response := gin.H{
		"error":      true,
		"code":       appErr.Code,
		"message":    appErr.Message,
		"timestamp":  appErr.Timestamp,
		"request_id": appErr.RequestID,
	}

	// 开发环境返回详细信息
	if gin.Mode() == gin.DebugMode {
		response["details"] = appErr.Details
		response["type"] = appErr.Type
		response["path"] = appErr.Path
		response["method"] = appErr.Method
		if appErr.Stack != "" {
			response["stack"] = appErr.Stack
		}
	}

	c.JSON(appErr.StatusCode, response)
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// AbortWithError 中止请求并返回错误
// 思路：便捷的错误处理函数
// 使用例子：middleware.AbortWithError(c, ErrorTypeValidation, "INVALID_PARAM", "参数无效", "用户ID不能为空")
func AbortWithError(c *gin.Context, errorType ErrorType, code, message, details string) {
	appErr := NewAppError(errorType, code, message, details)
	c.Error(appErr)
	c.Abort()
}

// AbortWithAppError 中止请求并返回AppError
// 思路：使用已创建的AppError中止请求
// 使用例子：middleware.AbortWithAppError(c, appErr)
func AbortWithAppError(c *gin.Context, appErr *AppError) {
	c.Error(appErr)
	c.Abort()
}

// 预定义的常用错误

// ErrInvalidParam 无效参数错误
func ErrInvalidParam(param, reason string) *AppError {
	return NewAppError(
		ErrorTypeValidation,
		"INVALID_PARAM",
		"参数验证失败",
		fmt.Sprintf("参数 %s 无效: %s", param, reason),
	)
}

// ErrNotFound 资源不存在错误
func ErrNotFound(resource string) *AppError {
	return NewAppError(
		ErrorTypeNotFound,
		"RESOURCE_NOT_FOUND",
		"资源不存在",
		fmt.Sprintf("%s 不存在", resource),
	)
}

// ErrUnauthorized 未授权错误
func ErrUnauthorized(reason string) *AppError {
	return NewAppError(
		ErrorTypeUnauthorized,
		"UNAUTHORIZED",
		"未授权访问",
		reason,
	)
}

// ErrForbidden 禁止访问错误
func ErrForbidden(reason string) *AppError {
	return NewAppError(
		ErrorTypeForbidden,
		"FORBIDDEN",
		"禁止访问",
		reason,
	)
}

// ErrInternalServer 内部服务器错误
func ErrInternalServer(details string) *AppError {
	return NewAppError(
		ErrorTypeInternal,
		"INTERNAL_SERVER_ERROR",
		"服务器内部错误",
		details,
	)
}

// ErrExternalService 外部服务错误
func ErrExternalService(service, details string) *AppError {
	return NewAppError(
		ErrorTypeExternal,
		"EXTERNAL_SERVICE_ERROR",
		"外部服务错误",
		fmt.Sprintf("%s: %s", service, details),
	)
}

// ErrRateLimit 限流错误
func ErrRateLimit(limit string) *AppError {
	return NewAppError(
		ErrorTypeRateLimit,
		"RATE_LIMIT_EXCEEDED",
		"请求频率过高",
		fmt.Sprintf("超过限制: %s", limit),
	)
}
