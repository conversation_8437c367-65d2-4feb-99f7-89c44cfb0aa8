# QQ平台适配器使用指南

## 概述

QQ平台适配器基于NapCat API实现，支持QQ消息的收发和事件处理。

## 前置条件

1. **安装NapCat**
   - 下载并安装NapCat
   - 配置QQ机器人账号
   - 启动NapCat服务

2. **配置环境变量**
   ```bash
   # 启用QQ平台
   QQ_ENABLED=true
   
   # NapCat API地址
   QQ_BASE_URL=http://localhost:3000
   
   # 访问令牌（可选）
   QQ_TOKEN=your_access_token_here
   ```

## NapCat配置

### 1. NapCat配置文件示例

```json
{
  "http": {
    "enable": true,
    "host": "0.0.0.0",
    "port": 3000,
    "secret": "",
    "enableHeart": true,
    "enablePost": true,
    "postUrls": [
      "http://localhost:8080/api/v1/platforms/qq/webhook"
    ]
  },
  "ws": {
    "enable": false
  },
  "reverseWs": {
    "enable": false
  },
  "GroupLocalTime": 0,
  "debug": false,
  "heartInterval": 30000,
  "messagePostFormat": "array",
  "enableLocalFile2Url": true,
  "musicSignUrl": "",
  "reportSelfMessage": false,
  "token": "your_access_token_here"
}
```

### 2. 启动NapCat

```bash
# Windows
napcat.exe

# Linux
./napcat

# Docker
docker run -d --name napcat \
  -p 3000:3000 \
  -v ./config:/app/config \
  napcat/napcat:latest
```

## 使用方法

### 1. 启动客服系统

```bash
# 设置环境变量
export QQ_ENABLED=true
export QQ_BASE_URL=http://localhost:3000
export QQ_TOKEN=your_token

# 启动服务
go run cmd/server/main.go
```

### 2. 检查适配器状态

```bash
# 获取平台列表
curl http://localhost:8080/api/v1/platforms/

# 获取详细统计
curl http://localhost:8080/api/v1/platforms/stats
```

### 3. 发送消息

```bash
# 发送私聊消息
curl -X POST http://localhost:8080/api/v1/platforms/qq/send \
  -H "Content-Type: application/json" \
  -d '{
    "platform_user_id": "123456789",
    "content": "你好，这是来自客服系统的消息",
    "message_type": "text"
  }'

# 发送群消息
curl -X POST http://localhost:8080/api/v1/platforms/qq/send \
  -H "Content-Type: application/json" \
  -d '{
    "platform_user_id": "123456789",
    "platform_chat_id": "987654321",
    "content": "群消息测试",
    "message_type": "text"
  }'
```

## 消息处理流程

### 1. 接收消息流程

```
QQ用户发送消息 
    ↓
NapCat接收并转发
    ↓
客服系统WebHook接收
    ↓
QQ适配器解析消息
    ↓
转换为统一消息格式
    ↓
调用消息处理器
    ↓
搜索知识库/调用OpenAI
    ↓
生成回复并发送
```

### 2. 消息格式转换

**NapCat原始格式:**
```json
{
  "post_type": "message",
  "message_type": "private",
  "time": 1640995200,
  "self_id": 123456,
  "user_id": 789012,
  "message": "你好",
  "raw_message": "你好",
  "message_id": 123,
  "sender": {
    "user_id": 789012,
    "nickname": "用户昵称",
    "card": "",
    "role": "member"
  }
}
```

**统一消息格式:**
```json
{
  "id": "qq_123",
  "platform": "qq",
  "timestamp": "2021-12-31T16:00:00Z",
  "from": {
    "platform_user_id": "789012",
    "platform": "qq",
    "nickname": "用户昵称",
    "is_bot": false
  },
  "content": "你好",
  "message_type": "text"
}
```

## 支持的消息类型

### 1. 文本消息
- 普通文本
- 表情符号
- @提及

### 2. 富媒体消息（计划支持）
- 图片消息
- 语音消息
- 文件消息
- 位置消息

## 错误处理

### 1. 常见错误

| 错误类型 | 原因 | 解决方案 |
|---------|------|----------|
| 连接失败 | NapCat未启动 | 检查NapCat服务状态 |
| 认证失败 | Token错误 | 检查配置的Token |
| 发送失败 | QQ号不存在 | 验证目标QQ号 |
| 权限不足 | 机器人被限制 | 检查QQ账号状态 |

### 2. 日志监控

```bash
# 查看适配器日志
tail -f logs/qq_adapter.log

# 查看NapCat日志
tail -f napcat/logs/napcat.log
```

## 性能优化

### 1. 连接池配置
- HTTP客户端超时设置
- 连接复用
- 请求重试机制

### 2. 消息队列
- 异步消息处理
- 消息去重
- 流量控制

## 安全考虑

### 1. 访问控制
- Token认证
- IP白名单
- 请求频率限制

### 2. 数据保护
- 消息内容加密
- 敏感信息脱敏
- 日志安全

## 故障排除

### 1. 检查清单

- [ ] NapCat服务是否正常运行
- [ ] 网络连接是否正常
- [ ] 配置参数是否正确
- [ ] QQ账号是否正常
- [ ] WebHook地址是否可访问

### 2. 调试模式

```bash
# 启用调试模式
export GIN_MODE=debug
export LOG_LEVEL=debug

# 启动服务
go run cmd/server/main.go
```

## 扩展开发

### 1. 自定义消息处理

```go
// 实现自定义消息处理器
type CustomMessageHandler struct {
    // 自定义字段
}

func (h *CustomMessageHandler) HandleIncomingMessage(ctx context.Context, message *adapters.IncomingMessage) error {
    // 自定义处理逻辑
    return nil
}
```

### 2. 添加新的消息类型

```go
// 扩展消息类型常量
const (
    MessageTypeCustom = "custom"
)

// 处理自定义消息类型
func (a *QQAdapter) handleCustomMessage(message *NapCatMessage) error {
    // 处理逻辑
    return nil
}
```

## 最佳实践

1. **消息去重**: 使用消息ID避免重复处理
2. **异步处理**: 使用goroutine处理耗时操作
3. **错误重试**: 实现指数退避重试机制
4. **监控告警**: 设置关键指标监控
5. **日志记录**: 记录详细的操作日志

## 相关链接

- [NapCat官方文档](https://napcat.napneko.com/)
- [OneBot标准](https://onebot.dev/)
- [QQ机器人开发指南](https://bot.q.qq.com/wiki/)
