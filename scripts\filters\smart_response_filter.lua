-- 智能响应过滤器
-- 思路：根据消息内容和上下文智能判断是否需要响应
-- 类型：message_filter
-- 优先级：100

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return true -- 空消息直接通过，由其他过滤器处理
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return true
end

local content = message.content:lower()
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

log("info", "智能过滤器检查消息: " .. content .. " 来自用户: " .. user_id)

-- 1. 黑名单用户检查
local blacklist_users = { "111111111", "222222222", "spam_user" }
for i, black_user in ipairs(blacklist_users) do
    if user_id == black_user then
        log("warn", "用户 " .. user_id .. " 在黑名单中，拦截消息")
        return false
    end
end

-- 2. 白名单用户检查（管理员等）
local whitelist_users = { "123456789", "987654321", "admin_user" }
for i, white_user in ipairs(whitelist_users) do
    if user_id == white_user then
        log("info", "用户 " .. user_id .. " 在白名单中，直接通过")
        return true
    end
end

-- 3. 获取当前时间信息
local current_hour = tonumber(os.date("%H"))
local current_weekday = tonumber(os.date("%w")) -- 0=周日, 1=周一, ..., 6=周六
local is_workday = current_weekday >= 1 and current_weekday <= 5
local is_workhour = current_hour >= 9 and current_hour < 18

-- log("info", "当前时间: " .. current_hour .. "点, 星期" .. current_weekday ..      ", 工作日: " .. tostring(is_workday) .. ", 工作时间: " .. tostring(is_workhour))

-- 4. 群消息特殊处理
local group_id = ""
if message.metadata and message.metadata.group_id then
    group_id = message.metadata.group_id
end

if group_id ~= "" and group_id ~= "0" then
    log("info", "处理群消息，群ID: " .. group_id)

    -- 群消息必须@机器人或包含机器人名称
    local bot_names = { "ai客服", "机器人", "小助手", "@" }
    local mention_found = false

    for i, bot_name in ipairs(bot_names) do
        if content:find(bot_name) then
            mention_found = true
            log("info", "群消息包含机器人名称: " .. bot_name)
            break
        end
    end

    if not mention_found then
        log("info", "群消息未@机器人，忽略")
        return false
    end

    -- 群黑名单检查
    local blacklist_groups = { "999999", "spam_group" }
    for i, black_group in ipairs(blacklist_groups) do
        if group_id == black_group then
            log("warn", "群 " .. group_id .. " 在黑名单中，拦截消息")
            return false
        end
    end
end

-- 5. 内容过滤
-- 过滤过短的消息
if string.len(content) < 3 then
    log("info", "消息过短，忽略: " .. content)
    return false
end

-- 过滤纯表情和符号
local emoji_pattern = "^[😀-🙏]+$"
local symbol_pattern = "^[。，！？]+$"
local number_pattern = "^[0-9]+$"

if content:match("^[%.%,!%?]+$") or content:match("^%d+$") then
    log("info", "消息为纯符号或数字，忽略: " .. content)
    return false
end

-- 6. 关键词检查
local response_keywords = {
    "客服", "帮助", "help", "问题", "咨询", "售后", "投诉", "bug", "故障"
}

local keyword_found = false
for i, keyword in ipairs(response_keywords) do
    if content:find(keyword) then
        keyword_found = true
        log("info", "发现响应关键词: " .. keyword)
        break
    end
end

-- 7. 特殊前缀检查（命令式消息）
local command_prefixes = { "/", "!", "@", "？", "?" }
local prefix_found = false

for i, prefix in ipairs(command_prefixes) do
    if content:sub(1, 1) == prefix then
        prefix_found = true
        log("info", "发现命令前缀: " .. prefix)
        break
    end
end

-- 8. 忽略关键词检查（闲聊内容）
local ignore_keywords = {
    "哈哈", "呵呵", "666", "牛逼", "厉害", "赞", "👍", "好的", "嗯", "哦"
}

local should_ignore = false
for i, ignore_word in ipairs(ignore_keywords) do
    if content:find(ignore_word) then
        should_ignore = true
        log("info", "发现忽略关键词: " .. ignore_word)
        break
    end
end

-- 9. 综合判断逻辑
local should_respond = false
local reason = ""

if should_ignore then
    should_respond = false
    reason = "包含忽略关键词"
elseif keyword_found or prefix_found then
    should_respond = true
    reason = "包含响应关键词或命令前缀"
elseif string.len(content) >= 10 and (content:find("？") or content:find("?")) then
    -- 长消息且包含问号，可能是问题
    should_respond = true
    reason = "长消息包含问号，可能是问题"
elseif is_workday and is_workhour then
    -- 工作时间内，降低响应阈值
    if string.len(content) >= 5 then
        should_respond = true
        reason = "工作时间内，消息长度足够"
    end
else
    -- 非工作时间，检查紧急关键词
    local urgent_keywords = { "紧急", "urgent", "故障", "bug", "问题" }
    for i, urgent_word in ipairs(urgent_keywords) do
        if content:find(urgent_word) then
            should_respond = true
            reason = "非工作时间紧急关键词: " .. urgent_word
            break
        end
    end
end

-- 10. 频率限制检查（简单实现）
-- 这里可以调用外部API或使用全局变量来跟踪频率
-- 暂时跳过，由Go层面的过滤器处理

-- 11. 记录决策结果
if should_respond then
    log("info", "智能过滤器决定响应消息: " .. reason)

    -- 设置上下文变量供后续脚本使用
    set_var("filter_reason", reason)
    set_var("response_approved", "true")
    set_var("filter_score", "80")
else
    log("info", "智能过滤器决定忽略消息: " .. (reason ~= "" and reason or "未满足响应条件"))

    set_var("filter_reason", reason ~= "" and reason or "未满足响应条件")
    set_var("response_approved", "false")
    set_var("filter_score", "20")
end

-- 返回过滤结果
return should_respond
