package main

import (
	"context"
	"fmt"
	"log"

	"github.com/sashabaranov/go-openai"
)

// 测试硅基流动 API 连接
// 思路：验证硅基流动的 OpenAI 兼容接口是否正常工作
// 使用例子：go run test_siliconflow.go
func main() {
	// 硅基流动配置
	apiKey := "your_siliconflow_api_key_here"  // 请替换为实际的 API Key
	baseURL := "https://api.siliconflow.cn/v1"
	model := "Qwen/Qwen2.5-72B-Instruct"

	if apiKey == "your_siliconflow_api_key_here" {
		fmt.Println("请先在代码中设置正确的 API Key")
		fmt.Println("1. 访问 https://cloud.siliconflow.cn/account/ak 获取 API Key")
		fmt.Println("2. 将 apiKey 变量替换为实际的密钥")
		return
	}

	// 创建客户端配置
	config := openai.DefaultConfig(apiKey)
	config.BaseURL = baseURL

	// 创建客户端
	client := openai.NewClientWithConfig(config)

	// 测试简单对话
	fmt.Printf("测试硅基流动 API 连接...\n")
	fmt.Printf("Base URL: %s\n", baseURL)
	fmt.Printf("Model: %s\n", model)
	fmt.Println("---")

	// 构建请求
	req := openai.ChatCompletionRequest{
		Model: model,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: "你是一个专业的客服助手，请简洁地回答用户问题。",
			},
			{
				Role:    "user",
				Content: "你好，请介绍一下你自己。",
			},
		},
		MaxTokens:   200,
		Temperature: 0.7,
	}

	// 发送请求
	ctx := context.Background()
	resp, err := client.CreateChatCompletion(ctx, req)
	if err != nil {
		log.Fatalf("API 调用失败: %v", err)
	}

	// 输出结果
	if len(resp.Choices) > 0 {
		fmt.Printf("✅ 硅基流动 API 调用成功！\n")
		fmt.Printf("模型: %s\n", resp.Model)
		fmt.Printf("回复: %s\n", resp.Choices[0].Message.Content)
		fmt.Printf("Token 使用: %d\n", resp.Usage.TotalTokens)
		fmt.Println("---")
		fmt.Println("🎉 硅基流动集成测试通过！")
		fmt.Println("现在可以在 AI 客服系统中使用硅基流动了。")
	} else {
		fmt.Println("❌ API 返回空响应")
	}
}
