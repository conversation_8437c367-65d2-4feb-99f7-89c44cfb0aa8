package plugins

import (
	"context"
	"time"

	"aike_go/internal/interfaces"
)

// Plugin 插件基础接口
// 思路：所有插件都需要实现的基础方法
type Plugin interface {
	// GetName 获取插件名称
	GetName() string

	// GetVersion 获取插件版本
	GetVersion() string

	// GetDescription 获取插件描述
	GetDescription() string

	// IsEnabled 检查插件是否启用
	IsEnabled() bool

	// Enable 启用插件
	Enable() error

	// Disable 禁用插件
	Disable() error

	// GetConfig 获取插件配置
	GetConfig() map[string]interface{}

	// SetConfig 设置插件配置
	SetConfig(config map[string]interface{}) error

	// Initialize 初始化插件
	Initialize(ctx context.Context) error

	// Cleanup 清理插件资源
	Cleanup(ctx context.Context) error
}

// ChatStoragePlugin 聊天记录存储插件接口
// 思路：专门用于存储聊天记录的插件接口
type ChatStoragePlugin interface {
	Plugin

	// StoreMessage 存储消息
	// 思路：保存单条消息到存储系统
	StoreMessage(ctx context.Context, message *ChatMessage) error

	// GetMessages 获取消息列表
	// 思路：根据条件查询消息
	GetMessages(ctx context.Context, filter *MessageFilter) ([]*ChatMessage, error)

	// GetMessageByID 根据ID获取消息
	GetMessageByID(ctx context.Context, messageID string) (*ChatMessage, error)

	// UpdateMessage 更新消息
	UpdateMessage(ctx context.Context, messageID string, updates map[string]interface{}) error

	// DeleteMessage 删除消息
	DeleteMessage(ctx context.Context, messageID string) error

	// GetConversation 获取对话历史
	// 思路：获取用户的完整对话记录
	GetConversation(ctx context.Context, userID, platform string, limit int) ([]*ChatMessage, error)

	// GetStats 获取存储统计信息
	GetStats(ctx context.Context) (*StorageStats, error)

	// Backup 备份数据
	Backup(ctx context.Context, backupPath string) error

	// Restore 恢复数据
	Restore(ctx context.Context, backupPath string) error
}

// ChatMessage 聊天消息结构
// 思路：标准化的消息格式，适用于所有存储插件
type ChatMessage struct {
	ID          string `json:"id"`
	UserID      string `json:"user_id"`
	Platform    string `json:"platform"`
	Content     string `json:"content"`
	MessageType string `json:"message_type"`
	Direction   string `json:"direction"` // incoming, outgoing
	SessionID   string `json:"session_id"`
	GroupID     string `json:"group_id"`    // 群组ID（如果是群消息）
	ReplyToID   string `json:"reply_to_id"` // 回复的消息ID

	// 用户信息
	// 思路：存储发送者的昵称和头像，便于显示和用户管理
	UserNickname string `json:"user_nickname"` // 发送者昵称
	UserAvatar   string `json:"user_avatar"`   // 发送者头像

	Metadata  map[string]interface{} `json:"metadata"` // 额外元数据
	Timestamp time.Time              `json:"timestamp"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// MessageFilter 消息过滤条件
// 思路：用于查询消息的过滤条件
type MessageFilter struct {
	UserID      string    `json:"user_id"`
	Platform    string    `json:"platform"`
	SessionID   string    `json:"session_id"`
	GroupID     string    `json:"group_id"`
	MessageType string    `json:"message_type"`
	Direction   string    `json:"direction"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Keyword     string    `json:"keyword"` // 内容关键词搜索
	Limit       int       `json:"limit"`
	Offset      int       `json:"offset"`
}

// StorageStats 存储统计信息
// 思路：提供存储系统的统计数据
type StorageStats struct {
	TotalMessages  int64     `json:"total_messages"`
	TotalUsers     int64     `json:"total_users"`
	TotalSessions  int64     `json:"total_sessions"`
	StorageSize    int64     `json:"storage_size"` // 字节
	OldestMessage  time.Time `json:"oldest_message"`
	NewestMessage  time.Time `json:"newest_message"`
	MessagesByDay  []int64   `json:"messages_by_day"` // 最近7天的消息数量
	TopPlatforms   []string  `json:"top_platforms"`   // 最活跃的平台
	LastBackupTime time.Time `json:"last_backup_time"`
}

// PluginManager 插件管理器接口
// 思路：管理所有插件的生命周期
type PluginManager interface {
	// RegisterPlugin 注册插件
	RegisterPlugin(plugin Plugin) error

	// UnregisterPlugin 注销插件
	UnregisterPlugin(name string) error

	// GetPlugin 获取插件
	GetPlugin(name string) (Plugin, error)

	// GetAllPlugins 获取所有插件
	GetAllPlugins() []Plugin

	// GetEnabledPlugins 获取启用的插件
	GetEnabledPlugins() []Plugin

	// EnablePlugin 启用插件
	EnablePlugin(name string) error

	// DisablePlugin 禁用插件
	DisablePlugin(name string) error

	// GetPluginConfig 获取插件配置
	GetPluginConfig(name string) (map[string]interface{}, error)

	// SetPluginConfig 设置插件配置
	SetPluginConfig(name string, config map[string]interface{}) error

	// ReloadPlugin 重新加载插件
	ReloadPlugin(name string) error

	// GetPluginStats 获取插件统计信息
	GetPluginStats() map[string]interface{}
}

// PluginConfig 插件配置结构
// 思路：标准化的插件配置格式
type PluginConfig struct {
	Name         string                 `yaml:"name" json:"name"`
	Version      string                 `yaml:"version" json:"version"`
	Description  string                 `yaml:"description" json:"description"`
	Enabled      bool                   `yaml:"enabled" json:"enabled"`
	Priority     int                    `yaml:"priority" json:"priority"`
	Config       map[string]interface{} `yaml:"config" json:"config"`
	Dependencies []string               `yaml:"dependencies" json:"dependencies"`
	Author       string                 `yaml:"author" json:"author"`
	License      string                 `yaml:"license" json:"license"`
	Homepage     string                 `yaml:"homepage" json:"homepage"`
}

// PluginEvent 插件事件
// 思路：插件系统的事件通知
type PluginEvent struct {
	Type      string                 `json:"type"`   // enabled, disabled, configured, error
	Plugin    string                 `json:"plugin"` // 插件名称
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`  // 事件数据
	Error     string                 `json:"error"` // 错误信息（如果有）
}

// PluginEventHandler 插件事件处理器
// 思路：处理插件事件的回调函数
type PluginEventHandler func(event *PluginEvent)

// ConvertIncomingMessage 转换消息格式
// 思路：将系统内部消息格式转换为插件标准格式
func ConvertIncomingMessage(msg *interfaces.IncomingMessage) *ChatMessage {
	chatMsg := &ChatMessage{
		ID:          msg.ID,
		UserID:      msg.From.PlatformUserID,
		Platform:    msg.Platform,
		Content:     msg.Content,
		MessageType: msg.MessageType,
		Direction:   "incoming",
		SessionID:   "", // 需要从上下文获取
		GroupID:     "", // 需要从元数据获取
		Metadata:    make(map[string]interface{}),
		Timestamp:   msg.Timestamp,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 复制元数据
	if msg.Metadata != nil {
		for k, v := range msg.Metadata {
			chatMsg.Metadata[k] = v
		}

		// 提取群组ID
		if groupID, ok := msg.Metadata["group_id"]; ok {
			chatMsg.GroupID = groupID
		}
	}

	return chatMsg
}

// ConvertOutgoingMessage 转换回复消息格式
// 思路：将回复消息转换为插件标准格式
func ConvertOutgoingMessage(userID, platform, content, sessionID string) *ChatMessage {
	return &ChatMessage{
		ID:          generateMessageID(),
		UserID:      userID,
		Platform:    platform,
		Content:     content,
		MessageType: "text",
		Direction:   "outgoing",
		SessionID:   sessionID,
		Metadata:    make(map[string]interface{}),
		Timestamp:   time.Now(),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	return time.Now().Format("20060102150405") + "_" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
