-- 多AI客服处理器
-- 思路：支持为不同平台、群聊、私聊配置不同的AI API
-- 使用例子：可以为QQ群使用硅基流动，为微信使用OpenAI，为Telegram使用DeepSeek等

-- 检查是否是语法验证模式，如果是则跳过实际处理
if __syntax_check_mode then
    log("info", "语法验证模式，跳过多AI客服处理")
    return nil
end

-- 检查消息和用户信息
if not message or not message.content then
    log("warn", "消息内容为空")
    return nil
end

if not message.from or not message.from.platform_user_id then
    log("warn", "用户信息不完整")
    return nil
end

local content = message.content
local user_id = message.from.platform_user_id
local platform = message.platform or "unknown"

-- 获取群聊信息
local group_id = ""
local is_group_chat = false
if message.metadata and message.metadata.group_id then
    group_id = message.metadata.group_id
    is_group_chat = true
end

log("info",
    "多AI客服处理用户 " .. user_id .. " 的消息: " .. content .. " (平台: " .. platform .. ", 群聊: " .. tostring(is_group_chat) .. ")")

-- 加载AI配置管理器
-- 思路：使用外部配置管理器，支持动态配置
local config_manager_path = "scripts/config/ai_config_manager.lua"
local config_manager_file = io.open(config_manager_path, "r")
if config_manager_file then
    config_manager_file:close()
    dofile(config_manager_path)
    log("info", "AI配置管理器加载成功")
else
    log("warn", "AI配置管理器文件不存在，使用内置配置")
end

-- AI配置选择函数
-- 思路：使用配置管理器获取AI配置，如果不可用则使用内置配置
-- 返回：{api_key, base_url, model, temperature, max_tokens}
local function selectAIConfig(platform, user_id, group_id, is_group_chat)
    -- 尝试使用配置管理器
    if getAIConfig then
        return getAIConfig(platform, user_id, group_id, is_group_chat)
    end

    -- 内置配置（备用）
    local default_config = {
        api_key = "", -- 空字符串表示使用全局配置
        base_url = "",
        model = "gpt-3.5-turbo",
        temperature = 0.7,
        max_tokens = 500
    }

    -- 简化的平台配置
    if platform == "qq" then
        return {
            api_key = "sk-your_siliconflow_key_here",
            base_url = "https://api.siliconflow.cn/v1",
            model = is_group_chat and "meta-llama/Llama-3.1-8B-Instruct" or "Qwen/Qwen2.5-72B-Instruct",
            temperature = 0.7,
            max_tokens = is_group_chat and 200 or 500
        }
    elseif platform == "telegram" then
        return {
            api_key = "sk-your_openai_key_here",
            base_url = "https://api.openai.com/v1",
            model = "gpt-4",
            temperature = 0.8,
            max_tokens = 600
        }
    end

    return default_config
end

-- 消息过滤函数（复用之前的逻辑）
local function shouldRespondToMessage(content, user_id, platform)
    local content_lower = content:lower()

    -- 1. 过滤过短的消息
    if string.len(content) < 3 then
        return false
    end

    -- 2. 过滤CQ码消息（图片、表情、语音等）
    if content:find("%[CQ:") then
        return false
    end

    -- 3. 过滤纯表情和符号
    if content:match("^[%.%,!%?]+$") or content:match("^%d+$") then
        return false
    end

    -- 4. 过滤常见闲聊词汇
    local ignore_keywords = {
        "哈哈", "呵呵", "666", "牛逼", "厉害", "赞", "好的", "嗯", "哦", "啊",
        "👍", "👌", "😂", "😄", "😊", "🤣", "😁", "😃", "😆", "😅"
    }

    for _, keyword in ipairs(ignore_keywords) do
        if content_lower:find(keyword:lower()) then
            return false
        end
    end

    -- 5. 检查响应关键词
    local response_keywords = {
        "客服", "帮助", "help", "问题", "咨询", "售后", "投诉", "bug", "故障",
        "怎么", "如何", "为什么", "什么", "哪里", "谁", "when", "where", "why", "how", "what"
    }

    for _, keyword in ipairs(response_keywords) do
        if content_lower:find(keyword:lower()) then
            return true
        end
    end

    -- 6. 检查命令前缀
    local first_char = content:sub(1, 1)
    local command_prefixes = { "/", "!", "@", "？", "?" }
    for _, prefix in ipairs(command_prefixes) do
        if first_char == prefix then
            return true
        end
    end

    -- 7. 检查问句（包含问号）
    if content:find("?") or content:find("？") then
        return true
    end

    -- 8. 检查消息长度（适中长度的消息可能是问题，但过长的可能是闲聊）
    local content_len = string.len(content)
    if content_len >= 10 and content_len <= 50 then
        -- 中等长度的消息，可能是问题，但需要进一步检查
        -- 如果包含游戏相关词汇，则不响应
        local game_keywords = {
            "游戏", "打牌", "发牌", "摸到", "866", "司马", "对面", "三张", "牌员",
            "高松", "千早", "长崎", "椎名", "胃袋", "肉食", "爱吃", "立吸"
        }

        for _, keyword in ipairs(game_keywords) do
            if content_lower:find(keyword) then
                return false
            end
        end

        return true
    end

    -- 9. 默认不响应
    return false
end

-- 0. 消息过滤检查（最高优先级）
local should_respond = shouldRespondToMessage(content, user_id, platform)
if not should_respond then
    -- log("info", "根据过滤规则，不响应此消息")
    return "消息已过滤，不响应"
end

-- 1. 选择AI配置
local ai_config = selectAIConfig(platform, user_id, group_id, is_group_chat)
log("info", "选择AI配置 - 模型: " .. ai_config.model .. ", API: " .. (ai_config.base_url ~= "" and ai_config.base_url or "默认"))

-- 2. 构建消息历史
local messages = {
    {
        role = "system",
        content = "你是一个专业的客服助手。请根据用户问题提供准确、有用的回答。保持友好和专业的语调。"
    },
    {
        role = "user",
        content = content
    }
}

-- 3. 调用AI API
local openai_response = call_openai({
    model = ai_config.model,
    messages = messages,
    max_tokens = ai_config.max_tokens,
    temperature = ai_config.temperature,
    api_key = ai_config.api_key,
    base_url = ai_config.base_url
})

if openai_response and openai_response.success then
    local ai_reply = openai_response.choices[1].message.content
    local tokens_used = openai_response.usage and openai_response.usage.total_tokens or 0

    -- 发送AI回复
    local reply_prefix = "🤖 "
    if ai_config.base_url:find("siliconflow") then
        reply_prefix = "🚀 " -- 硅基流动
    elseif ai_config.base_url:find("deepseek") then
        reply_prefix = "🧠 " -- DeepSeek
    elseif ai_config.base_url:find("openai") then
        reply_prefix = "✨ " -- OpenAI
    end

    send_message(user_id, reply_prefix .. ai_reply, group_id)

    log("info", "多AI回复成功 - 模型: " .. ai_config.model .. ", tokens: " .. tokens_used)
    return "多AI智能回复: " .. string.sub(ai_reply, 1, 50) .. "..."
else
    -- AI调用失败，使用备用回复
    log("error", "AI API调用失败: " .. (openai_response and openai_response.error or "未知错误"))

    local fallback_reply = "抱歉，我现在遇到了一些技术问题。请您：\n" ..
        "1. 稍后再试\n" ..
        "2. 发送'人工客服'转接人工服务\n" ..
        "3. 发送'知识库'搜索相关文档"

    send_message(user_id, fallback_reply, group_id)
    return "AI调用失败，已发送备用回复"
end
