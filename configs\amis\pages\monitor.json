{"url": "/monitor", "schema": {"type": "page", "title": "系统监控", "body": [{"type": "grid", "columns": [{"md": 6, "body": {"type": "card", "header": {"title": "系统健康状态", "subTitle": "实时系统状态监控"}, "body": {"type": "service", "api": "get:/api/v1/monitor/health", "interval": 5000, "body": {"type": "property", "column": 2, "items": [{"label": "系统状态", "content": "${status === 'HEALTH_STATUS_HEALTHY' ? '🟢 健康' : status === 'HEALTH_STATUS_WARNING' ? '🟡 警告' : '🔴 异常'}"}, {"label": "运行时间", "content": "${uptime}"}, {"label": "CPU使用率", "content": "${cpu_usage}%"}, {"label": "内存使用", "content": "${memory_usage}"}, {"label": "磁盘使用", "content": "${disk_usage}"}, {"label": "网络状态", "content": "${network_status === 'NETWORK_STATUS_CONNECTED' ? '🟢 正常' : '🔴 异常'}"}]}}}}, {"md": 6, "body": {"type": "card", "header": {"title": "性能指标", "subTitle": "系统性能实时监控"}, "body": {"type": "service", "api": "get:/api/v1/monitor/metrics", "interval": 5000, "body": {"type": "property", "column": 2, "items": [{"label": "请求总数", "content": "${total_requests || 0}"}, {"label": "成功请求", "content": "${successful_requests || 0}"}, {"label": "失败请求", "content": "${failed_requests || 0}"}, {"label": "平均响应时间", "content": "${avg_response_time || 0}ms"}, {"label": "QPS", "content": "${qps || 0}"}, {"label": "错误率", "content": "${error_rate || 0}%"}]}}}}]}]}}