package router

import (
	"aike_go/internal/handlers"

	"github.com/gin-gonic/gin"
)

// 创建Amis配置处理器
var amisConfigHandler = handlers.NewAmisConfigHandler("configs/amis")

func SetupRoutes(e *gin.Engine) {
	// 静态文件服务
	e.Static("/static", "./web")

	// 首页路由
	e.GET("/", indexPage)

	// 管理后台路由
	e.GET("/admin", adminPage)
	e.GET("/admin/*any", adminPage)

	// Amis管理后台路由
	e.GET("/amis", amisAdminPage)
	e.GET("/amis/*any", amisAdminPage)

	// Amis配置API路由
	e.GET("/api/v1/admin/amis-config", getAmisConfig)
	e.GET("/api/v1/admin/amis-config/pages/:page", getAmisPageConfig)
}

// indexPage 首页处理器
// 思路：提供系统首页，展示系统概览和快速入口
func indexPage(c *gin.Context) {
	c.File("./web/index.html")
}

// adminPage 管理后台页面处理器
// 思路：提供单页应用的路由支持，所有管理后台路径都返回同一个HTML页面
func adminPage(c *gin.Context) {
	c.File("./web/admin.html")
}

// amisAdminPage Amis管理后台页面处理器
// 思路：提供基于Amis的低代码管理界面，通过JSON配置快速构建管理功能
func amisAdminPage(c *gin.Context) {
	c.File("./web/amis_admin_simple.html")
}

// getAmisConfig 获取Amis配置API处理器
// 思路：委托给AmisConfigHandler处理，返回JSON格式的Amis配置
// 使用例子：GET /api/v1/admin/amis-config
func getAmisConfig(c *gin.Context) {
	amisConfigHandler.GetAmisConfig(c)
}

// getAmisPageConfig 获取单个页面配置API处理器
// 思路：委托给AmisConfigHandler处理，返回指定页面的配置
// 使用例子：GET /api/v1/admin/amis-config/pages/dashboard
func getAmisPageConfig(c *gin.Context) {
	amisConfigHandler.GetPageConfig(c)
}
