package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthConfig 认证配置
// 思路：配置不同的认证方式和规则
type AuthConfig struct {
	Enabled       bool              `json:"enabled"`        // 是否启用认证
	SecretKey     string            `json:"secret_key"`     // 密钥
	TokenExpiry   time.Duration     `json:"token_expiry"`   // Token过期时间
	PublicPaths   []string          `json:"public_paths"`   // 公开路径（不需要认证）
	AdminPaths    []string          `json:"admin_paths"`    // 管理员路径
	PlatformKeys  map[string]string `json:"platform_keys"`  // 平台密钥
	AllowedIPs    []string          `json:"allowed_ips"`    // 允许的IP地址
}

// AuthClaims 认证声明
// 思路：JWT Token中包含的用户信息
type AuthClaims struct {
	UserID    string    `json:"user_id"`
	Platform  string    `json:"platform"`
	Role      string    `json:"role"`
	IssuedAt  time.Time `json:"issued_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

// AuthMiddleware 认证中间件
// 思路：验证请求的身份和权限
// 使用例子：router.Use(middleware.AuthMiddleware(config))
func AuthMiddleware(config *AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果认证未启用，直接通过
		if !config.Enabled {
			c.Next()
			return
		}

		path := c.Request.URL.Path

		// 检查是否为公开路径
		if isPublicPath(path, config.PublicPaths) {
			c.Next()
			return
		}

		// 检查IP白名单
		if len(config.AllowedIPs) > 0 && !isAllowedIP(c.ClientIP(), config.AllowedIPs) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "IP地址不在允许列表中",
				"code":  "IP_NOT_ALLOWED",
			})
			c.Abort()
			return
		}

		// 验证认证信息
		claims, err := validateAuth(c, config)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": err.Error(),
				"code":  "AUTHENTICATION_FAILED",
			})
			c.Abort()
			return
		}

		// 检查管理员权限
		if isAdminPath(path, config.AdminPaths) && claims.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "需要管理员权限",
				"code":  "ADMIN_REQUIRED",
			})
			c.Abort()
			return
		}

		// 将用户信息设置到上下文
		c.Set("user_id", claims.UserID)
		c.Set("platform", claims.Platform)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// validateAuth 验证认证信息
// 思路：支持多种认证方式：Bearer Token、API Key、平台签名
func validateAuth(c *gin.Context, config *AuthConfig) (*AuthClaims, error) {
	// 1. 尝试Bearer Token认证
	if authHeader := c.GetHeader("Authorization"); authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			return validateBearerToken(token, config)
		}
	}

	// 2. 尝试API Key认证
	if apiKey := c.GetHeader("X-API-Key"); apiKey != "" {
		return validateAPIKey(apiKey, config)
	}

	// 3. 尝试平台签名认证
	if platform := c.GetHeader("X-Platform"); platform != "" {
		return validatePlatformSignature(c, platform, config)
	}

	return nil, fmt.Errorf("缺少认证信息")
}

// validateBearerToken 验证Bearer Token
// 思路：验证JWT Token的有效性和过期时间
func validateBearerToken(token string, config *AuthConfig) (*AuthClaims, error) {
	// 简化实现：使用HMAC验证
	// 实际项目中应该使用JWT库
	
	// 解析token（这里简化处理）
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("无效的token格式")
	}

	// 验证签名
	expectedSig := generateHMAC(parts[0]+"."+parts[1], config.SecretKey)
	if parts[2] != expectedSig {
		return nil, fmt.Errorf("token签名验证失败")
	}

	// 返回模拟的claims（实际应该解析JWT payload）
	return &AuthClaims{
		UserID:    "system",
		Platform:  "api",
		Role:      "user",
		IssuedAt:  time.Now().Add(-time.Hour),
		ExpiresAt: time.Now().Add(time.Hour),
	}, nil
}

// validateAPIKey 验证API Key
// 思路：验证预设的API密钥
func validateAPIKey(apiKey string, config *AuthConfig) (*AuthClaims, error) {
	// 检查是否为有效的API Key
	// 这里简化处理，实际应该从数据库查询
	if apiKey == config.SecretKey {
		return &AuthClaims{
			UserID:    "api_user",
			Platform:  "api",
			Role:      "user",
			IssuedAt:  time.Now(),
			ExpiresAt: time.Now().Add(24 * time.Hour),
		}, nil
	}

	return nil, fmt.Errorf("无效的API Key")
}

// validatePlatformSignature 验证平台签名
// 思路：验证来自平台的请求签名
func validatePlatformSignature(c *gin.Context, platform string, config *AuthConfig) (*AuthClaims, error) {
	// 获取平台密钥
	secretKey, exists := config.PlatformKeys[platform]
	if !exists {
		return nil, fmt.Errorf("未知的平台: %s", platform)
	}

	// 获取签名相关头部
	timestamp := c.GetHeader("X-Timestamp")
	signature := c.GetHeader("X-Signature")
	
	if timestamp == "" || signature == "" {
		return nil, fmt.Errorf("缺少签名信息")
	}

	// 验证时间戳（防重放攻击）
	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的时间戳")
	}

	if time.Since(time.Unix(ts, 0)) > 5*time.Minute {
		return nil, fmt.Errorf("请求已过期")
	}

	// 构建签名字符串
	method := c.Request.Method
	path := c.Request.URL.Path
	signString := fmt.Sprintf("%s\n%s\n%s", method, path, timestamp)

	// 验证签名
	expectedSig := generateHMAC(signString, secretKey)
	if signature != expectedSig {
		return nil, fmt.Errorf("签名验证失败")
	}

	return &AuthClaims{
		UserID:    platform + "_user",
		Platform:  platform,
		Role:      "platform",
		IssuedAt:  time.Now(),
		ExpiresAt: time.Now().Add(time.Hour),
	}, nil
}

// generateHMAC 生成HMAC签名
// 思路：使用HMAC-SHA256生成签名
func generateHMAC(data, key string) string {
	h := hmac.New(sha256.New, []byte(key))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// isPublicPath 检查是否为公开路径
// 思路：匹配公开路径列表
func isPublicPath(path string, publicPaths []string) bool {
	for _, publicPath := range publicPaths {
		if matchPath(path, publicPath) {
			return true
		}
	}
	return false
}

// isAdminPath 检查是否为管理员路径
// 思路：匹配管理员路径列表
func isAdminPath(path string, adminPaths []string) bool {
	for _, adminPath := range adminPaths {
		if matchPath(path, adminPath) {
			return true
		}
	}
	return false
}

// isAllowedIP 检查IP是否在允许列表中
// 思路：检查IP白名单
func isAllowedIP(clientIP string, allowedIPs []string) bool {
	for _, allowedIP := range allowedIPs {
		if clientIP == allowedIP || allowedIP == "*" {
			return true
		}
	}
	return false
}

// matchPath 路径匹配
// 思路：支持通配符匹配
func matchPath(path, pattern string) bool {
	// 精确匹配
	if path == pattern {
		return true
	}

	// 通配符匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}

	return false
}

// GenerateAPIKey 生成API密钥
// 思路：生成用于API访问的密钥
// 使用例子：apiKey := GenerateAPIKey("user123", "platform")
func GenerateAPIKey(userID, platform string) string {
	data := fmt.Sprintf("%s:%s:%d", userID, platform, time.Now().Unix())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// GeneratePlatformSignature 生成平台签名
// 思路：为平台请求生成签名
// 使用例子：sig := GeneratePlatformSignature("GET", "/api/v1/test", timestamp, secretKey)
func GeneratePlatformSignature(method, path string, timestamp int64, secretKey string) string {
	signString := fmt.Sprintf("%s\n%s\n%d", method, path, timestamp)
	return generateHMAC(signString, secretKey)
}

// GetDefaultAuthConfig 获取默认认证配置
// 思路：提供合理的默认认证配置
func GetDefaultAuthConfig() *AuthConfig {
	return &AuthConfig{
		Enabled:     false, // 默认关闭，需要手动启用
		SecretKey:   "your-secret-key-here",
		TokenExpiry: 24 * time.Hour,
		PublicPaths: []string{
			"/health",
			"/static/*",
			"/api/v1/platforms/*/webhook",
		},
		AdminPaths: []string{
			"/api/v1/admin/*",
			"/api/v1/platforms/stats",
		},
		PlatformKeys: map[string]string{
			"qq":       "qq-secret-key",
			"wechat":   "wechat-secret-key",
			"telegram": "telegram-secret-key",
			"qianniu":  "qianniu-secret-key",
		},
		AllowedIPs: []string{}, // 空表示允许所有IP
	}
}
