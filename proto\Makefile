# Protocol Buffers 代码生成 Makefile
# 思路：自动化生成 Go 代码和文档

# 变量定义
PROTO_DIR = .
GO_OUT_DIR = ../internal/proto
DOCS_OUT_DIR = ../docs/proto

# Proto 文件列表
PROTO_FILES = aike_service.proto aike_api.proto

# 默认目标
.PHONY: all
all: clean generate docs

# 清理生成的文件
.PHONY: clean
clean:
	@echo "🧹 清理生成的文件..."
	@rm -rf $(GO_OUT_DIR)
	@rm -rf $(DOCS_OUT_DIR)
	@echo "✅ 清理完成"

# 创建输出目录
.PHONY: dirs
dirs:
	@echo "📁 创建输出目录..."
	@mkdir -p $(GO_OUT_DIR)
	@mkdir -p $(DOCS_OUT_DIR)
	@echo "✅ 目录创建完成"

# 生成 Go 代码
.PHONY: generate
generate: dirs
	@echo "🔧 生成 Go 代码..."
	@for proto in $(PROTO_FILES); do \
		echo "  处理 $$proto..."; \
		protoc \
			--proto_path=$(PROTO_DIR) \
			--go_out=$(GO_OUT_DIR) \
			--go_opt=paths=source_relative \
			--go-grpc_out=$(GO_OUT_DIR) \
			--go-grpc_opt=paths=source_relative \
			$$proto; \
	done
	@echo "✅ Go 代码生成完成"

# 生成文档
.PHONY: docs
docs: dirs
	@echo "📚 生成 Protocol Buffers 文档..."
	@protoc \
		--proto_path=$(PROTO_DIR) \
		--doc_out=$(DOCS_OUT_DIR) \
		--doc_opt=html,index.html \
		$(PROTO_FILES)
	@protoc \
		--proto_path=$(PROTO_DIR) \
		--doc_out=$(DOCS_OUT_DIR) \
		--doc_opt=markdown,README.md \
		$(PROTO_FILES)
	@echo "✅ 文档生成完成"

# 验证 proto 文件语法
.PHONY: validate
validate:
	@echo "🔍 验证 Proto 文件语法..."
	@for proto in $(PROTO_FILES); do \
		echo "  验证 $$proto..."; \
		protoc --proto_path=$(PROTO_DIR) --descriptor_set_out=/dev/null $$proto; \
	done
	@echo "✅ 语法验证通过"

# 格式化 proto 文件
.PHONY: format
format:
	@echo "🎨 格式化 Proto 文件..."
	@for proto in $(PROTO_FILES); do \
		echo "  格式化 $$proto..."; \
		clang-format -i $$proto; \
	done
	@echo "✅ 格式化完成"

# 安装依赖工具
.PHONY: install-tools
install-tools:
	@echo "🛠️  安装 Protocol Buffers 工具..."
	@echo "请确保已安装以下工具："
	@echo "1. protoc (Protocol Buffers 编译器)"
	@echo "   - macOS: brew install protobuf"
	@echo "   - Ubuntu: apt-get install protobuf-compiler"
	@echo "   - Windows: 下载 https://github.com/protocolbuffers/protobuf/releases"
	@echo ""
	@echo "2. Go 插件:"
	@echo "   go install google.golang.org/protobuf/cmd/protoc-gen-go@latest"
	@echo "   go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest"
	@echo ""
	@echo "3. 文档生成插件:"
	@echo "   go install github.com/pseudomuto/protoc-gen-doc/cmd/protoc-gen-doc@latest"
	@echo ""
	@echo "4. 格式化工具 (可选):"
	@echo "   - macOS: brew install clang-format"
	@echo "   - Ubuntu: apt-get install clang-format"

# 检查工具是否安装
.PHONY: check-tools
check-tools:
	@echo "🔍 检查工具安装状态..."
	@command -v protoc >/dev/null 2>&1 || { echo "❌ protoc 未安装"; exit 1; }
	@command -v protoc-gen-go >/dev/null 2>&1 || { echo "❌ protoc-gen-go 未安装"; exit 1; }
	@command -v protoc-gen-go-grpc >/dev/null 2>&1 || { echo "❌ protoc-gen-go-grpc 未安装"; exit 1; }
	@command -v protoc-gen-doc >/dev/null 2>&1 || { echo "⚠️  protoc-gen-doc 未安装 (文档生成将跳过)"; }
	@echo "✅ 工具检查完成"

# 显示帮助信息
.PHONY: help
help:
	@echo "AI客服系统 Protocol Buffers 构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  all           - 执行完整构建流程 (清理 + 生成 + 文档)"
	@echo "  generate      - 生成 Go 代码"
	@echo "  docs          - 生成文档"
	@echo "  validate      - 验证 proto 文件语法"
	@echo "  format        - 格式化 proto 文件"
	@echo "  clean         - 清理生成的文件"
	@echo "  install-tools - 显示工具安装说明"
	@echo "  check-tools   - 检查工具安装状态"
	@echo "  help          - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make all      - 完整构建"
	@echo "  make generate - 只生成 Go 代码"
	@echo "  make docs     - 只生成文档"

# 开发模式 - 监听文件变化并自动重新生成
.PHONY: watch
watch:
	@echo "👀 监听模式启动 (需要安装 fswatch)..."
	@echo "当 proto 文件发生变化时将自动重新生成代码"
	@echo "按 Ctrl+C 退出监听模式"
	@fswatch -o $(PROTO_FILES) | while read f; do \
		echo "🔄 检测到文件变化，重新生成..."; \
		make generate; \
		echo "✅ 重新生成完成"; \
	done
