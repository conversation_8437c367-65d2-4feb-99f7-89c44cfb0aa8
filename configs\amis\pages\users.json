{"url": "/users", "schema": {"type": "page", "title": "用户管理", "body": {"type": "crud", "api": {"method": "get", "url": "/api/v1/users", "adaptor": "return {data: payload.users || [], total: payload.pagination?.total_count || 0};"}, "saveOrderApi": {"method": "put", "url": "/api/v1/users/batch-update"}, "filter": {"title": "搜索", "body": [{"type": "input-text", "name": "search", "placeholder": "搜索用户昵称或ID", "clearable": true}, {"type": "select", "name": "platform", "placeholder": "选择平台", "clearable": true, "options": [{"label": "QQ", "value": "qq"}, {"label": "微信", "value": "wechat"}, {"label": "Telegram", "value": "telegram"}]}, {"type": "select", "name": "status", "placeholder": "用户状态", "clearable": true, "options": [{"label": "活跃", "value": "active"}, {"label": "非活跃", "value": "inactive"}, {"label": "已封禁", "value": "blocked"}]}, {"type": "submit", "label": "搜索"}]}, "headerToolbar": [{"type": "button", "label": "新增用户", "level": "primary", "actionType": "dialog", "dialog": {"title": "新增用户", "body": {"type": "form", "api": "post:/api/v1/users", "body": [{"type": "input-text", "name": "platform_id", "label": "平台用户ID", "required": true, "placeholder": "如QQ号、微信openid等"}, {"type": "select", "name": "platform", "label": "平台类型", "required": true, "options": [{"label": "QQ", "value": "qq"}, {"label": "微信", "value": "wechat"}, {"label": "Telegram", "value": "telegram"}, {"label": "千牛", "value": "<PERSON>ian<PERSON>u"}]}, {"type": "input-text", "name": "nickname", "label": "用户昵称", "required": true}, {"type": "input-url", "name": "avatar", "label": "头像URL"}, {"type": "input-email", "name": "email", "label": "邮箱"}, {"type": "input-text", "name": "phone", "label": "手机号"}, {"type": "switch", "name": "is_vip", "label": "VIP用户", "value": false}]}}}, {"type": "button", "label": "批量操作", "level": "info", "actionType": "dropdown", "buttons": [{"type": "button", "label": "批量激活", "actionType": "ajax", "api": "put:/api/v1/users/batch-activate", "confirmText": "确定要激活选中的用户吗？"}, {"type": "button", "label": "批量封禁", "actionType": "ajax", "api": "put:/api/v1/users/batch-block", "confirmText": "确定要封禁选中的用户吗？", "level": "danger"}]}], "columns": [{"type": "checkbox", "name": "id", "label": "", "width": 50}, {"name": "id", "label": "ID", "width": 80, "sortable": true}, {"name": "platform_id", "label": "平台ID", "width": 120, "searchable": true}, {"name": "platform", "label": "平台", "width": 100, "type": "mapping", "map": {"qq": "<span class='label label-info'>QQ</span>", "wechat": "<span class='label label-success'>微信</span>", "telegram": "<span class='label label-primary'>Telegram</span>", "qianniu": "<span class='label label-warning'>千牛</span>"}}, {"name": "nickname", "label": "昵称", "width": 150, "searchable": true}, {"name": "avatar", "label": "头像", "width": 80, "type": "image", "enlargeAble": true, "thumbMode": "cover", "thumbRatio": "1:1"}, {"name": "status", "label": "状态", "width": 100, "type": "mapping", "map": {"USER_STATUS_ACTIVE": "<span class='label label-success'>活跃</span>", "USER_STATUS_INACTIVE": "<span class='label label-warning'>非活跃</span>", "USER_STATUS_BLOCKED": "<span class='label label-danger'>已封禁</span>"}}, {"name": "is_vip", "label": "VIP", "width": 80, "type": "mapping", "map": {"true": "<span class='label label-warning'>VIP</span>", "false": "<span class='label label-default'>普通</span>"}}, {"name": "message_count", "label": "消息数", "width": 100, "sortable": true}, {"name": "email", "label": "邮箱", "width": 150}, {"name": "phone", "label": "手机号", "width": 120}, {"name": "last_active_at", "label": "最后活跃", "width": 180, "type": "datetime", "sortable": true}, {"name": "created_at", "label": "创建时间", "width": 180, "type": "datetime", "sortable": true}, {"type": "operation", "label": "操作", "width": 150, "buttons": [{"type": "button", "label": "查看", "level": "link", "actionType": "dialog", "dialog": {"title": "用户详情", "body": {"type": "property", "column": 2, "items": [{"label": "用户ID", "content": "${id}"}, {"label": "平台ID", "content": "${platform_id}"}, {"label": "平台", "content": "${platform}"}, {"label": "昵称", "content": "${nickname}"}, {"label": "状态", "content": "${status}"}, {"label": "VIP", "content": "${is_vip ? 'VIP用户' : '普通用户'}"}, {"label": "消息数", "content": "${message_count}"}, {"label": "最后活跃", "content": "${last_active_at}"}]}}}, {"type": "button", "label": "编辑", "level": "link", "actionType": "dialog", "dialog": {"title": "编辑用户", "body": {"type": "form", "api": "put:/api/v1/users/${id}", "initApi": "get:/api/v1/users/${id}", "body": [{"type": "input-text", "name": "nickname", "label": "用户昵称", "required": true}, {"type": "input-url", "name": "avatar", "label": "头像URL"}, {"type": "input-email", "name": "email", "label": "邮箱"}, {"type": "input-text", "name": "phone", "label": "手机号"}, {"type": "select", "name": "status", "label": "用户状态", "options": [{"label": "活跃", "value": "USER_STATUS_ACTIVE"}, {"label": "非活跃", "value": "USER_STATUS_INACTIVE"}, {"label": "已封禁", "value": "USER_STATUS_BLOCKED"}]}, {"type": "switch", "name": "is_vip", "label": "VIP用户"}]}}}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "actionType": "ajax", "confirmText": "确定要删除该用户吗？此操作不可恢复！", "api": "delete:/api/v1/users/${id}"}]}]}}}