package adapters

import (
	"context"
	"log"

	"aike_go/internal/database"
	"aike_go/internal/interfaces"
	"aike_go/internal/plugins"
	"aike_go/internal/plugins/storage"
	"aike_go/internal/scripting"
	"aike_go/internal/services"
)

// ScriptServiceAdapter 脚本服务适配器
// 思路：解耦services和scripting包的循环依赖
type ScriptServiceAdapter struct {
	scriptService *services.ScriptService
	luaEngine     *scripting.SimpleLuaEngine
}

// NewScriptServiceAdapter 创建脚本服务适配器
// 思路：在这里组装依赖，避免循环导入
func NewScriptServiceAdapter(
	scriptService *services.ScriptService,
	openaiService interfaces.OpenAIService,
	knowledgeService interfaces.KnowledgeService,
) *ScriptServiceAdapter {
	// 创建简化的Lua引擎
	luaEngine := scripting.NewSimpleLuaEngine(openaiService, knowledgeService)

	// 初始化插件系统
	// 创建插件管理器
	pluginManager := plugins.NewPluginManager()

	// 创建数据库存储插件
	db := database.GetDB()
	dbStoragePlugin := storage.NewDatabaseStoragePlugin(db)

	// 注册存储插件
	if err := pluginManager.RegisterPlugin(dbStoragePlugin); err != nil {
		log.Printf("注册数据库存储插件失败: %v", err)
	} else {
		log.Printf("数据库存储插件已注册")
	}

	// 创建分布式存储插件
	distributedStoragePlugin := storage.NewDistributedStoragePlugin("data/distributed")

	// 注册分布式存储插件
	if err := pluginManager.RegisterPlugin(distributedStoragePlugin); err != nil {
		log.Printf("注册分布式存储插件失败: %v", err)
	} else {
		log.Printf("分布式存储插件已注册")
	}

	// 创建插件API
	pluginAPI := scripting.NewPluginAPI(pluginManager)
	pluginAPI.SetChatStoragePlugin(dbStoragePlugin)
	pluginAPI.SetDistributedStoragePlugin(distributedStoragePlugin)

	// 设置插件API到Lua引擎
	luaEngine.SetPluginAPI(pluginAPI)

	// 注入到脚本服务
	scriptService.SetLuaEngine(luaEngine)
	scriptService.SetPluginManager(pluginManager)

	return &ScriptServiceAdapter{
		scriptService: scriptService,
		luaEngine:     luaEngine,
	}
}

// GetLuaEngine 获取Lua引擎
// 思路：提供对内部Lua引擎的访问
func (a *ScriptServiceAdapter) GetLuaEngine() interfaces.ScriptEngine {
	return a.luaEngine
}

// LoadScriptsFromDatabase 从数据库加载脚本
// 思路：启动时加载所有脚本到Lua引擎
func (a *ScriptServiceAdapter) LoadScriptsFromDatabase(ctx context.Context) error {
	// 获取所有启用的脚本
	scripts, err := a.scriptService.GetAllScripts(ctx)
	if err != nil {
		return err
	}

	// 加载到Lua引擎
	for _, script := range scripts {
		if script.Enabled {
			if err := a.luaEngine.LoadScript(script); err != nil {
				// 记录错误但继续加载其他脚本
				log.Printf("加载脚本失败 %s: %v", script.Name, err)
				continue
			}
		}
	}

	return nil
}

// ExecuteMessageFilters 执行消息过滤器
// 思路：提供统一的脚本执行接口
func (a *ScriptServiceAdapter) ExecuteMessageFilters(ctx context.Context, message *interfaces.IncomingMessage) ([]*interfaces.ScriptResult, error) {
	scriptCtx := &interfaces.ScriptContext{
		Message:   message,
		Platform:  message.Platform,
		Timestamp: message.Timestamp,
		Variables: make(map[string]interface{}),
	}

	return a.luaEngine.ExecuteScriptsByType(ctx, "message_filter", scriptCtx)
}

// ExecuteMessageHandlers 执行消息处理器
// 思路：处理通过过滤器的消息
func (a *ScriptServiceAdapter) ExecuteMessageHandlers(ctx context.Context, message *interfaces.IncomingMessage) ([]*interfaces.ScriptResult, error) {
	scriptCtx := &interfaces.ScriptContext{
		Message:   message,
		Platform:  message.Platform,
		Timestamp: message.Timestamp,
		Variables: make(map[string]interface{}),
	}

	return a.luaEngine.ExecuteScriptsByType(ctx, "message_handler", scriptCtx)
}

// ExecuteBusinessRules 执行业务规则
// 思路：执行业务逻辑验证
func (a *ScriptServiceAdapter) ExecuteBusinessRules(ctx context.Context, message *interfaces.IncomingMessage) ([]*interfaces.ScriptResult, error) {
	scriptCtx := &interfaces.ScriptContext{
		Message:   message,
		Platform:  message.Platform,
		Timestamp: message.Timestamp,
		Variables: make(map[string]interface{}),
	}

	return a.luaEngine.ExecuteScriptsByType(ctx, "business_rule", scriptCtx)
}

// ProcessMessage 处理完整的消息流程
// 思路：按优先级执行所有类型的脚本
func (a *ScriptServiceAdapter) ProcessMessage(ctx context.Context, message *interfaces.IncomingMessage) (*MessageProcessResult, error) {
	result := &MessageProcessResult{
		Message:    message,
		Processed:  false,
		ShouldStop: false,
		Actions:    []interfaces.ScriptAction{},
		Results:    []*interfaces.ScriptResult{},
	}

	// 1. 执行业务规则（优先级最高）
	ruleResults, err := a.ExecuteBusinessRules(ctx, message)
	if err == nil {
		result.Results = append(result.Results, ruleResults...)
		for _, r := range ruleResults {
			if r.ShouldStop {
				result.ShouldStop = true
				return result, nil
			}
			result.Actions = append(result.Actions, r.Actions...)
		}
	}

	// 2. 执行消息过滤器
	filterResults, err := a.ExecuteMessageFilters(ctx, message)
	if err == nil {
		result.Results = append(result.Results, filterResults...)
		for _, r := range filterResults {
			if r.ShouldStop {
				result.ShouldStop = true
				return result, nil
			}
			result.Actions = append(result.Actions, r.Actions...)
		}
	}

	// 3. 执行消息处理器（优先级最低）
	handlerResults, err := a.ExecuteMessageHandlers(ctx, message)
	if err == nil {
		result.Results = append(result.Results, handlerResults...)
		for _, r := range handlerResults {
			result.Actions = append(result.Actions, r.Actions...)
			if r.Success && r.Result != nil {
				result.Processed = true
			}
		}
	}

	return result, nil
}

// MessageProcessResult 消息处理结果
// 思路：封装完整的消息处理结果
type MessageProcessResult struct {
	Message    *interfaces.IncomingMessage `json:"message"`
	Processed  bool                        `json:"processed"`
	ShouldStop bool                        `json:"should_stop"`
	Actions    []interfaces.ScriptAction   `json:"actions"`
	Results    []*interfaces.ScriptResult  `json:"results"`
}
