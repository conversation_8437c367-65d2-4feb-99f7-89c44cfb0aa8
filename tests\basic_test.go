package tests

import (
	"fmt"
	"sync"
	"testing"

	"aike_go/internal/config"
	"aike_go/internal/services"

	"github.com/stretchr/testify/assert"
)

// TestBasicFunctionality 基础功能测试
// 思路：测试系统的基本组件能否正常创建和工作
func TestBasicFunctionality(t *testing.T) {
	// 测试配置创建
	t.Run("配置创建", func(t *testing.T) {
		cfg := &config.Config{
			Host: "localhost",
			Port: 8080,
			Database: config.DatabaseConfig{
				Type:     "sqlite",
				Database: ":memory:",
			},
			OpenAI: config.OpenAIConfig{
				APIKey:  "test-api-key",
				BaseURL: "https://api.openai.com/v1",
				Model:   "gpt-3.5-turbo",
			},
		}

		assert.NotNil(t, cfg)
		assert.Equal(t, "localhost", cfg.Host)
		assert.Equal(t, 8080, cfg.Port)
		assert.Equal(t, "sqlite", cfg.Database.Type)
		assert.Equal(t, "test-api-key", cfg.OpenAI.APIKey)
	})

	// 测试服务创建
	t.Run("服务创建", func(t *testing.T) {
		cfg := &config.Config{
			OpenAI: config.OpenAIConfig{
				APIKey:  "test-api-key",
				BaseURL: "https://api.openai.com/v1",
				Model:   "gpt-3.5-turbo",
			},
		}

		// 创建聊天服务
		chatService := services.NewChatService(cfg)
		assert.NotNil(t, chatService)

		// 创建知识库服务
		knowledgeService := services.NewKnowledgeService()
		assert.NotNil(t, knowledgeService)
	})
}

// TestConfigValidation 配置验证测试
// 思路：测试配置的基本验证逻辑
func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name   string
		config *config.Config
		valid  bool
	}{
		{
			name: "有效配置",
			config: &config.Config{
				Host: "localhost",
				Port: 8080,
				Database: config.DatabaseConfig{
					Type:     "sqlite",
					Database: "test.db",
				},
			},
			valid: true,
		},
		{
			name: "无效端口",
			config: &config.Config{
				Host: "localhost",
				Port: 0,
				Database: config.DatabaseConfig{
					Type:     "sqlite",
					Database: "test.db",
				},
			},
			valid: false,
		},
		{
			name: "空主机",
			config: &config.Config{
				Host: "",
				Port: 8080,
				Database: config.DatabaseConfig{
					Type:     "sqlite",
					Database: "test.db",
				},
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			valid := validateConfig(tt.config)
			assert.Equal(t, tt.valid, valid)
		})
	}
}

// validateConfig 简单的配置验证函数
func validateConfig(cfg *config.Config) bool {
	if cfg.Host == "" {
		return false
	}
	if cfg.Port <= 0 || cfg.Port > 65535 {
		return false
	}
	return true
}

// TestStringUtils 字符串工具测试
// 思路：测试一些基本的字符串处理功能
func TestStringUtils(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "正常字符串",
			input:    "Hello World",
			expected: "hello world",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "中文字符串",
			input:    "你好世界",
			expected: "你好世界",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := toLowerCase(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// toLowerCase 简单的字符串转小写函数
func toLowerCase(s string) string {
	if s == "" {
		return ""
	}
	// 简化实现，只处理英文
	result := ""
	for _, r := range s {
		if r >= 'A' && r <= 'Z' {
			result += string(r + 32)
		} else {
			result += string(r)
		}
	}
	return result
}

// TestSliceUtils 切片工具测试
// 思路：测试一些基本的切片处理功能
func TestSliceUtils(t *testing.T) {
	t.Run("切片包含检查", func(t *testing.T) {
		slice := []string{"apple", "banana", "orange"}

		assert.True(t, contains(slice, "apple"))
		assert.True(t, contains(slice, "banana"))
		assert.False(t, contains(slice, "grape"))
		assert.False(t, contains([]string{}, "apple"))
	})

	t.Run("切片去重", func(t *testing.T) {
		input := []string{"apple", "banana", "apple", "orange", "banana"}
		expected := []string{"apple", "banana", "orange"}

		result := unique(input)
		assert.ElementsMatch(t, expected, result)
	})
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// unique 去除切片中的重复元素
func unique(slice []string) []string {
	seen := make(map[string]bool)
	result := []string{}

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// BenchmarkStringProcessing 字符串处理基准测试
// 思路：测试字符串处理的性能
func BenchmarkStringProcessing(b *testing.B) {
	input := "Hello World Test String"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = toLowerCase(input)
	}
}

// BenchmarkSliceProcessing 切片处理基准测试
// 思路：测试切片处理的性能
func BenchmarkSliceProcessing(b *testing.B) {
	input := []string{"apple", "banana", "apple", "orange", "banana", "grape", "apple"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = unique(input)
	}
}

// TestErrorHandling 错误处理测试
// 思路：测试基本的错误处理逻辑
func TestErrorHandling(t *testing.T) {
	t.Run("正常情况", func(t *testing.T) {
		result, err := divide(10, 2)
		assert.NoError(t, err)
		assert.Equal(t, 5.0, result)
	})

	t.Run("除零错误", func(t *testing.T) {
		result, err := divide(10, 0)
		assert.Error(t, err)
		assert.Equal(t, 0.0, result)
		assert.Contains(t, err.Error(), "除零")
	})
}

// divide 简单的除法函数，用于测试错误处理
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除零错误")
	}
	return a / b, nil
}

// TestConcurrency 并发测试
// 思路：测试基本的并发安全性
func TestConcurrency(t *testing.T) {
	counter := &SafeCounter{value: 0}

	// 启动多个goroutine并发增加计数器
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func() {
			for j := 0; j < 100; j++ {
				counter.Increment()
			}
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 验证最终结果
	assert.Equal(t, 1000, counter.Value())
}

// SafeCounter 线程安全的计数器
type SafeCounter struct {
	value int
	mutex sync.Mutex
}

// Increment 增加计数器
func (c *SafeCounter) Increment() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.value++
}

// Value 获取计数器值
func (c *SafeCounter) Value() int {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.value
}
