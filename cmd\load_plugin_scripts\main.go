package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"time"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/models"
)

// 插件脚本定义
type PluginScriptDef struct {
	Name        string
	Description string
	FilePath    string
	Type        string
	Priority    int
	Enabled     bool
}

func main() {
	fmt.Println("🔌 加载插件管理脚本")

	// 1. 初始化配置
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type: "sqlite",
			DSN:  "./aike_go.db", // 使用服务器实际使用的数据库
		},
	}

	// 2. 初始化数据库
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 3. 获取数据库实例
	db := database.GetDB()

	// 4. 定义插件脚本
	pluginScripts := []PluginScriptDef{
		{
			Name:        "聊天记录存储管理器",
			Description: "通过Lua脚本管理聊天记录的存储、查询和配置",
			FilePath:    "scripts/plugins/chat_storage_manager.lua",
			Type:        "message_handler",
			Priority:    50,
			Enabled:     true,
		},
		{
			Name:        "插件配置管理器",
			Description: "通过Lua脚本动态配置插件参数",
			FilePath:    "scripts/plugins/plugin_config_manager.lua",
			Type:        "message_handler",
			Priority:    40,
			Enabled:     true,
		},
		{
			Name:        "分布式存储管理器",
			Description: "使用分布式存储系统管理聊天记录，按平台和聊天类型分类存储",
			FilePath:    "scripts/plugins/distributed_storage_manager.lua",
			Type:        "message_handler",
			Priority:    40,
			Enabled:     true,
		},
	}

	fmt.Printf("📖 准备加载 %d 个插件脚本\n\n", len(pluginScripts))

	successCount := 0
	for _, scriptDef := range pluginScripts {
		fmt.Printf("🔄 加载脚本: %s\n", scriptDef.Name)

		// 读取脚本文件
		content, err := ioutil.ReadFile(scriptDef.FilePath)
		if err != nil {
			fmt.Printf("❌ 读取脚本文件失败: %v\n", err)
			continue
		}

		// 创建脚本记录
		script := &models.Script{
			Name:        scriptDef.Name,
			Description: scriptDef.Description,
			Content:     string(content),
			Type:        scriptDef.Type,
			Enabled:     scriptDef.Enabled,
			Priority:    scriptDef.Priority,
			Triggers:    models.StringArray{},
			Config:      models.StringMap{},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		// 检查是否已存在
		var existingScript models.Script
		result := db.Where("name = ?", script.Name).First(&existingScript)

		if result.Error == nil {
			// 脚本已存在，更新内容
			fmt.Printf("⚠️  脚本已存在，更新内容: %s\n", script.Name)

			existingScript.Content = script.Content
			existingScript.Description = script.Description
			existingScript.Type = script.Type
			existingScript.Priority = script.Priority
			existingScript.UpdatedAt = time.Now()

			if err := db.Save(&existingScript).Error; err != nil {
				fmt.Printf("❌ 更新脚本失败: %v\n", err)
				continue
			}

			fmt.Printf("✅ 更新脚本成功: %s (ID: %s)\n", script.Name, existingScript.ID)
		} else {
			// 创建新脚本
			if err := db.Create(script).Error; err != nil {
				fmt.Printf("❌ 创建脚本失败: %v\n", err)
				continue
			}

			fmt.Printf("✅ 创建脚本成功: %s (ID: %s)\n", script.Name, script.ID)
		}

		successCount++
		fmt.Println()
	}

	fmt.Printf("🎉 插件脚本加载完成！成功: %d, 总计: %d\n\n", successCount, len(pluginScripts))

	// 5. 查看已加载的脚本
	fmt.Println("🔄 查看已加载的脚本...")

	var scripts []models.Script
	if err := db.Where("enabled = ?", true).Find(&scripts).Error; err != nil {
		log.Printf("获取脚本列表失败: %v", err)
	} else {
		// 显示已启用的脚本
		for _, script := range scripts {
			fmt.Printf("📜 脚本: %s (类型: %s, 优先级: %d)\n", script.Name, script.Type, script.Priority)
		}
	}

	fmt.Println("✅ 插件脚本已准备就绪")

	fmt.Println("\n📖 使用说明:")
	fmt.Println("1. 启动服务器: go run cmd/server/main.go")
	fmt.Println("2. 发送插件管理命令测试:")
	fmt.Println("   • /plugin list - 查看所有插件")
	fmt.Println("   • /plugin enable <name> - 启用插件")
	fmt.Println("   • /plugin disable <name> - 禁用插件")
	fmt.Println("   • /config list - 查看插件配置")
	fmt.Println("   • /history 10 - 查看对话历史")
	fmt.Println("   • /search <关键词> - 搜索消息")
	fmt.Println("3. 查看服务器日志了解插件执行情况")
}
