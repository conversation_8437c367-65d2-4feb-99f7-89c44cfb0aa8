package handlers

import (
	"net/http"
	"strconv"
	"time"

	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
)

// KnowledgeHandlers 知识库处理器
// 思路：将知识库相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewKnowledgeHandlers(knowledgeService)
type KnowledgeHandlers struct {
	knowledgeService *services.KnowledgeService
}

// NewKnowledgeHandlers 创建知识库处理器
// 思路：初始化知识库处理器，注入知识库服务依赖
// 使用例子：handlers := NewKnowledgeHandlers(knowledgeService)
func NewKnowledgeHandlers(knowledgeService *services.KnowledgeService) *KnowledgeHandlers {
	return &KnowledgeHandlers{
		knowledgeService: knowledgeService,
	}
}

// CreateKnowledge 创建知识条目处理器
// 思路：处理创建新知识条目的HTTP请求
// 使用例子：POST /api/v1/knowledge/
func (h *KnowledgeHandlers) CreateKnowledge(c *gin.Context) {
	var req struct {
		Title    string `json:"title" binding:"required"`   // 知识标题
		Content  string `json:"content" binding:"required"` // 知识内容
		Keywords string `json:"keywords"`                   // 关键词（字符串格式）
		Category string `json:"category"`                   // 分类
		Tags     string `json:"tags"`                       // 标签（字符串格式）
		IsPublic bool   `json:"is_public"`                  // 是否公开
		Priority int    `json:"priority"`                   // 优先级
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 创建知识条目
	knowledge, err := h.knowledgeService.CreateKnowledge(services.CreateKnowledgeRequest{
		Title:    req.Title,
		Content:  req.Content,
		Keywords: req.Keywords,
		Category: req.Category,
		Tags:     req.Tags,
		IsPublic: req.IsPublic,
		Priority: req.Priority,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "创建知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "知识条目创建成功",
		"data":    knowledge,
	})
}

// ListKnowledge 获取知识列表处理器
// 思路：处理获取知识列表的HTTP请求，支持分页和过滤
// 使用例子：GET /api/v1/knowledge/?page=1&limit=20&category=FAQ
func (h *KnowledgeHandlers) ListKnowledge(c *gin.Context) {
	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")
	category := c.Query("category")
	keyword := c.Query("keyword")
	isPublicStr := c.Query("is_public")

	// 转换分页参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	// 转换布尔参数
	var isPublic *bool
	if isPublicStr != "" {
		if isPublicStr == "true" {
			val := true
			isPublic = &val
		} else if isPublicStr == "false" {
			val := false
			isPublic = &val
		}
	}

	// 计算偏移量
	offset := (page - 1) * limit

	// 获取知识列表
	knowledgeList, total, err := h.knowledgeService.ListKnowledge(category, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取知识列表失败",
			"details": err.Error(),
		})
		return
	}

	// 计算分页信息
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	c.JSON(http.StatusOK, gin.H{
		"message": "获取知识列表成功",
		"data": gin.H{
			"knowledge": knowledgeList,
			"pagination": gin.H{
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": totalPages,
				"has_next":    page < totalPages,
				"has_prev":    page > 1,
			},
		},
	})
}

// UpdateKnowledge 更新知识条目处理器
// 思路：处理更新知识条目的HTTP请求
// 使用例子：PUT /api/v1/knowledge/123
func (h *KnowledgeHandlers) UpdateKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的知识条目ID",
		})
		return
	}

	var req struct {
		Title    *string `json:"title"`     // 知识标题
		Content  *string `json:"content"`   // 知识内容
		Keywords *string `json:"keywords"`  // 关键词
		Category *string `json:"category"`  // 分类
		Tags     *string `json:"tags"`      // 标签
		IsPublic *bool   `json:"is_public"` // 是否公开
		Priority *int    `json:"priority"`  // 优先级
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 更新知识条目
	knowledge, err := h.knowledgeService.UpdateKnowledge(uint(id), services.UpdateKnowledgeRequest{
		Title:    req.Title,
		Content:  req.Content,
		Keywords: req.Keywords,
		Category: req.Category,
		Tags:     req.Tags,
		IsPublic: req.IsPublic,
		Priority: req.Priority,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "更新知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "知识条目更新成功",
		"data":    knowledge,
	})
}

// DeleteKnowledge 删除知识条目处理器
// 思路：处理删除知识条目的HTTP请求
// 使用例子：DELETE /api/v1/knowledge/123
func (h *KnowledgeHandlers) DeleteKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的知识条目ID",
		})
		return
	}

	// 删除知识条目
	err = h.knowledgeService.DeleteKnowledge(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "删除知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "知识条目删除成功",
		"data": gin.H{
			"id":         id,
			"deleted_at": time.Now().Unix(),
		},
	})
}

// SearchKnowledge 搜索知识库处理器
// 思路：处理知识库搜索的HTTP请求，支持全文搜索和相似度匹配
// 使用例子：POST /api/v1/knowledge/search
func (h *KnowledgeHandlers) SearchKnowledge(c *gin.Context) {
	var req struct {
		Query     string   `json:"query" binding:"required"` // 搜索查询
		Category  string   `json:"category"`                 // 分类过滤
		Tags      []string `json:"tags"`                     // 标签过滤
		Limit     int      `json:"limit"`                    // 结果数量限制
		Threshold float64  `json:"threshold"`                // 相似度阈值
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 50 {
		req.Limit = 10
	}
	if req.Threshold <= 0 {
		req.Threshold = 0.5
	}

	// 搜索知识库
	results, err := h.knowledgeService.SearchKnowledge(req.Query, req.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "搜索知识库失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "搜索知识库成功",
		"data": gin.H{
			"query":   req.Query,
			"results": results,
			"count":   len(results),
		},
	})
}
