package adapters

import (
	"aike_go/internal/interfaces"
)

// 重新导出接口类型，保持向后兼容
type IncomingMessage = interfaces.IncomingMessage
type OutgoingMessage = interfaces.OutgoingMessage
type UserInfo = interfaces.UserInfo
type MessageHandler = interfaces.MessageHandler
type PlatformConfig = interfaces.PlatformConfig
type AdapterStats = interfaces.AdapterStats
type PlatformAdapter = interfaces.PlatformAdapter
type PlatformEvent = interfaces.PlatformEvent

// 常量定义
const (
	MessageTypeText  = "text"
	MessageTypeImage = "image"
	MessageTypeVoice = "voice"
	MessageTypeFile  = "file"
	MessageTypeVideo = "video"

	ChatTypePrivate = "private"
	ChatTypeGroup   = "group"
	ChatTypeChannel = "channel"

	// 适配器状态常量
	AdapterStatusStopped  = interfaces.AdapterStatusStopped
	AdapterStatusStarting = interfaces.AdapterStatusStarting
	AdapterStatusRunning  = interfaces.AdapterStatusRunning
	AdapterStatusError    = interfaces.AdapterStatusError
)
