package services

import (
	"context"
	"fmt"
	"strings"

	"aike_go/internal/database"
	"aike_go/internal/models"

	"gorm.io/gorm"
)

// KnowledgeService 知识库服务结构体
// 思路：提供知识库的增删改查和搜索功能
// 使用例子：service := NewKnowledgeService(); results := service.SearchKnowledge(query, limit)
type KnowledgeService struct {
	db *gorm.DB
}

// KnowledgeSearchResult 知识库搜索结果
// 思路：包含知识条目和相似度分数
type KnowledgeSearchResult struct {
	Knowledge models.Knowledge `json:"knowledge"` // 知识条目
	Score     float64          `json:"score"`     // 相似度分数
}

// CreateKnowledgeRequest 创建知识库请求
// 思路：创建知识条目的请求结构
type CreateKnowledgeRequest struct {
	Title    string `json:"title" binding:"required"`   // 标题
	Content  string `json:"content" binding:"required"` // 内容
	Category string `json:"category"`                   // 分类
	Keywords string `json:"keywords"`                   // 关键词
	Tags     string `json:"tags"`                       // 标签
	Priority int    `json:"priority"`                   // 优先级
	IsPublic bool   `json:"is_public"`                  // 是否公开
}

// UpdateKnowledgeRequest 更新知识库请求
// 思路：更新知识条目的请求结构
type UpdateKnowledgeRequest struct {
	Title    *string `json:"title"`     // 标题
	Content  *string `json:"content"`   // 内容
	Category *string `json:"category"`  // 分类
	Keywords *string `json:"keywords"`  // 关键词
	Tags     *string `json:"tags"`      // 标签
	Priority *int    `json:"priority"`  // 优先级
	IsPublic *bool   `json:"is_public"` // 是否公开
	Status   *string `json:"status"`    // 状态
}

// SearchKnowledgeRequest 搜索知识库请求
// 思路：知识库搜索的请求参数
type SearchKnowledgeRequest struct {
	Query    string `json:"query" binding:"required"` // 搜索关键词
	Category string `json:"category"`                 // 分类过滤
	Limit    int    `json:"limit"`                    // 结果数量限制
	Offset   int    `json:"offset"`                   // 偏移量
}

// NewKnowledgeService 创建知识库服务实例
// 思路：初始化数据库连接
// 使用例子：service := NewKnowledgeService()
func NewKnowledgeService() *KnowledgeService {
	return &KnowledgeService{
		db: database.GetDB(),
	}
}

// CreateKnowledge 创建知识条目
// 思路：创建新的知识库条目并保存到数据库
// 使用例子：knowledge, err := service.CreateKnowledge(request)
func (s *KnowledgeService) CreateKnowledge(req CreateKnowledgeRequest) (*models.Knowledge, error) {
	knowledge := &models.Knowledge{
		Title:    req.Title,
		Content:  req.Content,
		Category: req.Category,
		Keywords: req.Keywords,
		Tags:     req.Tags,
		Priority: req.Priority,
		IsPublic: req.IsPublic,
		Status:   "active",
	}

	if err := s.db.Create(knowledge).Error; err != nil {
		return nil, fmt.Errorf("创建知识条目失败: %w", err)
	}

	return knowledge, nil
}

// GetKnowledge 获取知识条目
// 思路：根据ID获取单个知识条目
// 使用例子：knowledge, err := service.GetKnowledge(id)
func (s *KnowledgeService) GetKnowledge(id uint) (*models.Knowledge, error) {
	var knowledge models.Knowledge

	err := s.db.First(&knowledge, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("知识条目不存在")
		}
		return nil, fmt.Errorf("获取知识条目失败: %w", err)
	}

	// 增加查看次数
	knowledge.IncrementViewCount(s.db)

	return &knowledge, nil
}

// UpdateKnowledge 更新知识条目
// 思路：根据ID更新知识条目的指定字段
// 使用例子：knowledge, err := service.UpdateKnowledge(id, request)
func (s *KnowledgeService) UpdateKnowledge(id uint, req UpdateKnowledgeRequest) (*models.Knowledge, error) {
	var knowledge models.Knowledge

	// 先查找记录
	err := s.db.First(&knowledge, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("知识条目不存在")
		}
		return nil, fmt.Errorf("查找知识条目失败: %w", err)
	}

	// 构建更新数据
	updates := make(map[string]interface{})

	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Category != nil {
		updates["category"] = *req.Category
	}
	if req.Keywords != nil {
		updates["keywords"] = *req.Keywords
	}
	if req.Tags != nil {
		updates["tags"] = *req.Tags
	}
	if req.Priority != nil {
		updates["priority"] = *req.Priority
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	// 执行更新
	if err := s.db.Model(&knowledge).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新知识条目失败: %w", err)
	}

	return &knowledge, nil
}

// DeleteKnowledge 删除知识条目
// 思路：软删除知识条目
// 使用例子：err := service.DeleteKnowledge(id)
func (s *KnowledgeService) DeleteKnowledge(id uint) error {
	result := s.db.Delete(&models.Knowledge{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除知识条目失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("知识条目不存在")
	}

	return nil
}

// ListKnowledge 获取知识条目列表
// 思路：分页获取知识条目列表，支持分类过滤
// 使用例子：knowledge, total, err := service.ListKnowledge(category, limit, offset)
func (s *KnowledgeService) ListKnowledge(category string, limit, offset int) ([]models.Knowledge, int64, error) {
	var knowledge []models.Knowledge
	var total int64

	query := s.db.Model(&models.Knowledge{}).Where("status = ?", "active")

	// 分类过滤
	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取知识条目总数失败: %w", err)
	}

	// 获取列表
	if err := query.Order("priority DESC, created_at DESC").
		Limit(limit).Offset(offset).Find(&knowledge).Error; err != nil {
		return nil, 0, fmt.Errorf("获取知识条目列表失败: %w", err)
	}

	return knowledge, total, nil
}

// SearchKnowledge 搜索知识库
// 思路：基于关键词搜索知识库，返回相似度排序的结果
// 使用例子：results, err := service.SearchKnowledge(query, limit)
func (s *KnowledgeService) SearchKnowledge(query string, limit int) ([]KnowledgeSearchResult, error) {
	var knowledge []models.Knowledge

	// 简单的关键词搜索（实际项目中可以使用更复杂的搜索算法）
	searchQuery := "%" + strings.ToLower(query) + "%"

	err := s.db.Where("status = ? AND is_public = ?", "active", true).
		Where("LOWER(title) LIKE ? OR LOWER(content) LIKE ? OR LOWER(keywords) LIKE ?",
			searchQuery, searchQuery, searchQuery).
		Order("priority DESC, use_count DESC").
		Limit(limit).
		Find(&knowledge).Error

	if err != nil {
		return nil, fmt.Errorf("搜索知识库失败: %w", err)
	}

	// 计算相似度分数（简化实现）
	results := make([]KnowledgeSearchResult, len(knowledge))
	for i, kb := range knowledge {
		score := s.calculateSimilarity(query, kb)
		results[i] = KnowledgeSearchResult{
			Knowledge: kb,
			Score:     score,
		}
	}

	return results, nil
}

// calculateSimilarity 计算相似度分数
// 思路：简单的相似度计算，实际项目中可以使用更复杂的算法
func (s *KnowledgeService) calculateSimilarity(query string, knowledge models.Knowledge) float64 {
	query = strings.ToLower(query)
	title := strings.ToLower(knowledge.Title)
	content := strings.ToLower(knowledge.Content)
	keywords := strings.ToLower(knowledge.Keywords)

	score := 0.0

	// 标题匹配权重最高
	if strings.Contains(title, query) {
		score += 0.5
	}

	// 关键词匹配
	if strings.Contains(keywords, query) {
		score += 0.3
	}

	// 内容匹配
	if strings.Contains(content, query) {
		score += 0.2
	}

	// 根据优先级和使用次数调整分数
	score += float64(knowledge.Priority) * 0.01
	score += float64(knowledge.UseCount) * 0.001

	return score
}

// GetCategories 获取所有分类
// 思路：获取知识库中所有的分类列表
// 使用例子：categories, err := service.GetCategories()
func (s *KnowledgeService) GetCategories() ([]string, error) {
	var categories []string

	err := s.db.Model(&models.Knowledge{}).
		Where("status = ? AND category != ''", "active").
		Distinct("category").
		Pluck("category", &categories).Error

	if err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}

	return categories, nil
}

// Search 知识库搜索（接口实现）
// 思路：为Lua脚本提供知识库搜索接口
func (s *KnowledgeService) Search(ctx context.Context, query string, limit int) ([]map[string]interface{}, error) {
	// 调用现有的搜索方法
	results, err := s.SearchKnowledge(query, limit)
	if err != nil {
		return nil, err
	}

	// 转换为通用格式
	var searchResults []map[string]interface{}
	for _, result := range results {
		searchResults = append(searchResults, map[string]interface{}{
			"id":       result.Knowledge.ID,
			"title":    result.Knowledge.Title,
			"content":  result.Knowledge.Content,
			"category": result.Knowledge.Category,
			"score":    result.Score,
		})
	}

	return searchResults, nil
}
