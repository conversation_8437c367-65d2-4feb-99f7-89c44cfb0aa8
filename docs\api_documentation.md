# AI客服系统 API 文档

## 概述

AI客服系统提供了完整的RESTful API接口，支持消息处理、知识库管理、平台集成和系统监控等功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: `v1`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

目前API支持以下认证方式：

### 1. API Key认证
```http
X-API-Key: your-api-key-here
```

### 2. Bearer Token认证
```http
Authorization: Bearer your-token-here
```

### 3. 平台签名认证
```http
X-Platform: platform-name
X-Timestamp: unix-timestamp
X-Signature: hmac-sha256-signature
```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "error": true,
  "code": "ERROR_CODE",
  "message": "错误描述",
  "timestamp": "2023-12-31T16:00:00Z",
  "request_id": "req_1234567890"
}
```

## API接口

### 1. 健康检查

#### 基础健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2023-12-31T16:00:00Z"
  }
}
```

#### 详细健康检查
```http
GET /api/v1/monitor/health/detailed
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2023-12-31T16:00:00Z",
    "version": "1.0.0",
    "uptime": "24h30m15s",
    "database": {
      "status": "healthy",
      "type": "sqlite"
    },
    "adapters": {
      "total": 1,
      "running": 0,
      "errors": 0
    },
    "websocket": {
      "status": "healthy",
      "clients": 5
    },
    "metrics": {
      "total_requests": 1250,
      "active_requests": 3,
      "error_rate": 0.8,
      "avg_response_time": "45ms"
    }
  }
}
```

### 2. 消息处理

#### 发送消息
```http
POST /api/v1/messages/send
```

**请求参数**:
```json
{
  "user_id": "user123",           // 用户ID
  "platform": "qq",              // 平台名称
  "content": "你好，工作时间是什么？", // 消息内容
  "type": "text",                 // 消息类型
  "session_id": "session123"      // 会话ID（可选）
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "content": "我们的工作时间是周一至周五 9:00-18:00",
    "type": "text",
    "direction": "outgoing",
    "source": "knowledge",
    "timestamp": "2023-12-31T16:00:00Z",
    "session_id": "session123"
  }
}
```

#### 获取消息历史
```http
GET /api/v1/messages/history?user_id=user123&limit=20&offset=0
```

**查询参数**:
- `user_id`: 用户ID（必需）
- `limit`: 返回数量限制（默认20，最大100）
- `offset`: 偏移量（默认0）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 123,
        "content": "你好",
        "type": "text",
        "direction": "incoming",
        "timestamp": "2023-12-31T16:00:00Z"
      }
    ],
    "total": 50,
    "limit": 20,
    "offset": 0
  }
}
```

### 3. 知识库管理

#### 创建知识库条目
```http
POST /api/v1/knowledge/
```

**请求参数**:
```json
{
  "title": "工作时间",
  "content": "我们的工作时间是周一至周五 9:00-18:00",
  "keywords": "工作时间,上班时间,营业时间",
  "category": "基础信息",
  "status": "active",
  "created_by": "admin"
}
```

#### 获取知识库列表
```http
GET /api/v1/knowledge/?limit=20&offset=0&category=基础信息&status=active
```

**查询参数**:
- `limit`: 返回数量限制（默认20）
- `offset`: 偏移量（默认0）
- `category`: 分类过滤（可选）
- `status`: 状态过滤（可选）

#### 更新知识库条目
```http
PUT /api/v1/knowledge/{id}
```

#### 删除知识库条目
```http
DELETE /api/v1/knowledge/{id}
```

#### 搜索知识库
```http
POST /api/v1/knowledge/search
```

**请求参数**:
```json
{
  "query": "工作时间",
  "limit": 10
}
```

### 4. 平台管理

#### 获取平台列表
```http
GET /api/v1/platforms/
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "platforms": [
      {
        "name": "qq",
        "enabled": true,
        "status": "stopped",
        "config": {
          "base_url": "http://localhost:3000"
        }
      }
    ]
  }
}
```

#### 获取平台统计
```http
GET /api/v1/platforms/stats
```

#### 平台WebHook
```http
POST /api/v1/platforms/{platform}/webhook
```

#### 发送平台消息
```http
POST /api/v1/platforms/{platform}/send
```

**请求参数**:
```json
{
  "platform_user_id": "123456789",
  "platform_chat_id": "987654321",
  "content": "你好，这是来自客服系统的消息",
  "message_type": "text"
}
```

### 5. WebSocket实时通信

#### 连接WebSocket
```
ws://localhost:8080/ws?user_id=user123&platform=web
```

#### 消息格式
```json
{
  "type": "message",
  "data": {
    "content": "消息内容",
    "timestamp": "2023-12-31T16:00:00Z"
  }
}
```

#### WebSocket管理API

##### 获取连接客户端
```http
GET /api/v1/ws/clients
```

##### 广播消息
```http
POST /api/v1/ws/broadcast
```

##### 发送消息给指定客户端
```http
POST /api/v1/ws/send
```

### 6. 系统监控

#### 获取系统指标
```http
GET /api/v1/monitor/metrics
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_requests": 1250,
    "active_requests": 3,
    "total_errors": 10,
    "error_rate": 0.8,
    "uptime": "24h30m15s",
    "avg_response_time": "45ms",
    "p95_response_time": "120ms",
    "p99_response_time": "250ms",
    "status_codes": {
      "200": 1200,
      "400": 8,
      "404": 2,
      "500": 0
    },
    "methods": {
      "GET": 800,
      "POST": 400,
      "PUT": 30,
      "DELETE": 20
    },
    "last_updated": "2023-12-31T16:00:00Z"
  }
}
```

#### 获取路径指标
```http
GET /api/v1/monitor/metrics/paths
```

#### 重置指标
```http
POST /api/v1/monitor/metrics/reset
```

## 错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|-----------|------|
| `INVALID_PARAM` | 400 | 参数验证失败 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 禁止访问 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率过高 |
| `INTERNAL_SERVER_ERROR` | 500 | 服务器内部错误 |
| `EXTERNAL_SERVICE_ERROR` | 502 | 外部服务错误 |

## 限流规则

| 维度 | 限制 | 时间窗口 |
|------|------|----------|
| 全局 | 1000请求 | 1分钟 |
| 单IP | 100请求 | 1分钟 |
| 单用户 | 200请求 | 1分钟 |
| 发送消息 | 10请求 | 1分钟 |
| 搜索知识库 | 30请求 | 1分钟 |

## SDK和示例

### cURL示例

#### 发送消息
```bash
curl -X POST http://localhost:8080/api/v1/messages/send \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "platform": "qq",
    "content": "工作时间是什么？",
    "type": "text"
  }'
```

#### 搜索知识库
```bash
curl -X POST http://localhost:8080/api/v1/knowledge/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "工作时间",
    "limit": 10
  }'
```

### JavaScript示例

```javascript
// 发送消息
async function sendMessage(userId, platform, content) {
  const response = await fetch('/api/v1/messages/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user_id: userId,
      platform: platform,
      content: content,
      type: 'text'
    })
  });
  
  return await response.json();
}

// WebSocket连接
const ws = new WebSocket('ws://localhost:8080/ws?user_id=user123&platform=web');

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('收到消息:', message);
};

ws.send(JSON.stringify({
  type: 'message',
  data: {
    content: '你好',
    timestamp: new Date().toISOString()
  }
}));
```

## 版本历史

### v1.0.0 (2023-12-31)
- 初始版本发布
- 支持基础消息处理
- 知识库管理功能
- QQ平台适配器
- WebSocket实时通信
- 系统监控和指标

## 支持

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 文档：https://docs.example.com
- GitHub：https://github.com/example/aike_go

## 相关文档

- [部署指南](deployment_guide.md)
- [QQ适配器使用指南](qq_adapter_guide.md)
- [开发指南](development_guide.md)
