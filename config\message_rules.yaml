# 消息响应规则配置
# 思路：定义哪些消息需要AI客服响应，哪些需要忽略

# 全局开关
global:
  enabled: true
  debug_mode: true  # 调试模式，会记录所有过滤决策

# 私聊消息规则
private_message:
  enabled: true

  # 白名单用户（总是响应）
  whitelist_users:
    - "123456789"    # 管理员QQ号
    - "2700721092"    # 测试用户QQ号

  # 黑名单用户（永不响应）
  blacklist_users:
    - "111111111"    # 垃圾用户
    - "222222222"    # 骚扰用户

  # 响应条件（满足任一条件即响应）
  response_conditions:
    # 1. 包含关键词
    keywords:
      - "客服"
      - "帮助"
      - "help"
      - "问题"
      - "咨询"
      - "售后"
      - "投诉"

    # 2. 以特定符号开头（如@机器人）
    prefixes:
      - "@"
      - "/"
      - "!"

    # 3. 消息长度超过阈值（认为是认真咨询）
    min_length: 10

    # 4. 包含问号（可能是问题）
    contains_question: true

# 群聊消息规则
group_message:
  enabled: true

  # 白名单群（总是响应）
  whitelist_groups:
    - "100001"       # 客服群
    - "100002"       # 技术支持群

  # 黑名单群（永不响应）
  blacklist_groups:
    - "999999"       # 水群

  # 响应条件（必须满足所有条件）
  response_conditions:
    # 1. 必须@机器人或包含机器人昵称
    mention_required: true
    bot_names:
      - "AI客服"
      - "小助手"
      - "机器人"

    # 2. 包含关键词
    keywords:
      - "客服"
      - "帮助"
      - "问题"
      - "bug"
      - "故障"

    # 3. 不响应闲聊内容
    ignore_keywords:
      - "哈哈"
      - "呵呵"
      - "666"
      - "牛逼"
      - "厉害"

# 时间规则
time_rules:
  # 工作时间内更积极响应
  work_hours:
    enabled: true
    start_hour: 9
    end_hour: 18
    weekdays_only: true

    # 工作时间内降低响应阈值
    relaxed_conditions: true

  # 非工作时间只响应紧急情况
  off_hours:
    enabled: true
    urgent_keywords:
      - "紧急"
      - "urgent"
      - "故障"
      - "bug"
      - "问题"

# 频率限制
rate_limit:
  enabled: true

  # 每个用户的限制
  per_user:
    max_messages: 10      # 每小时最多响应10条消息
    time_window: 3600     # 时间窗口（秒）
    cooldown: 60          # 冷却时间（秒）

  # 每个群的限制
  per_group:
    max_messages: 20      # 每小时最多响应20条消息
    time_window: 3600
    cooldown: 30

# 内容过滤
content_filter:
  enabled: true

  # 忽略的消息类型
  ignore_types:
    - "image"            # 忽略图片
    - "voice"            # 忽略语音
    - "video"            # 忽略视频
    - "file"             # 忽略文件

  # 忽略过短的消息
  min_length: 2

  # 忽略纯表情/符号
  ignore_patterns:
    - "^[😀-🙏]+$"        # 纯emoji
    - "^[。，！？]+$"      # 纯标点
    - "^[0-9]+$"          # 纯数字
    - "^[a-zA-Z]+$"       # 纯字母（除非是help等关键词）

# 智能判断
smart_detection:
  enabled: true

  # AI判断是否需要响应（可选功能）
  ai_filter:
    enabled: false
    model: "gpt-3.5-turbo"
    prompt: "判断以下消息是否需要客服响应（回答yes或no）："

  # 基于历史对话判断
  context_aware:
    enabled: true
    consider_history: true
    max_history: 5        # 考虑最近5条消息

# 特殊规则
special_rules:
  # 新用户更容易触发响应
  new_user_bonus:
    enabled: true
    days_threshold: 7     # 7天内的新用户
    response_boost: 0.3   # 降低30%的响应阈值

  # VIP用户特殊处理
  vip_users:
    enabled: true
    users:
      - "888888888"
    always_respond: true

  # 管理员命令
  admin_commands:
    enabled: true
    admins:
      - "123456789"
    commands:
      - "!enable"         # 启用响应
      - "!disable"        # 禁用响应
      - "!status"         # 查看状态
      - "!reload"         # 重载配置
