{"url": "/platforms", "schema": {"type": "page", "title": "平台管理", "body": {"type": "crud", "api": {"method": "get", "url": "/api/v1/platforms", "adaptor": "return {data: payload.data.platforms || [], total: payload.data?.total_adapters || 0};"}, "saveOrderApi": {"method": "put", "url": "/api/v1/platforms/batch-update"}, "headerToolbar": [{"type": "button", "label": "新增平台", "level": "primary", "actionType": "dialog", "dialog": {"title": "新增平台配置", "size": "lg", "body": {"type": "form", "api": "post:/api/v1/platforms", "body": [{"type": "input-text", "name": "platform", "label": "平台名称", "required": true, "placeholder": "如：QQ机器人、微信客服等"}, {"type": "select", "name": "platform", "label": "平台类型", "required": true, "options": [{"label": "qq", "value": "PLATFORM_TYPE_QQ"}, {"label": "微信", "value": "PLATFORM_TYPE_WECHAT"}, {"label": "Telegram", "value": "PLATFORM_TYPE_TELEGRAM"}, {"label": "千牛", "value": "PLATFORM_TYPE_QIANNIU"}]}, {"type": "input-url", "name": "endpoint", "label": "端点地址", "required": true, "placeholder": "WebSocket或HTTP端点地址"}, {"type": "input-text", "name": "token", "label": "访问令牌", "placeholder": "平台API访问令牌"}, {"type": "json-editor", "name": "config", "label": "配置参数", "placeholder": "JSON格式的平台特定配置"}, {"type": "switch", "name": "enabled", "label": "启用平台", "value": true}]}}}, {"type": "button", "label": "批量操作", "level": "info", "actionType": "dropdown", "buttons": [{"type": "button", "label": "批量启用", "actionType": "ajax", "api": "put:/api/v1/platforms/batch-enable", "confirmText": "确定要启用选中的平台吗？"}, {"type": "button", "label": "批量禁用", "actionType": "ajax", "api": "put:/api/v1/platforms/batch-disable", "confirmText": "确定要禁用选中的平台吗？"}, {"type": "button", "label": "批量测试连接", "actionType": "ajax", "api": "post:/api/v1/platforms/batch-test", "confirmText": "确定要测试选中平台的连接吗？"}]}], "columns": [{"type": "checkbox", "name": "id", "label": "", "width": 50}, {"name": "id", "label": "ID", "width": 80, "sortable": true}, {"name": "platform", "label": "平台名称", "searchable": true}, {"name": "type", "label": "平台类型", "width": 120, "type": "mapping", "map": {"PLATFORM_TYPE_QQ": "<span class='label label-info'>QQ</span>", "PLATFORM_TYPE_WECHAT": "<span class='label label-success'>微信</span>", "PLATFORM_TYPE_TELEGRAM": "<span class='label label-primary'>Telegram</span>", "PLATFORM_TYPE_QIANNIU": "<span class='label label-warning'>千牛</span>"}}, {"name": "connection_status", "label": "连接状态", "width": 120, "type": "mapping", "map": {"PLATFORM_STATUS_CONNECTED": "<span class='label label-success'>已连接</span>", "disconnected": "<span class='label label-danger'>已断开</span>", "PLATFORM_STATUS_CONNECTING": "<span class='label label-warning'>连接中</span>", "PLATFORM_STATUS_ERROR": "<span class='label label-danger'>错误</span>"}}, {"name": "status", "label": "启用状态", "width": 100, "type": "mapping", "map": {"true": "<span class='label label-success'>已启用</span>", "stopped": "<span class='label label-default'>已禁用</span>"}}, {"name": "endpoint", "label": "端点地址", "type": "tpl", "tpl": "${endpoint | truncate:30}"}, {"name": "message_count", "label": "消息数", "width": 100, "sortable": true}, {"name": "last_active_at", "label": "最后活跃", "width": 180, "type": "datetime", "sortable": true}, {"name": "created_at", "label": "创建时间", "width": 180, "type": "datetime", "sortable": true}, {"type": "operation", "label": "操作", "width": 250, "buttons": [{"type": "button", "label": "查看", "level": "link", "actionType": "dialog", "dialog": {"title": "平台详情", "size": "lg", "body": {"type": "service", "api": "get:/api/v1/platforms/${id}", "body": [{"type": "property", "column": 2, "items": [{"label": "平台ID", "content": "${id}"}, {"label": "平台名称", "content": "${name}"}, {"label": "平台类型", "content": "${type}"}, {"label": "连接状态", "content": "${status}"}, {"label": "启用状态", "content": "${enabled ? '已启用' : '已禁用'}"}, {"label": "端点地址", "content": "${endpoint}"}, {"label": "访问令牌", "content": "${token ? '已设置' : '未设置'}"}, {"label": "消息数", "content": "${message_count}"}, {"label": "最后活跃", "content": "${last_active_at}"}, {"label": "创建时间", "content": "${created_at}"}, {"label": "更新时间", "content": "${updated_at}"}]}, {"type": "divider"}, {"type": "static", "label": "配置参数", "value": "${config}", "className": "bg-light p-3", "style": {"fontFamily": "monospace", "whiteSpace": "pre-wrap"}}]}}}, {"type": "button", "label": "编辑", "level": "link", "actionType": "dialog", "dialog": {"title": "编辑平台配置", "size": "lg", "body": {"type": "form", "api": "put:/api/v1/platforms/${id}", "initApi": "get:/api/v1/platforms/${id}", "body": [{"type": "input-text", "name": "name", "label": "平台名称", "required": true}, {"type": "select", "name": "type", "label": "平台类型", "required": true, "options": [{"label": "QQ", "value": "PLATFORM_TYPE_QQ"}, {"label": "微信", "value": "PLATFORM_TYPE_WECHAT"}, {"label": "Telegram", "value": "PLATFORM_TYPE_TELEGRAM"}, {"label": "千牛", "value": "PLATFORM_TYPE_QIANNIU"}]}, {"type": "input-url", "name": "endpoint", "label": "端点地址", "required": true}, {"type": "input-text", "name": "token", "label": "访问令牌"}, {"type": "json-editor", "name": "config", "label": "配置参数"}, {"type": "switch", "name": "enabled", "label": "启用平台"}]}}}, {"type": "button", "label": "测试连接", "level": "link", "className": "text-info", "actionType": "ajax", "api": "post:/api/v1/platforms/${id}/test", "confirmText": "确定要测试该平台的连接吗？"}, {"type": "button", "label": "${enabled ? '禁用' : '启用'}", "level": "link", "className": "${enabled ? 'text-warning' : 'text-success'}", "actionType": "ajax", "confirmText": "${enabled ? '确定要禁用该平台吗？' : '确定要启用该平台吗？'}", "api": "put:/api/v1/platforms/${id}/toggle"}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "actionType": "ajax", "confirmText": "确定要删除该平台吗？此操作不可恢复！", "api": "delete:/api/v1/platforms/${id}"}]}]}}}