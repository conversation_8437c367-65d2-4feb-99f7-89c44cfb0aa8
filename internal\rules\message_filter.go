package rules

import (
	"fmt"
	"log"
	"regexp"
	"strings"
	"sync"
	"time"

	"aike_go/internal/interfaces"

	"gopkg.in/yaml.v2"
)

// MessageRules 消息规则配置
// 思路：从YAML配置文件加载的完整规则结构
type MessageRules struct {
	Global struct {
		Enabled   bool `yaml:"enabled"`
		DebugMode bool `yaml:"debug_mode"`
	} `yaml:"global"`

	PrivateMessage struct {
		Enabled            bool     `yaml:"enabled"`
		WhitelistUsers     []string `yaml:"whitelist_users"`
		BlacklistUsers     []string `yaml:"blacklist_users"`
		ResponseConditions struct {
			Keywords         []string `yaml:"keywords"`
			Prefixes         []string `yaml:"prefixes"`
			MinLength        int      `yaml:"min_length"`
			ContainsQuestion bool     `yaml:"contains_question"`
		} `yaml:"response_conditions"`
	} `yaml:"private_message"`

	GroupMessage struct {
		Enabled            bool     `yaml:"enabled"`
		WhitelistGroups    []string `yaml:"whitelist_groups"`
		BlacklistGroups    []string `yaml:"blacklist_groups"`
		ResponseConditions struct {
			MentionRequired bool     `yaml:"mention_required"`
			BotNames        []string `yaml:"bot_names"`
			Keywords        []string `yaml:"keywords"`
			IgnoreKeywords  []string `yaml:"ignore_keywords"`
		} `yaml:"response_conditions"`
	} `yaml:"group_message"`

	TimeRules struct {
		WorkHours struct {
			Enabled           bool `yaml:"enabled"`
			StartHour         int  `yaml:"start_hour"`
			EndHour           int  `yaml:"end_hour"`
			WeekdaysOnly      bool `yaml:"weekdays_only"`
			RelaxedConditions bool `yaml:"relaxed_conditions"`
		} `yaml:"work_hours"`
		OffHours struct {
			Enabled        bool     `yaml:"enabled"`
			UrgentKeywords []string `yaml:"urgent_keywords"`
		} `yaml:"off_hours"`
	} `yaml:"time_rules"`

	RateLimit struct {
		Enabled bool `yaml:"enabled"`
		PerUser struct {
			MaxMessages int `yaml:"max_messages"`
			TimeWindow  int `yaml:"time_window"`
			Cooldown    int `yaml:"cooldown"`
		} `yaml:"per_user"`
		PerGroup struct {
			MaxMessages int `yaml:"max_messages"`
			TimeWindow  int `yaml:"time_window"`
			Cooldown    int `yaml:"cooldown"`
		} `yaml:"per_group"`
	} `yaml:"rate_limit"`

	ContentFilter struct {
		Enabled        bool     `yaml:"enabled"`
		IgnoreTypes    []string `yaml:"ignore_types"`
		MinLength      int      `yaml:"min_length"`
		IgnorePatterns []string `yaml:"ignore_patterns"`
	} `yaml:"content_filter"`
}

// MessageFilter 消息过滤器
// 思路：根据规则判断消息是否需要响应
type MessageFilter struct {
	rules       *MessageRules
	rateTracker map[string]*RateTracker
	mutex       sync.RWMutex
}

// RateTracker 频率跟踪器
// 思路：跟踪用户/群组的消息频率
type RateTracker struct {
	Messages  []time.Time
	LastReply time.Time
}

// FilterResult 过滤结果
// 思路：包含是否响应的决策和原因
type FilterResult struct {
	ShouldRespond bool   `json:"should_respond"`
	Reason        string `json:"reason"`
	Score         int    `json:"score"`      // 响应分数（0-100）
	DebugInfo     string `json:"debug_info"` // 调试信息
}

// NewMessageFilter 创建消息过滤器
// 思路：加载配置文件并初始化过滤器
func NewMessageFilter(configData []byte) (*MessageFilter, error) {
	var rules MessageRules
	if err := yaml.Unmarshal(configData, &rules); err != nil {
		return nil, fmt.Errorf("解析规则配置失败: %w", err)
	}

	return &MessageFilter{
		rules:       &rules,
		rateTracker: make(map[string]*RateTracker),
	}, nil
}

// ShouldRespond 判断是否应该响应消息
// 思路：综合所有规则进行判断
func (f *MessageFilter) ShouldRespond(message *interfaces.IncomingMessage) *FilterResult {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	result := &FilterResult{
		ShouldRespond: false,
		Score:         0,
		DebugInfo:     "",
	}

	// 1. 全局开关检查
	if !f.rules.Global.Enabled {
		result.Reason = "全局响应已禁用"
		return result
	}

	// 2. 内容过滤
	if !f.checkContentFilter(message, result) {
		return result
	}

	// 3. 黑白名单检查
	if !f.checkBlackWhiteList(message, result) {
		return result
	}

	// 4. 频率限制检查
	if !f.checkRateLimit(message, result) {
		return result
	}

	// 5. 时间规则检查
	timeScore := f.checkTimeRules(message, result)
	result.Score += timeScore

	// 6. 消息类型特定规则
	var typeScore int
	if message.Platform == "qq" {
		groupID := message.Metadata["group_id"]
		if groupID != "" && groupID != "0" {
			// 群消息
			typeScore = f.checkGroupMessageRules(message, result)
		} else {
			// 私聊消息
			typeScore = f.checkPrivateMessageRules(message, result)
		}
	}
	result.Score += typeScore

	// 7. 综合判断
	threshold := 50 // 基础阈值
	if f.rules.TimeRules.WorkHours.Enabled && f.rules.TimeRules.WorkHours.RelaxedConditions && f.isWorkHours() {
		threshold = 30 // 工作时间降低阈值
	}

	result.ShouldRespond = result.Score >= threshold

	if result.ShouldRespond {
		result.Reason = fmt.Sprintf("响应分数 %d >= 阈值 %d", result.Score, threshold)
		f.updateRateTracker(message)
	} else {
		result.Reason = fmt.Sprintf("响应分数 %d < 阈值 %d", result.Score, threshold)
	}

	if f.rules.Global.DebugMode {
		log.Printf("消息过滤结果: %+v", result)
	}

	return result
}

// checkContentFilter 检查内容过滤规则
// 思路：过滤不合适的消息内容
func (f *MessageFilter) checkContentFilter(message *interfaces.IncomingMessage, result *FilterResult) bool {
	if !f.rules.ContentFilter.Enabled {
		return true
	}

	// 检查消息类型
	for _, ignoreType := range f.rules.ContentFilter.IgnoreTypes {
		if message.MessageType == ignoreType {
			result.Reason = fmt.Sprintf("忽略消息类型: %s", ignoreType)
			return false
		}
	}

	// 检查消息长度
	if len(message.Content) < f.rules.ContentFilter.MinLength {
		result.Reason = fmt.Sprintf("消息过短: %d < %d", len(message.Content), f.rules.ContentFilter.MinLength)
		return false
	}

	// 检查忽略模式
	for _, pattern := range f.rules.ContentFilter.IgnorePatterns {
		if matched, _ := regexp.MatchString(pattern, message.Content); matched {
			result.Reason = fmt.Sprintf("匹配忽略模式: %s", pattern)
			return false
		}
	}

	result.DebugInfo += "内容过滤通过; "
	return true
}

// checkBlackWhiteList 检查黑白名单
// 思路：优先检查黑白名单
func (f *MessageFilter) checkBlackWhiteList(message *interfaces.IncomingMessage, result *FilterResult) bool {
	userID := message.From.PlatformUserID
	groupID := message.Metadata["group_id"]

	// 检查用户黑名单
	for _, blackUser := range f.rules.PrivateMessage.BlacklistUsers {
		if userID == blackUser {
			result.Reason = fmt.Sprintf("用户在黑名单: %s", userID)
			return false
		}
	}

	// 检查群黑名单
	if groupID != "" {
		for _, blackGroup := range f.rules.GroupMessage.BlacklistGroups {
			if groupID == blackGroup {
				result.Reason = fmt.Sprintf("群组在黑名单: %s", groupID)
				return false
			}
		}
	}

	// 检查用户白名单（直接通过）
	for _, whiteUser := range f.rules.PrivateMessage.WhitelistUsers {
		if userID == whiteUser {
			result.ShouldRespond = true
			result.Score = 100
			result.Reason = fmt.Sprintf("用户在白名单: %s", userID)
			return true
		}
	}

	// 检查群白名单（加分）
	if groupID != "" {
		for _, whiteGroup := range f.rules.GroupMessage.WhitelistGroups {
			if groupID == whiteGroup {
				result.Score += 50
				result.DebugInfo += fmt.Sprintf("群组在白名单: %s; ", groupID)
				break
			}
		}
	}

	return true
}

// checkRateLimit 检查频率限制
// 思路：防止过于频繁的响应
func (f *MessageFilter) checkRateLimit(message *interfaces.IncomingMessage, result *FilterResult) bool {
	if !f.rules.RateLimit.Enabled {
		return true
	}

	userID := message.From.PlatformUserID
	groupID := message.Metadata["group_id"]

	// 检查用户频率限制
	if !f.checkUserRateLimit(userID, result) {
		return false
	}

	// 检查群组频率限制
	if groupID != "" && !f.checkGroupRateLimit(groupID, result) {
		return false
	}

	return true
}

// checkUserRateLimit 检查用户频率限制
func (f *MessageFilter) checkUserRateLimit(userID string, result *FilterResult) bool {
	key := "user:" + userID
	tracker := f.getRateTracker(key)

	now := time.Now()
	windowStart := now.Add(-time.Duration(f.rules.RateLimit.PerUser.TimeWindow) * time.Second)

	// 清理过期记录
	var validMessages []time.Time
	for _, msgTime := range tracker.Messages {
		if msgTime.After(windowStart) {
			validMessages = append(validMessages, msgTime)
		}
	}
	tracker.Messages = validMessages

	// 检查消息数量限制
	if len(tracker.Messages) >= f.rules.RateLimit.PerUser.MaxMessages {
		result.Reason = fmt.Sprintf("用户 %s 超过频率限制: %d/%d", userID, len(tracker.Messages), f.rules.RateLimit.PerUser.MaxMessages)
		return false
	}

	// 检查冷却时间
	if !tracker.LastReply.IsZero() {
		cooldownEnd := tracker.LastReply.Add(time.Duration(f.rules.RateLimit.PerUser.Cooldown) * time.Second)
		if now.Before(cooldownEnd) {
			result.Reason = fmt.Sprintf("用户 %s 在冷却期内", userID)
			return false
		}
	}

	return true
}

// checkGroupRateLimit 检查群组频率限制
func (f *MessageFilter) checkGroupRateLimit(groupID string, result *FilterResult) bool {
	key := "group:" + groupID
	tracker := f.getRateTracker(key)

	now := time.Now()
	windowStart := now.Add(-time.Duration(f.rules.RateLimit.PerGroup.TimeWindow) * time.Second)

	// 清理过期记录
	var validMessages []time.Time
	for _, msgTime := range tracker.Messages {
		if msgTime.After(windowStart) {
			validMessages = append(validMessages, msgTime)
		}
	}
	tracker.Messages = validMessages

	// 检查消息数量限制
	if len(tracker.Messages) >= f.rules.RateLimit.PerGroup.MaxMessages {
		result.Reason = fmt.Sprintf("群组 %s 超过频率限制: %d/%d", groupID, len(tracker.Messages), f.rules.RateLimit.PerGroup.MaxMessages)
		return false
	}

	// 检查冷却时间
	if !tracker.LastReply.IsZero() {
		cooldownEnd := tracker.LastReply.Add(time.Duration(f.rules.RateLimit.PerGroup.Cooldown) * time.Second)
		if now.Before(cooldownEnd) {
			result.Reason = fmt.Sprintf("群组 %s 在冷却期内", groupID)
			return false
		}
	}

	return true
}

// getRateTracker 获取频率跟踪器
func (f *MessageFilter) getRateTracker(key string) *RateTracker {
	if tracker, exists := f.rateTracker[key]; exists {
		return tracker
	}

	tracker := &RateTracker{
		Messages: make([]time.Time, 0),
	}
	f.rateTracker[key] = tracker
	return tracker
}

// updateRateTracker 更新频率跟踪器
func (f *MessageFilter) updateRateTracker(message *interfaces.IncomingMessage) {
	now := time.Now()
	userID := message.From.PlatformUserID
	groupID := message.Metadata["group_id"]

	// 更新用户跟踪器
	userKey := "user:" + userID
	userTracker := f.getRateTracker(userKey)
	userTracker.Messages = append(userTracker.Messages, now)
	userTracker.LastReply = now

	// 更新群组跟踪器
	if groupID != "" {
		groupKey := "group:" + groupID
		groupTracker := f.getRateTracker(groupKey)
		groupTracker.Messages = append(groupTracker.Messages, now)
		groupTracker.LastReply = now
	}
}

// checkTimeRules 检查时间规则
// 思路：根据当前时间调整响应策略
func (f *MessageFilter) checkTimeRules(message *interfaces.IncomingMessage, result *FilterResult) int {
	score := 0

	if f.isWorkHours() {
		// 工作时间内
		if f.rules.TimeRules.WorkHours.Enabled {
			score += 20
			result.DebugInfo += "工作时间内+20; "
		}
	} else {
		// 非工作时间
		if f.rules.TimeRules.OffHours.Enabled {
			// 检查是否包含紧急关键词
			content := strings.ToLower(message.Content)
			for _, keyword := range f.rules.TimeRules.OffHours.UrgentKeywords {
				if strings.Contains(content, keyword) {
					score += 30
					result.DebugInfo += fmt.Sprintf("非工作时间紧急关键词'%s'+30; ", keyword)
					break
				}
			}
		}
	}

	return score
}

// checkPrivateMessageRules 检查私聊消息规则
// 思路：私聊消息的特定判断逻辑
func (f *MessageFilter) checkPrivateMessageRules(message *interfaces.IncomingMessage, result *FilterResult) int {
	if !f.rules.PrivateMessage.Enabled {
		return 0
	}

	score := 0
	content := strings.ToLower(message.Content)

	// 检查关键词
	for _, keyword := range f.rules.PrivateMessage.ResponseConditions.Keywords {
		if strings.Contains(content, keyword) {
			score += 25
			result.DebugInfo += fmt.Sprintf("私聊关键词'%s'+25; ", keyword)
		}
	}

	// 检查前缀
	for _, prefix := range f.rules.PrivateMessage.ResponseConditions.Prefixes {
		if strings.HasPrefix(message.Content, prefix) {
			score += 30
			result.DebugInfo += fmt.Sprintf("私聊前缀'%s'+30; ", prefix)
			break
		}
	}

	// 检查消息长度
	if len(message.Content) >= f.rules.PrivateMessage.ResponseConditions.MinLength {
		score += 15
		result.DebugInfo += fmt.Sprintf("私聊长度>=%d+15; ", f.rules.PrivateMessage.ResponseConditions.MinLength)
	}

	// 检查是否包含问号
	if f.rules.PrivateMessage.ResponseConditions.ContainsQuestion && strings.Contains(message.Content, "?") || strings.Contains(message.Content, "？") {
		score += 20
		result.DebugInfo += "私聊包含问号+20; "
	}

	return score
}

// checkGroupMessageRules 检查群聊消息规则
// 思路：群聊消息的特定判断逻辑
func (f *MessageFilter) checkGroupMessageRules(message *interfaces.IncomingMessage, result *FilterResult) int {
	if !f.rules.GroupMessage.Enabled {
		return 0
	}

	score := 0
	content := strings.ToLower(message.Content)

	// 检查是否@机器人或提到机器人名称
	mentionFound := false
	if f.rules.GroupMessage.ResponseConditions.MentionRequired {
		// 检查@符号
		if strings.Contains(message.Content, "@") {
			mentionFound = true
		}

		// 检查机器人名称
		for _, botName := range f.rules.GroupMessage.ResponseConditions.BotNames {
			if strings.Contains(content, strings.ToLower(botName)) {
				mentionFound = true
				break
			}
		}

		if mentionFound {
			score += 40
			result.DebugInfo += "群聊@机器人+40; "
		} else {
			// 没有@机器人，直接返回0分
			result.DebugInfo += "群聊未@机器人，跳过; "
			return 0
		}
	}

	// 检查关键词
	for _, keyword := range f.rules.GroupMessage.ResponseConditions.Keywords {
		if strings.Contains(content, keyword) {
			score += 20
			result.DebugInfo += fmt.Sprintf("群聊关键词'%s'+20; ", keyword)
		}
	}

	// 检查忽略关键词
	for _, ignoreKeyword := range f.rules.GroupMessage.ResponseConditions.IgnoreKeywords {
		if strings.Contains(content, ignoreKeyword) {
			score -= 30
			result.DebugInfo += fmt.Sprintf("群聊忽略关键词'%s'-30; ", ignoreKeyword)
		}
	}

	return score
}

// isWorkHours 判断是否在工作时间
// 思路：根据配置判断当前是否为工作时间
func (f *MessageFilter) isWorkHours() bool {
	if !f.rules.TimeRules.WorkHours.Enabled {
		return true
	}

	now := time.Now()
	hour := now.Hour()
	weekday := now.Weekday()

	// 检查小时范围
	if hour < f.rules.TimeRules.WorkHours.StartHour || hour >= f.rules.TimeRules.WorkHours.EndHour {
		return false
	}

	// 检查是否只在工作日
	if f.rules.TimeRules.WorkHours.WeekdaysOnly {
		if weekday == time.Saturday || weekday == time.Sunday {
			return false
		}
	}

	return true
}

// ReloadRules 重新加载规则
// 思路：支持热重载配置
func (f *MessageFilter) ReloadRules(configData []byte) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	var newRules MessageRules
	if err := yaml.Unmarshal(configData, &newRules); err != nil {
		return fmt.Errorf("解析新规则配置失败: %w", err)
	}

	f.rules = &newRules
	log.Println("消息规则已重新加载")
	return nil
}

// GetStats 获取统计信息
// 思路：提供过滤器的运行统计
func (f *MessageFilter) GetStats() map[string]interface{} {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_trackers": len(f.rateTracker),
		"rules_enabled":  f.rules.Global.Enabled,
		"debug_mode":     f.rules.Global.DebugMode,
	}

	// 统计活跃用户和群组
	userCount := 0
	groupCount := 0
	for key := range f.rateTracker {
		if strings.HasPrefix(key, "user:") {
			userCount++
		} else if strings.HasPrefix(key, "group:") {
			groupCount++
		}
	}

	stats["active_users"] = userCount
	stats["active_groups"] = groupCount

	return stats
}
