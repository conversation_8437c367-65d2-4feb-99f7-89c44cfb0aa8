package server

import (
	"github.com/gin-gonic/gin"
)

// RouteManager 路由管理器
// 思路：将路由设置从主服务器文件中分离，便于维护和扩展
// 使用例子：routeManager := NewRouteManager(server); routeManager.SetupRoutes()
type RouteManager struct {
	server *RefactoredServer
	engine *gin.Engine
}

// NewRouteManager 创建路由管理器
// 思路：初始化路由管理器，关联服务器实例
// 使用例子：routeManager := NewRouteManager(server)
func NewRouteManager(server *RefactoredServer) *RouteManager {
	return &RouteManager{
		server: server,
		engine: server.engine,
	}
}

// SetupRoutes 设置所有路由
// 思路：模块化路由设置，便于维护和扩展
// 使用例子：routeManager.SetupRoutes()
func (rm *RouteManager) SetupRoutes() {
	// 静态文件路由
	rm.setupStaticRoutes()

	// 页面路由
	rm.setupPageRoutes()

	// 健康检查
	rm.engine.GET("/health", rm.server.healthCheck)

	// API路由组
	api := rm.engine.Group("/api/v1")
	{
		rm.setupMessageRoutes(api)
		rm.setupKnowledgeRoutes(api)
		rm.setupPlatformRoutes(api)
		rm.setupScriptRoutes(api)
		rm.setupWebSocketRoutes(api)
		rm.setupMonitorRoutes(api)
	}

	// WebSocket路由
	rm.engine.GET("/ws", rm.server.handleWebSocket)
}

// setupStaticRoutes 设置静态文件路由
// 思路：处理静态资源文件的访问
func (rm *RouteManager) setupStaticRoutes() {
	rm.engine.Static("/static", "./web/static")
}

// setupPageRoutes 设置页面路由
// 思路：处理前端页面的路由
func (rm *RouteManager) setupPageRoutes() {
	rm.engine.GET("/", rm.server.pageHandlers.IndexPage)
	rm.engine.GET("/admin", rm.server.pageHandlers.AdminPage)
	rm.engine.GET("/admin/*any", rm.server.pageHandlers.AdminPage)
	rm.engine.GET("/amis", rm.server.pageHandlers.AmisAdminPage)
	rm.engine.GET("/amis/*any", rm.server.pageHandlers.AmisAdminPage)
}

// setupMessageRoutes 设置消息相关路由
// 思路：处理消息发送和历史记录相关的API
func (rm *RouteManager) setupMessageRoutes(api *gin.RouterGroup) {
	messages := api.Group("/messages")
	{
		messages.POST("/send", rm.server.messageHandlers.SendMessage)         // 发送消息
		messages.GET("/history", rm.server.messageHandlers.GetMessageHistory) // 获取历史消息
	}
}

// setupKnowledgeRoutes 设置知识库相关路由
// 思路：处理知识库管理相关的API
func (rm *RouteManager) setupKnowledgeRoutes(api *gin.RouterGroup) {
	knowledge := api.Group("/knowledge")
	{
		knowledge.POST("/", rm.server.knowledgeHandlers.CreateKnowledge)       // 创建知识条目
		knowledge.GET("/", rm.server.knowledgeHandlers.ListKnowledge)          // 获取知识列表
		knowledge.PUT("/:id", rm.server.knowledgeHandlers.UpdateKnowledge)     // 更新知识条目
		knowledge.DELETE("/:id", rm.server.knowledgeHandlers.DeleteKnowledge)  // 删除知识条目
		knowledge.POST("/search", rm.server.knowledgeHandlers.SearchKnowledge) // 搜索知识库
	}
}

// setupPlatformRoutes 设置平台相关路由
// 思路：处理平台适配器管理相关的API
func (rm *RouteManager) setupPlatformRoutes(api *gin.RouterGroup) {
	platforms := api.Group("/platforms")
	{
		platforms.GET("/", rm.server.platformHandlers.ListPlatforms)                     // 获取平台列表
		platforms.GET("/stats", rm.server.platformHandlers.GetPlatformStats)             // 获取平台统计
		platforms.POST("/:platform/webhook", rm.server.platformHandlers.HandleWebhook)   // 处理平台回调
		platforms.POST("/:platform/send", rm.server.messageHandlers.SendPlatformMessage) // 发送平台消息
		platforms.POST("/:platform/restart", rm.server.platformHandlers.RestartPlatform) // 重启平台适配器
		platforms.GET("/:platform/config", rm.server.platformHandlers.GetPlatformConfig) // 获取平台配置
	}
}

// setupScriptRoutes 设置脚本相关路由
// 思路：处理Lua脚本管理相关的API
func (rm *RouteManager) setupScriptRoutes(api *gin.RouterGroup) {
	// 暂时注释掉脚本路由，因为ScriptHandler还未实现
	// scripts := api.Group("/scripts")
	// {
	//     scriptHandler := handlers.NewScriptHandler(rm.server.scriptService)
	//     scripts.POST("/", scriptHandler.CreateScript)             // 创建脚本
	//     scripts.GET("/", scriptHandler.ListScripts)               // 获取脚本列表
	//     scripts.GET("/:id", scriptHandler.GetScript)              // 获取脚本详情
	//     scripts.PUT("/:id", scriptHandler.UpdateScript)           // 更新脚本
	//     scripts.DELETE("/:id", scriptHandler.DeleteScript)        // 删除脚本
	//     scripts.POST("/:id/execute", scriptHandler.ExecuteScript) // 执行脚本
	//     scripts.GET("/types", scriptHandler.GetScriptTypes)       // 获取脚本类型
	// }
}

// setupWebSocketRoutes 设置WebSocket相关路由
// 思路：处理WebSocket连接和消息相关的API
func (rm *RouteManager) setupWebSocketRoutes(api *gin.RouterGroup) {
	ws := api.Group("/ws")
	{
		ws.GET("/clients", rm.server.websocketHandlers.GetWSClients)                    // 获取连接的客户端列表
		ws.POST("/broadcast", rm.server.websocketHandlers.BroadcastMessage)             // 广播消息
		ws.POST("/send", rm.server.websocketHandlers.SendWSMessage)                     // 发送消息给指定客户端
		ws.GET("/napcat", rm.server.websocketHandlers.HandleNapCatWebSocket)            // NapCat反向连接
		ws.GET("/stats", rm.server.websocketHandlers.GetWSStats)                        // 获取WebSocket统计
		ws.DELETE("/clients/:client_id", rm.server.websocketHandlers.CloseWSConnection) // 关闭连接
	}
}

// setupMonitorRoutes 设置监控相关路由
// 思路：处理系统监控和健康检查相关的API
func (rm *RouteManager) setupMonitorRoutes(api *gin.RouterGroup) {
	monitor := api.Group("/monitor")
	{
		monitor.GET("/metrics", rm.server.monitorHandlers.GetMetrics)                // 获取系统指标
		monitor.GET("/metrics/paths", rm.server.monitorHandlers.GetPathMetrics)      // 获取路径指标
		monitor.POST("/metrics/reset", rm.server.monitorHandlers.ResetMetrics)       // 重置指标
		monitor.GET("/health/detailed", rm.server.monitorHandlers.GetDetailedHealth) // 详细健康检查
	}
}
