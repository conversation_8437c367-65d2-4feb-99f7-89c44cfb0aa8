package server

import (
	"aike_go/internal/handlers"
	"github.com/gin-gonic/gin"
)

// RouteManager 路由管理器
// 思路：将路由设置从主服务器文件中分离，便于维护和扩展
// 使用例子：routeManager := NewRouteManager(server); routeManager.SetupRoutes()
type RouteManager struct {
	server *Server
	engine *gin.Engine
}

// NewRouteManager 创建路由管理器
// 思路：初始化路由管理器，关联服务器实例
// 使用例子：routeManager := NewRouteManager(server)
func NewRouteManager(server *Server) *RouteManager {
	return &RouteManager{
		server: server,
		engine: server.engine,
	}
}

// SetupRoutes 设置所有路由
// 思路：模块化路由设置，便于维护和扩展
// 使用例子：routeManager.SetupRoutes()
func (rm *RouteManager) SetupRoutes() {
	// 静态文件路由
	rm.setupStaticRoutes()
	
	// 页面路由
	rm.setupPageRoutes()
	
	// 健康检查
	rm.engine.GET("/health", rm.server.healthCheck)
	
	// API路由组
	api := rm.engine.Group("/api/v1")
	{
		rm.setupMessageRoutes(api)
		rm.setupKnowledgeRoutes(api)
		rm.setupPlatformRoutes(api)
		rm.setupScriptRoutes(api)
		rm.setupWebSocketRoutes(api)
		rm.setupMonitorRoutes(api)
	}
	
	// WebSocket路由
	rm.engine.GET("/ws", rm.server.handleWebSocket)
}

// setupStaticRoutes 设置静态文件路由
// 思路：处理静态资源文件的访问
func (rm *RouteManager) setupStaticRoutes() {
	rm.engine.Static("/static", "./web/static")
}

// setupPageRoutes 设置页面路由
// 思路：处理前端页面的路由
func (rm *RouteManager) setupPageRoutes() {
	rm.engine.GET("/", rm.server.indexPage)
	rm.engine.GET("/admin", rm.server.adminPage)
	rm.engine.GET("/admin/*any", rm.server.adminPage)
	rm.engine.GET("/amis", rm.server.amisAdminPage)
	rm.engine.GET("/amis/*any", rm.server.amisAdminPage)
}

// setupMessageRoutes 设置消息相关路由
// 思路：处理消息发送和历史记录相关的API
func (rm *RouteManager) setupMessageRoutes(api *gin.RouterGroup) {
	messages := api.Group("/messages")
	{
		messages.POST("/send", rm.server.sendMessage)         // 发送消息
		messages.GET("/history", rm.server.getMessageHistory) // 获取历史消息
	}
}

// setupKnowledgeRoutes 设置知识库相关路由
// 思路：处理知识库管理相关的API
func (rm *RouteManager) setupKnowledgeRoutes(api *gin.RouterGroup) {
	knowledge := api.Group("/knowledge")
	{
		knowledge.POST("/", rm.server.createKnowledge)       // 创建知识条目
		knowledge.GET("/", rm.server.listKnowledge)          // 获取知识列表
		knowledge.PUT("/:id", rm.server.updateKnowledge)     // 更新知识条目
		knowledge.DELETE("/:id", rm.server.deleteKnowledge)  // 删除知识条目
		knowledge.POST("/search", rm.server.searchKnowledge) // 搜索知识库
	}
}

// setupPlatformRoutes 设置平台相关路由
// 思路：处理平台适配器管理相关的API
func (rm *RouteManager) setupPlatformRoutes(api *gin.RouterGroup) {
	platforms := api.Group("/platforms")
	{
		platforms.GET("/", rm.server.listPlatforms)                      // 获取平台列表
		platforms.GET("/stats", rm.server.getPlatformStats)              // 获取平台统计
		platforms.POST("/:platform/webhook", rm.server.handleWebhook)    // 处理平台回调
		platforms.POST("/:platform/send", rm.server.sendPlatformMessage) // 发送平台消息
	}
}

// setupScriptRoutes 设置脚本相关路由
// 思路：处理Lua脚本管理相关的API
func (rm *RouteManager) setupScriptRoutes(api *gin.RouterGroup) {
	scripts := api.Group("/scripts")
	{
		scriptHandler := handlers.NewScriptHandler(rm.server.scriptService)
		scripts.POST("/", scriptHandler.CreateScript)             // 创建脚本
		scripts.GET("/", scriptHandler.ListScripts)               // 获取脚本列表
		scripts.GET("/:id", scriptHandler.GetScript)              // 获取脚本详情
		scripts.PUT("/:id", scriptHandler.UpdateScript)           // 更新脚本
		scripts.DELETE("/:id", scriptHandler.DeleteScript)        // 删除脚本
		scripts.POST("/:id/execute", scriptHandler.ExecuteScript) // 执行脚本
		scripts.GET("/types", scriptHandler.GetScriptTypes)       // 获取脚本类型
	}
}

// setupWebSocketRoutes 设置WebSocket相关路由
// 思路：处理WebSocket连接和消息相关的API
func (rm *RouteManager) setupWebSocketRoutes(api *gin.RouterGroup) {
	ws := api.Group("/ws")
	{
		ws.GET("/clients", rm.server.getWSClients)         // 获取连接的客户端列表
		ws.POST("/broadcast", rm.server.broadcastMessage)  // 广播消息
		ws.POST("/send", rm.server.sendWSMessage)          // 发送消息给指定客户端
		ws.GET("/napcat", rm.server.handleNapCatWebSocket) // NapCat反向连接
	}
}

// setupMonitorRoutes 设置监控相关路由
// 思路：处理系统监控和健康检查相关的API
func (rm *RouteManager) setupMonitorRoutes(api *gin.RouterGroup) {
	monitor := api.Group("/monitor")
	{
		monitor.GET("/metrics", rm.server.getMetrics)                // 获取系统指标
		monitor.GET("/metrics/paths", rm.server.getPathMetrics)      // 获取路径指标
		monitor.POST("/metrics/reset", rm.server.resetMetrics)       // 重置指标
		monitor.GET("/health/detailed", rm.server.getDetailedHealth) // 详细健康检查
	}
}
