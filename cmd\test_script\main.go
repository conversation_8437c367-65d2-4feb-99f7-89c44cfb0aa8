package main

import (
	"context"
	"fmt"
	"log"

	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/services"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建脚本服务
	scriptService := services.NewScriptService(database.GetDB())

	// 创建测试脚本
	scripts := []services.CreateScriptRequest{
		// 		{
		// 			Name:        "垃圾消息过滤器",
		// 			Description: "过滤包含广告、垃圾信息的消息",
		// 			Type:        "message_filter",
		// 			Priority:    100,
		// 			Enabled:     true,
		// 			Content: `-- 垃圾消息过滤器
		// -- 检查消息是否包含垃圾内容

		// local spam_keywords = {
		//     "广告", "推广", "加微信", "免费领取",
		//     "点击链接", "限时优惠", "赚钱", "兼职"
		// }

		// -- 检查消息内容
		// local content = message.content:lower()
		// log("info", "检查消息: " .. content)

		// -- 遍历垃圾关键词
		// for i, keyword in ipairs(spam_keywords) do
		//     if content:find(keyword) then
		//         log("warn", "检测到垃圾消息关键词: " .. keyword)
		//         _should_stop = true  -- 停止后续处理
		//         return false
		//     end
		// end

		// log("info", "消息通过垃圾过滤检查")
		// return true`,
		// 		},
		// 		{
		// 			Name:        "自动回复处理器",
		// 			Description: "根据关键词自动回复消息",
		// 			Type:        "message_handler",
		// 			Priority:    200,
		// 			Enabled:     true,
		// 			Content: `-- 自动回复处理器
		// -- 根据关键词自动回复

		// local content = message.content
		// local user_id = message.from.platform_user_id

		// log("info", "处理用户消息: " .. content)

		// -- 问候语回复
		// if content == "你好" or content == "hi" or content == "hello" then
		//     send_message(user_id, "您好！欢迎使用AI客服系统，我是您的智能助手。")
		//     return "已发送问候回复"
		// end

		// -- 帮助信息
		// if content == "帮助" or content == "help" then
		//     local help_text = "可用命令:\n" ..
		//                      "• 你好 - 问候\n" ..
		//                      "• 帮助 - 显示此帮助\n" ..
		//                      "• 时间 - 获取当前时间\n" ..
		//                      "• 状态 - 查看系统状态"
		//     send_message(user_id, help_text)
		//     return "已发送帮助信息"
		// end

		// -- 时间查询
		// if content == "时间" or content == "现在几点" then
		//     local current_time = os.date("%Y-%m-%d %H:%M:%S")
		//     send_message(user_id, "当前时间: " .. current_time)
		//     return "已发送时间信息"
		// end

		// log("info", "未匹配到自动回复规则")
		// return nil`,
		// 		},
		// 		{
		// 			Name:        "工作时间检查",
		// 			Description: "检查是否在工作时间内，非工作时间自动回复",
		// 			Type:        "business_rule",
		// 			Priority:    50,
		// 			Enabled:     true,
		// 			Content: `-- 工作时间检查业务规则
		// -- 检查当前是否在工作时间内

		// local current_hour = tonumber(os.date("%H"))
		// local current_day = tonumber(os.date("%w"))  -- 0=周日, 1=周一, ..., 6=周六

		// log("info", "当前时间: " .. current_hour .. "点, 星期" .. current_day)

		// -- 工作时间: 周一到周五 9:00-18:00
		// local is_workday = current_day >= 1 and current_day <= 5
		// local is_workhour = current_hour >= 9 and current_hour < 18

		// if not (is_workday and is_workhour) then
		//     local user_id = message.from.platform_user_id
		//     local off_work_msg = "您好！现在是非工作时间。\n" ..
		//                         "工作时间：周一至周五 9:00-18:00\n" ..
		//                         "您的消息我们已收到，工作时间会及时回复您。"

		//     send_message(user_id, off_work_msg)
		//     log("info", "已发送非工作时间自动回复")

		//     -- 设置变量标记已处理
		//     set_var("handled_by_workhour_check", true)
		//     return "非工作时间自动回复"
		// end

		// log("info", "当前在工作时间内")
		// return "工作时间内，继续处理"`,
		// 		},
	}

	// 创建脚本
	ctx := context.Background()
	for _, script := range scripts {
		createdScript, err := scriptService.CreateScript(ctx, &script)
		if err != nil {
			log.Printf("创建脚本失败 %s: %v", script.Name, err)
			continue
		}
		fmt.Printf("✅ 创建脚本成功: %s (ID: %s)\n", createdScript.Name, createdScript.ID)
	}

	fmt.Println("\n🎉 所有测试脚本创建完成！")
	fmt.Println("\n📖 使用说明:")
	fmt.Println("1. 访问 http://localhost:8082/api/v1/scripts/ 查看脚本列表")
	fmt.Println("2. 发送消息测试脚本功能")
	fmt.Println("3. 查看服务器日志了解脚本执行情况")
}
