package server

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"aike_go/internal/adapters"
	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/handlers"
	"aike_go/internal/middleware"
	"aike_go/internal/platforms/napcat"
	"aike_go/internal/services"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器结构体
// 思路：封装HTTP服务器，支持优雅关闭和中间件管理
// 使用例子：srv := server.New(cfg); srv.Start()
type Server struct {
	config            *config.Config
	engine            *gin.Engine
	server            *http.Server
	chatService       *services.ChatService
	knowledgeService  *services.KnowledgeService
	scriptService     *services.ScriptService
	wsService         *services.WebSocketService
	adapterManager    *adapters.AdapterManager
	napCatHandler     interface{}                 // NapCat WebSocket处理器
	amisConfigHandler *handlers.AmisConfigHandler // Amis配置处理器
	startTime         time.Time
}

// New 创建新的服务器实例
// 思路：初始化Gin引擎，设置路由和中间件
// 使用例子：srv := server.New(cfg)
func New(cfg *config.Config) *Server {
	// 初始化数据库
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 设置Gin模式
	if cfg.OpenAI.APIKey == "" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()

	// 添加自定义中间件
	engine.Use(middleware.ErrorHandlerMiddleware())
	engine.Use(middleware.LoggerMiddleware())
	engine.Use(middleware.MetricsMiddleware())

	// 添加限流中间件
	rateLimitConfig := middleware.GetDefaultRateLimitConfig()
	engine.Use(middleware.RateLimitMiddleware(rateLimitConfig))

	// 添加认证中间件
	authConfig := middleware.GetDefaultAuthConfig()
	engine.Use(middleware.AuthMiddleware(authConfig))

	// CORS中间件
	engine.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建服务实例
	chatService := services.NewChatService(cfg)
	knowledgeService := services.NewKnowledgeService()
	openaiService := services.NewOpenAIService(cfg)
	scriptService := services.NewScriptService(database.GetDB())
	wsService := services.NewWebSocketService(cfg)
	adapterManager := adapters.NewAdapterManager(cfg, chatService)

	// 创建脚本适配器（解决循环导入）
	scriptAdapter := adapters.NewScriptServiceAdapter(scriptService, openaiService, knowledgeService)

	// 创建NapCat WebSocket处理器
	napCatHandler := napcat.NewNapCatWebSocketHandler(chatService, scriptAdapter.GetLuaEngine())

	// 启动时加载脚本到引擎
	go func() {
		ctx := context.Background()
		if err := scriptAdapter.LoadScriptsFromDatabase(ctx); err != nil {
			log.Printf("启动时加载脚本失败: %v", err)
		} else {
			log.Println("脚本已成功加载到Lua引擎")
		}
	}()

	srv := &Server{
		config:           cfg,
		engine:           engine,
		chatService:      chatService,
		knowledgeService: knowledgeService,
		scriptService:    scriptService,
		wsService:        wsService,
		adapterManager:   adapterManager,
		napCatHandler:    napCatHandler,
		//	amisConfigHandler: amisConfigHandler,
		startTime: time.Now(),
		server: &http.Server{
			Addr:    fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
			Handler: engine,
		},
	}

	// 注册平台适配器
	srv.registerAdapters()

	// 设置路由
	srv.setupRoutes()

	return srv
}

// handleNapCatWebSocket 处理NapCat WebSocket连接
// 思路：代理到NapCat处理器的HandleWebSocket方法
func (s *Server) handleNapCatWebSocket(c *gin.Context) {
	if s.napCatHandler != nil {
		// 类型断言，调用HandleWebSocket方法
		if handler, ok := s.napCatHandler.(interface {
			HandleWebSocket(c *gin.Context)
		}); ok {
			handler.HandleWebSocket(c)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "NapCat处理器不可用",
			})
		}
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "NapCat服务未启用",
		})
	}
}

// registerAdapters 注册平台适配器
// 思路：注册所有支持的平台适配器
func (s *Server) registerAdapters() {
	// 注册QQ适配器
	qqAdapter := adapters.NewQQAdapter()
	qqAdapter.SetMessageHandler(s.adapterManager.GetHandler())
	if err := s.adapterManager.RegisterAdapter(qqAdapter); err != nil {
		log.Printf("注册QQ适配器失败: %v", err)
	}

	// 注册Telegram适配器
	telegramAdapter := adapters.NewTelegramAdapter()
	telegramAdapter.SetMessageHandler(s.adapterManager.GetHandler())
	if err := s.adapterManager.RegisterAdapter(telegramAdapter); err != nil {
		log.Printf("注册Telegram适配器失败: %v", err)
	}

	// 这里可以注册其他平台适配器
	// wechatAdapter := adapters.NewWeChatAdapter()
	// qianniuAdapter := adapters.NewQianniuAdapter()
}

// setupRoutes 设置路由
// 思路：模块化路由设置，便于维护和扩展
func (s *Server) setupRoutes() {
	// 健康检查
	s.engine.GET("/health", s.healthCheck)

	// API路由组
	api := s.engine.Group("/api/v1")
	{
		// 消息相关路由
		messages := api.Group("/messages")
		{
			messages.POST("/send", s.sendMessage)         // 发送消息
			messages.GET("/history", s.getMessageHistory) // 获取历史消息
		}

		// 知识库相关路由
		knowledge := api.Group("/knowledge")
		{
			knowledge.POST("/", s.createKnowledge)       // 创建知识条目
			knowledge.GET("/", s.listKnowledge)          // 获取知识列表
			knowledge.PUT("/:id", s.updateKnowledge)     // 更新知识条目
			knowledge.DELETE("/:id", s.deleteKnowledge)  // 删除知识条目
			knowledge.POST("/search", s.searchKnowledge) // 搜索知识库
		}

		// 平台相关路由
		platforms := api.Group("/platforms")
		{
			platforms.GET("/", s.listPlatforms)                      // 获取平台列表
			platforms.GET("/stats", s.getPlatformStats)              // 获取平台统计
			platforms.POST("/:platform/webhook", s.handleWebhook)    // 处理平台回调
			platforms.POST("/:platform/send", s.sendPlatformMessage) // 发送平台消息
		}

		// 脚本相关路由
		scripts := api.Group("/scripts")
		{
			scriptHandler := handlers.NewScriptHandler(s.scriptService)
			scripts.POST("/", scriptHandler.CreateScript)             // 创建脚本
			scripts.GET("/", scriptHandler.ListScripts)               // 获取脚本列表
			scripts.GET("/:id", scriptHandler.GetScript)              // 获取脚本详情
			scripts.PUT("/:id", scriptHandler.UpdateScript)           // 更新脚本
			scripts.DELETE("/:id", scriptHandler.DeleteScript)        // 删除脚本
			scripts.POST("/:id/execute", scriptHandler.ExecuteScript) // 执行脚本
			scripts.GET("/types", scriptHandler.GetScriptTypes)       // 获取脚本类型
		}

		// WebSocket相关路由
		ws := api.Group("/ws")
		{
			ws.GET("/clients", s.getWSClients)         // 获取连接的客户端列表
			ws.POST("/broadcast", s.broadcastMessage)  // 广播消息
			ws.POST("/send", s.sendWSMessage)          // 发送消息给指定客户端
			ws.GET("/napcat", s.handleNapCatWebSocket) // NapCat反向连接
		}

		// 监控相关路由
		monitor := api.Group("/monitor")
		{
			monitor.GET("/metrics", s.getMetrics)                // 获取系统指标
			monitor.GET("/metrics/paths", s.getPathMetrics)      // 获取路径指标
			monitor.POST("/metrics/reset", s.resetMetrics)       // 重置指标
			monitor.GET("/health/detailed", s.getDetailedHealth) // 详细健康检查
		}
	}

	// WebSocket路由
	s.engine.GET("/ws", s.handleWebSocket)
}

// Start 启动服务器
// 思路：支持优雅关闭，监听系统信号
// 使用例子：err := srv.Start()
func (s *Server) Start() error {
	// 创建上下文用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动适配器管理器
	go func() {
		if err := s.adapterManager.Start(ctx); err != nil {
			log.Printf("启动适配器管理器失败: %v", err)
		}
	}()

	// 启动服务器
	go func() {
		log.Printf("服务器启动在 %s", s.server.Addr)
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("正在关闭服务器...")

	// 优雅关闭服务器
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 30*time.Second)
	defer shutdownCancel()

	if err := s.server.Shutdown(shutdownCtx); err != nil {
		return fmt.Errorf("服务器关闭失败: %w", err)
	}

	log.Println("服务器已关闭")
	return nil
}

// healthCheck 健康检查处理器
// 思路：提供简单的健康状态检查接口
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"time":   time.Now().Unix(),
	})
}

// sendMessage 发送消息处理器
// 思路：接收用户消息，调用聊天服务处理，返回AI回复
func (s *Server) sendMessage(c *gin.Context) {
	var req services.ProcessMessageRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 验证必需参数
	if req.Platform == "" || req.PlatformID == "" || req.Content == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "缺少必需参数：platform, platform_id, content",
		})
		return
	}

	// 设置默认消息类型
	if req.MessageType == "" {
		req.MessageType = "text"
	}

	// 处理消息
	response, err := s.chatService.ProcessMessage(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "处理消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

func (s *Server) getMessageHistory(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "获取历史消息功能待实现"})
}

// createKnowledge 创建知识条目处理器
func (s *Server) createKnowledge(c *gin.Context) {
	var req services.CreateKnowledgeRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	knowledge, err := s.knowledgeService.CreateKnowledge(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "创建知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    knowledge,
	})
}

// listKnowledge 获取知识列表处理器
func (s *Server) listKnowledge(c *gin.Context) {
	category := c.Query("category")
	limit := 20
	offset := 0

	// 解析分页参数
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	knowledge, total, err := s.knowledgeService.ListKnowledge(category, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取知识列表失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":  knowledge,
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// updateKnowledge 更新知识条目处理器
func (s *Server) updateKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的知识条目ID",
		})
		return
	}

	var req services.UpdateKnowledgeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	knowledge, err := s.knowledgeService.UpdateKnowledge(uint(id), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "更新知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    knowledge,
	})
}

// deleteKnowledge 删除知识条目处理器
func (s *Server) deleteKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的知识条目ID",
		})
		return
	}

	err = s.knowledgeService.DeleteKnowledge(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "删除知识条目失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "知识条目已删除",
	})
}

// searchKnowledge 搜索知识库处理器
func (s *Server) searchKnowledge(c *gin.Context) {
	var req services.SearchKnowledgeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if req.Limit <= 0 || req.Limit > 50 {
		req.Limit = 10
	}

	results, err := s.knowledgeService.SearchKnowledge(req.Query, req.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "搜索知识库失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    results,
	})
}

// listPlatforms 获取平台列表
func (s *Server) listPlatforms(c *gin.Context) {
	// 添加调试日志
	log.Printf("listPlatforms 被调用，请求路径: %s, 方法: %s", c.Request.URL.Path, c.Request.Method)

	stats := s.adapterManager.GetAdapterStats()
	log.Printf("获取到适配器统计信息，适配器数量: %d", len(stats.AdapterStats))

	platforms := make([]gin.H, 0)
	for platform, adapterStats := range stats.AdapterStats {
		platforms = append(platforms, gin.H{
			"platform":          platform,
			"status":            adapterStats.Status,
			"connection_status": adapterStats.ConnectionStatus,
			"messages_sent":     adapterStats.MessagesSent,
			"messages_received": adapterStats.MessagesReceived,
			"error_count":       adapterStats.ErrorCount,
			"last_error":        adapterStats.LastError,
		})
	}

	log.Printf("构建平台列表完成，平台数量: %d", len(platforms))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"platforms":        platforms,
			"total_adapters":   stats.TotalAdapters,
			"running_adapters": stats.RunningAdapters,
			"total_messages":   stats.TotalMessages,
			"total_errors":     stats.TotalErrors,
		},
	})

	log.Printf("listPlatforms 响应已发送")
}

// getPlatformStats 获取平台统计
func (s *Server) getPlatformStats(c *gin.Context) {
	stats := s.adapterManager.GetAdapterStats()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// sendPlatformMessage 发送平台消息
func (s *Server) sendPlatformMessage(c *gin.Context) {
	platform := c.Param("platform")

	var req adapters.OutgoingMessage
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 设置平台
	req.Platform = platform

	// 发送消息
	if err := s.adapterManager.SendMessage(c.Request.Context(), &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息发送成功",
	})
}

// handleWebhook 处理平台回调
func (s *Server) handleWebhook(c *gin.Context) {
	platform := c.Param("platform")

	// 根据平台路由到对应的适配器
	switch platform {
	case "qq":
		if adapter, exists := s.adapterManager.GetAdapter("qq"); exists {
			if qqAdapter, ok := adapter.(*adapters.QQAdapter); ok {
				qqAdapter.HandleWebHook(c.Writer, c.Request)
				return
			}
		}
	case "telegram":
		if adapter, exists := s.adapterManager.GetAdapter("telegram"); exists {
			if telegramAdapter, ok := adapter.(*adapters.TelegramAdapter); ok {
				// 读取请求体
				body, err := io.ReadAll(c.Request.Body)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "读取请求体失败: " + err.Error(),
					})
					return
				}

				// 处理WebHook
				if err := telegramAdapter.ProcessWebHook(c.Request.Context(), body); err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error": "处理WebHook失败: " + err.Error(),
					})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"message": "WebHook处理成功",
				})
				return
			}
		}
	default:
		c.JSON(http.StatusNotFound, gin.H{
			"error": "不支持的平台: " + platform,
		})
		return
	}

	c.JSON(http.StatusServiceUnavailable, gin.H{
		"error": "平台适配器不可用: " + platform,
	})
}

// handleWebSocket WebSocket连接处理器
// 思路：升级HTTP连接为WebSocket，提供实时通信能力
func (s *Server) handleWebSocket(c *gin.Context) {
	s.wsService.HandleConnection(c)
}

// getWSClients 获取WebSocket客户端列表
// 思路：返回当前连接的所有WebSocket客户端信息
func (s *Server) getWSClients(c *gin.Context) {
	clients := s.wsService.GetConnectedClients()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"clients": clients,
			"count":   len(clients),
		},
	})
}

// broadcastMessage 广播消息
// 思路：向所有连接的WebSocket客户端广播消息
func (s *Server) broadcastMessage(c *gin.Context) {
	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	s.wsService.BroadcastMessage(req.Type, req.Data)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已广播",
	})
}

// sendWSMessage 发送WebSocket消息
// 思路：向指定客户端或平台用户发送消息
func (s *Server) sendWSMessage(c *gin.Context) {
	var req struct {
		ClientID   string      `json:"client_id"`   // 客户端ID（可选）
		Platform   string      `json:"platform"`    // 平台类型（可选）
		PlatformID string      `json:"platform_id"` // 平台用户ID（可选）
		Type       string      `json:"type" binding:"required"`
		Data       interface{} `json:"data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 根据不同的目标发送消息
	if req.ClientID != "" {
		// 发送给指定客户端
		s.wsService.SendToClient(req.ClientID, req.Type, req.Data)
	} else if req.Platform != "" && req.PlatformID != "" {
		// 发送给指定平台用户
		s.wsService.SendToPlatformUser(req.Platform, req.PlatformID, req.Type, req.Data)
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "必须指定client_id或platform+platform_id",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已发送",
	})
}

// getMetrics 获取系统指标
// 思路：返回系统的性能和使用指标
func (s *Server) getMetrics(c *gin.Context) {
	metrics := middleware.GetGlobalMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// getPathMetrics 获取路径指标
// 思路：返回各个API路径的详细指标
func (s *Server) getPathMetrics(c *gin.Context) {
	pathMetrics := middleware.GetGlobalPathMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    pathMetrics,
	})
}

// resetMetrics 重置指标
// 思路：清空所有指标数据，重新开始统计
func (s *Server) resetMetrics(c *gin.Context) {
	middleware.ResetGlobalMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gin.H{"message": "指标已重置"},
	})
}

// getDetailedHealth 详细健康检查
// 思路：返回系统各组件的详细健康状态
func (s *Server) getDetailedHealth(c *gin.Context) {
	health := gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"uptime":    time.Since(s.startTime),
	}

	// 检查数据库连接
	if db := database.GetDB(); db != nil {
		sqlDB, err := db.DB()
		if err == nil {
			if err := sqlDB.Ping(); err == nil {
				health["database"] = gin.H{
					"status": "healthy",
					"type":   "sqlite", // 或从配置获取
				}
			} else {
				health["database"] = gin.H{
					"status": "unhealthy",
					"error":  err.Error(),
				}
				health["status"] = "degraded"
			}
		}
	}

	// 检查适配器状态
	if s.adapterManager != nil {
		stats := s.adapterManager.GetAdapterStats()
		health["adapters"] = gin.H{
			"total":   stats.TotalAdapters,
			"running": stats.RunningAdapters,
			"errors":  stats.TotalErrors,
		}

		if stats.TotalErrors > 0 {
			health["status"] = "degraded"
		}
	}

	// 检查WebSocket服务
	if s.wsService != nil {
		clients := s.wsService.GetConnectedClients()
		health["websocket"] = gin.H{
			"status":  "healthy",
			"clients": len(clients),
		}
	}

	// 获取系统指标
	metrics := middleware.GetGlobalMetrics()
	health["metrics"] = gin.H{
		"total_requests":    metrics.TotalRequests,
		"active_requests":   metrics.ActiveRequests,
		"error_rate":        metrics.ErrorRate,
		"avg_response_time": metrics.AvgResponseTime.String(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    health,
	})
}
