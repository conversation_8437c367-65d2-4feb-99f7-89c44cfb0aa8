<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI客服系统管理后台</title>
	<!-- 引入Vue 3和Element Plus -->
	<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
	<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
	<script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
	<!-- 引入图标库 -->
	<script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
	<!-- 引入axios -->
	<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
	<style>
		body {
			margin: 0;
			font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
		}

		.admin-container {
			height: 100vh;
			display: flex;
		}

		.sidebar {
			width: 250px;
			background-color: #304156;
			color: white;
		}

		.main-content {
			flex: 1;
			background-color: #f0f2f5;
			overflow: auto;
		}

		.header {
			height: 60px;
			background-color: white;
			box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
			display: flex;
			align-items: center;
			padding: 0 20px;
			justify-content: space-between;
		}

		.content-area {
			padding: 20px;
		}

		.logo {
			padding: 20px;
			text-align: center;
			border-bottom: 1px solid #434a50;
		}

		.logo h2 {
			margin: 0;
			color: #409eff;
		}

		.menu-item {
			padding: 12px 20px;
			cursor: pointer;
			border-bottom: 1px solid #434a50;
			transition: background-color 0.3s;
		}

		.menu-item:hover {
			background-color: #434a50;
		}

		.menu-item.active {
			background-color: #409eff;
		}

		.menu-item i {
			margin-right: 10px;
			width: 16px;
		}

		.stats-card {
			background: white;
			border-radius: 8px;
			padding: 20px;
			margin-bottom: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
		}

		.stats-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: 20px;
			margin-bottom: 20px;
		}

		.stat-item {
			text-align: center;
		}

		.stat-number {
			font-size: 32px;
			font-weight: bold;
			color: #409eff;
			margin-bottom: 8px;
		}

		.stat-label {
			color: #666;
			font-size: 14px;
		}

		.table-container {
			background: white;
			border-radius: 8px;
			padding: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
		}

		.search-bar {
			margin-bottom: 20px;
			display: flex;
			gap: 10px;
			align-items: center;
		}

		.pagination-container {
			margin-top: 20px;
			text-align: center;
		}

		.status-tag {
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 12px;
		}

		.status-active {
			background-color: #f0f9ff;
			color: #1890ff;
		}

		.status-inactive {
			background-color: #fff2e8;
			color: #fa8c16;
		}

		.status-error {
			background-color: #fff1f0;
			color: #f5222d;
		}
	</style>
</head>

<body>
	<div id="app">
		<div class="admin-container">
			<!-- 侧边栏 -->
			<div class="sidebar">
				<div class="logo">
					<h2>AI客服管理</h2>
				</div>
				<div class="menu-item" :class="{active: currentView === 'dashboard'}" @click="switchView('dashboard')">
					<i class="el-icon-odometer"></i>
					仪表盘
				</div>
				<div class="menu-item" :class="{active: currentView === 'users'}" @click="switchView('users')">
					<i class="el-icon-user"></i>
					用户管理
				</div>
				<div class="menu-item" :class="{active: currentView === 'messages'}" @click="switchView('messages')">
					<i class="el-icon-chat-line-square"></i>
					消息管理
				</div>
				<div class="menu-item" :class="{active: currentView === 'knowledge'}" @click="switchView('knowledge')">
					<i class="el-icon-collection"></i>
					知识库管理
				</div>
				<div class="menu-item" :class="{active: currentView === 'scripts'}" @click="switchView('scripts')">
					<i class="el-icon-document"></i>
					脚本管理
				</div>
				<div class="menu-item" :class="{active: currentView === 'platforms'}" @click="switchView('platforms')">
					<i class="el-icon-connection"></i>
					平台管理
				</div>
				<div class="menu-item" :class="{active: currentView === 'monitor'}" @click="switchView('monitor')">
					<i class="el-icon-monitor"></i>
					系统监控
				</div>
			</div>

			<!-- 主内容区 -->
			<div class="main-content">
				<!-- 顶部导航 -->
				<div class="header">
					<h3>{{ getViewTitle() }}</h3>
					<div>
						<el-button type="primary" @click="refreshData">刷新数据</el-button>
						<el-dropdown>
							<span class="el-dropdown-link">
								管理员<i class="el-icon-arrow-down el-icon--right"></i>
							</span>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item>个人设置</el-dropdown-item>
									<el-dropdown-item>退出登录</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</div>
				</div>

				<!-- 内容区域 -->
				<div class="content-area">
					<!-- 仪表盘视图 -->
					<div v-if="currentView === 'dashboard'">
						<div class="stats-grid">
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemStats.totalUsers || 0 }}</div>
									<div class="stat-label">总用户数</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemStats.totalMessages || 0 }}</div>
									<div class="stat-label">总消息数</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemStats.totalSessions || 0 }}</div>
									<div class="stat-label">总会话数</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemStats.totalKnowledge || 0 }}</div>
									<div class="stat-label">知识库条目</div>
								</div>
							</div>
						</div>

						<div class="table-container">
							<h4>系统健康状态</h4>
							<el-descriptions :column="2" border>
								<el-descriptions-item label="系统状态">
									<el-tag :type="healthStatus.status === 'healthy' ? 'success' : 'danger'">
										{{ healthStatus.status === 'healthy' ? '健康' : '异常' }}
									</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="运行时间">{{ healthStatus.uptime || 'N/A'
									}}</el-descriptions-item>
								<el-descriptions-item label="数据库状态">
									<el-tag :type="healthStatus.database?.status === 'healthy' ? 'success' : 'danger'">
										{{ healthStatus.database?.status === 'healthy' ? '正常' : '异常' }}
									</el-tag>
								</el-descriptions-item>
								<el-descriptions-item label="WebSocket连接">{{ healthStatus.websocket?.clients || 0 }}
									个</el-descriptions-item>
							</el-descriptions>
						</div>
					</div>

					<!-- 用户管理视图 -->
					<div v-if="currentView === 'users'">
						<div class="table-container">
							<div class="search-bar">
								<el-input v-model="userSearch" placeholder="搜索用户..." style="width: 300px;"></el-input>
								<el-select v-model="userPlatformFilter" placeholder="选择平台" style="width: 150px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="QQ" value="qq"></el-option>
									<el-option label="微信" value="wechat"></el-option>
									<el-option label="Telegram" value="telegram"></el-option>
								</el-select>
								<el-button type="primary" @click="searchUsers">搜索</el-button>
								<el-button @click="resetUserSearch">重置</el-button>
							</div>

							<el-table :data="users" style="width: 100%" v-loading="loading.users">
								<el-table-column prop="id" label="ID" width="80"></el-table-column>
								<el-table-column prop="platform_id" label="平台ID" width="120"></el-table-column>
								<el-table-column prop="platform" label="平台" width="100">
									<template #default="scope">
										<el-tag size="small">{{ scope.row.platform }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="nickname" label="昵称" width="150"></el-table-column>
								<el-table-column prop="status" label="状态" width="100">
									<template #default="scope">
										<span :class="getUserStatusClass(scope.row.status)">
											{{ getUserStatusText(scope.row.status) }}
										</span>
									</template>
								</el-table-column>
								<el-table-column prop="is_vip" label="VIP" width="80">
									<template #default="scope">
										<el-tag :type="scope.row.is_vip ? 'warning' : 'info'" size="small">
											{{ scope.row.is_vip ? 'VIP' : '普通' }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="message_count" label="消息数" width="100"></el-table-column>
								<el-table-column prop="last_active_at" label="最后活跃" width="180">
									<template #default="scope">
										{{ formatDateTime(scope.row.last_active_at) }}
									</template>
								</el-table-column>
								<el-table-column label="操作" width="150">
									<template #default="scope">
										<el-button size="small" @click="viewUser(scope.row)">查看</el-button>
										<el-button size="small" type="danger"
											@click="blockUser(scope.row)">封禁</el-button>
									</template>
								</el-table-column>
							</el-table>

							<div class="pagination-container">
								<el-pagination v-model:current-page="userPagination.page"
									v-model:page-size="userPagination.pageSize" :page-sizes="[10, 20, 50, 100]"
									:total="userPagination.total" layout="total, sizes, prev, pager, next, jumper"
									@size-change="handleUserPageSizeChange" @current-change="handleUserPageChange">
								</el-pagination>
							</div>
						</div>
					</div>

					<!-- 消息管理视图 -->
					<div v-if="currentView === 'messages'">
						<div class="table-container">
							<div class="search-bar">
								<el-input v-model="messageSearch" placeholder="搜索消息内容..."
									style="width: 300px;"></el-input>
								<el-select v-model="messagePlatformFilter" placeholder="选择平台" style="width: 150px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="QQ" value="qq"></el-option>
									<el-option label="微信" value="wechat"></el-option>
									<el-option label="Telegram" value="telegram"></el-option>
								</el-select>
								<el-select v-model="messageDirectionFilter" placeholder="消息方向" style="width: 150px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="用户发送" value="incoming"></el-option>
									<el-option label="机器人回复" value="outgoing"></el-option>
								</el-select>
								<el-button type="primary" @click="searchMessages">搜索</el-button>
								<el-button @click="resetMessageSearch">重置</el-button>
							</div>

							<el-table :data="messages" style="width: 100%" v-loading="loading.messages">
								<el-table-column prop="id" label="ID" width="80"></el-table-column>
								<el-table-column prop="user_id" label="用户ID" width="100"></el-table-column>
								<el-table-column prop="platform" label="平台" width="100">
									<template #default="scope">
										<el-tag size="small">{{ scope.row.platform }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="direction" label="方向" width="100">
									<template #default="scope">
										<el-tag :type="scope.row.direction === 'incoming' ? 'primary' : 'success'"
											size="small">
											{{ scope.row.direction === 'incoming' ? '接收' : '发送' }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="content" label="消息内容" min-width="200">
									<template #default="scope">
										<div
											style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
											{{ scope.row.content }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="is_ai_generated" label="AI生成" width="100">
									<template #default="scope">
										<el-tag :type="scope.row.is_ai_generated ? 'warning' : 'info'" size="small">
											{{ scope.row.is_ai_generated ? 'AI' : '人工' }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="tokens_used" label="Token" width="80"></el-table-column>
								<el-table-column prop="created_at" label="时间" width="180">
									<template #default="scope">
										{{ formatDateTime(scope.row.created_at) }}
									</template>
								</el-table-column>
								<el-table-column label="操作" width="120">
									<template #default="scope">
										<el-button size="small" @click="viewMessage(scope.row)">查看详情</el-button>
									</template>
								</el-table-column>
							</el-table>

							<div class="pagination-container">
								<el-pagination v-model:current-page="messagePagination.page"
									v-model:page-size="messagePagination.pageSize" :page-sizes="[10, 20, 50, 100]"
									:total="messagePagination.total" layout="total, sizes, prev, pager, next, jumper"
									@size-change="handleMessagePageSizeChange"
									@current-change="handleMessagePageChange">
								</el-pagination>
							</div>
						</div>
					</div>

					<!-- 知识库管理视图 -->
					<div v-if="currentView === 'knowledge'">
						<div class="table-container">
							<div class="search-bar">
								<el-input v-model="knowledgeSearch" placeholder="搜索知识库..."
									style="width: 300px;"></el-input>
								<el-select v-model="knowledgeCategoryFilter" placeholder="选择分类" style="width: 150px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="常见问题" value="FAQ"></el-option>
									<el-option label="产品介绍" value="Product"></el-option>
									<el-option label="技术支持" value="Support"></el-option>
								</el-select>
								<el-button type="primary" @click="searchKnowledge">搜索</el-button>
								<el-button @click="resetKnowledgeSearch">重置</el-button>
								<el-button type="success" @click="showCreateKnowledgeDialog">新增知识</el-button>
							</div>

							<el-table :data="knowledgeList" style="width: 100%" v-loading="loading.knowledge">
								<el-table-column prop="id" label="ID" width="80"></el-table-column>
								<el-table-column prop="title" label="标题" min-width="200"></el-table-column>
								<el-table-column prop="category" label="分类" width="120">
									<template #default="scope">
										<el-tag size="small">{{ scope.row.category }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="status" label="状态" width="100">
									<template #default="scope">
										<span :class="getKnowledgeStatusClass(scope.row.status)">
											{{ getKnowledgeStatusText(scope.row.status) }}
										</span>
									</template>
								</el-table-column>
								<el-table-column prop="priority" label="优先级" width="100"></el-table-column>
								<el-table-column prop="use_count" label="使用次数" width="100"></el-table-column>
								<el-table-column prop="updated_at" label="更新时间" width="180">
									<template #default="scope">
										{{ formatDateTime(scope.row.updated_at) }}
									</template>
								</el-table-column>
								<el-table-column label="操作" width="200">
									<template #default="scope">
										<el-button size="small" @click="viewKnowledge(scope.row)">查看</el-button>
										<el-button size="small" type="primary"
											@click="editKnowledge(scope.row)">编辑</el-button>
										<el-button size="small" type="danger"
											@click="deleteKnowledge(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>

							<div class="pagination-container">
								<el-pagination v-model:current-page="knowledgePagination.page"
									v-model:page-size="knowledgePagination.pageSize" :page-sizes="[10, 20, 50, 100]"
									:total="knowledgePagination.total" layout="total, sizes, prev, pager, next, jumper"
									@size-change="handleKnowledgePageSizeChange"
									@current-change="handleKnowledgePageChange">
								</el-pagination>
							</div>
						</div>
					</div>

					<!-- 脚本管理视图 -->
					<div v-if="currentView === 'scripts'">
						<div class="table-container">
							<div class="search-bar">
								<el-input v-model="scriptSearch" placeholder="搜索脚本..." style="width: 300px;"></el-input>
								<el-select v-model="scriptTypeFilter" placeholder="选择类型" style="width: 150px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="消息处理器" value="message_handler"></el-option>
									<el-option label="业务规则" value="business_rule"></el-option>
									<el-option label="插件" value="plugin"></el-option>
									<el-option label="过滤器" value="filter"></el-option>
								</el-select>
								<el-select v-model="scriptStatusFilter" placeholder="状态" style="width: 120px;">
									<el-option label="全部" value=""></el-option>
									<el-option label="启用" value="true"></el-option>
									<el-option label="禁用" value="false"></el-option>
								</el-select>
								<el-button type="primary" @click="searchScripts">搜索</el-button>
								<el-button @click="resetScriptSearch">重置</el-button>
								<el-button type="success" @click="showCreateScriptDialog">新增脚本</el-button>
							</div>

							<el-table :data="scriptList" style="width: 100%" v-loading="loading.scripts">
								<el-table-column prop="id" label="ID" width="80"></el-table-column>
								<el-table-column prop="name" label="脚本名称" min-width="150"></el-table-column>
								<el-table-column prop="type" label="类型" width="120">
									<template #default="scope">
										<el-tag size="small">{{ getScriptTypeText(scope.row.type) }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="enabled" label="状态" width="100">
									<template #default="scope">
										<el-switch v-model="scope.row.enabled" @change="toggleScript(scope.row)"
											active-text="启用" inactive-text="禁用">
										</el-switch>
									</template>
								</el-table-column>
								<el-table-column prop="priority" label="优先级" width="100"></el-table-column>
								<el-table-column prop="execution_count" label="执行次数" width="100"></el-table-column>
								<el-table-column prop="last_executed_at" label="最后执行" width="180">
									<template #default="scope">
										{{ formatDateTime(scope.row.last_executed_at) }}
									</template>
								</el-table-column>
								<el-table-column label="操作" width="250">
									<template #default="scope">
										<el-button size="small" @click="viewScript(scope.row)">查看</el-button>
										<el-button size="small" type="primary"
											@click="editScript(scope.row)">编辑</el-button>
										<el-button size="small" type="warning"
											@click="executeScript(scope.row)">执行</el-button>
										<el-button size="small" type="danger"
											@click="deleteScript(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>

							<div class="pagination-container">
								<el-pagination v-model:current-page="scriptPagination.page"
									v-model:page-size="scriptPagination.pageSize" :page-sizes="[10, 20, 50, 100]"
									:total="scriptPagination.total" layout="total, sizes, prev, pager, next, jumper"
									@size-change="handleScriptPageSizeChange" @current-change="handleScriptPageChange">
								</el-pagination>
							</div>
						</div>
					</div>

					<!-- 平台管理视图 -->
					<div v-if="currentView === 'platforms'">
						<div class="table-container">
							<div class="search-bar">
								<el-button type="success" @click="refreshPlatforms">刷新状态</el-button>
								<el-button type="primary" @click="showAddPlatformDialog">添加平台</el-button>
							</div>

							<el-table :data="platformList" style="width: 100%" v-loading="loading.platforms">
								<el-table-column prop="name" label="平台名称" width="120">
									<template #default="scope">
										<el-tag size="small">{{ scope.row.name.toUpperCase() }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="status" label="连接状态" width="120">
									<template #default="scope">
										<span :class="getPlatformStatusClass(scope.row.status)">
											{{ getPlatformStatusText(scope.row.status) }}
										</span>
									</template>
								</el-table-column>
								<el-table-column prop="enabled" label="启用状态" width="100">
									<template #default="scope">
										<el-switch v-model="scope.row.enabled" @change="togglePlatform(scope.row)"
											active-text="启用" inactive-text="禁用">
										</el-switch>
									</template>
								</el-table-column>
								<el-table-column prop="endpoint" label="端点地址" min-width="200"></el-table-column>
								<el-table-column prop="last_heartbeat" label="最后心跳" width="180">
									<template #default="scope">
										{{ formatDateTime(scope.row.last_heartbeat) }}
									</template>
								</el-table-column>
								<el-table-column prop="message_count" label="消息数" width="100"></el-table-column>
								<el-table-column label="操作" width="200">
									<template #default="scope">
										<el-button size="small" @click="viewPlatform(scope.row)">查看</el-button>
										<el-button size="small" type="primary"
											@click="editPlatform(scope.row)">配置</el-button>
										<el-button size="small" type="warning"
											@click="testPlatform(scope.row)">测试</el-button>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>

					<!-- 系统监控视图 -->
					<div v-if="currentView === 'monitor'">
						<div class="stats-grid">
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemMetrics.total_requests || 0 }}</div>
									<div class="stat-label">总请求数</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemMetrics.active_requests || 0 }}</div>
									<div class="stat-label">活跃请求</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ (systemMetrics.error_rate || 0).toFixed(2) }}%</div>
									<div class="stat-label">错误率</div>
								</div>
							</div>
							<div class="stats-card">
								<div class="stat-item">
									<div class="stat-number">{{ systemMetrics.avg_response_time || 'N/A' }}</div>
									<div class="stat-label">平均响应时间</div>
								</div>
							</div>
						</div>

						<div class="table-container">
							<h4>系统指标详情</h4>
							<el-descriptions :column="2" border>
								<el-descriptions-item label="运行时间">{{ systemMetrics.uptime || 'N/A'
									}}</el-descriptions-item>
								<el-descriptions-item label="总错误数">{{ systemMetrics.total_errors || 0
									}}</el-descriptions-item>
								<el-descriptions-item label="P95响应时间">{{ systemMetrics.p95_response_time || 'N/A'
									}}</el-descriptions-item>
								<el-descriptions-item label="P99响应时间">{{ systemMetrics.p99_response_time || 'N/A'
									}}</el-descriptions-item>
							</el-descriptions>

							<h4 style="margin-top: 20px;">状态码分布</h4>
							<el-table :data="getStatusCodeData()" style="width: 100%">
								<el-table-column prop="code" label="状态码" width="120"></el-table-column>
								<el-table-column prop="count" label="数量" width="120"></el-table-column>
								<el-table-column prop="percentage" label="百分比" width="120">
									<template #default="scope">
										{{ scope.row.percentage }}%
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>

					<!-- 其他视图 -->
					<div
						v-if="!['dashboard', 'users', 'messages', 'knowledge', 'scripts', 'platforms', 'monitor'].includes(currentView)">
						<div class="table-container">
							<h3>{{ getViewTitle() }}</h3>
							<p>此功能正在开发中...</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		const { createApp } = Vue;
		const { ElMessage, ElMessageBox } = ElementPlus;

		// 配置axios基础URL
		axios.defaults.baseURL = '/api/v1';
		axios.defaults.timeout = 10000;

		// 响应拦截器
		axios.interceptors.response.use(
			response => response,
			error => {
				ElMessage.error(error.response?.data?.message || '请求失败');
				return Promise.reject(error);
			}
		);

		createApp({
			data() {
				return {
					currentView: 'dashboard',
					loading: {
						users: false,
						messages: false,
						knowledge: false,
						scripts: false,
						platforms: false
					},
					// 系统统计数据
					systemStats: {},
					healthStatus: {},
					// 用户管理数据
					users: [],
					userSearch: '',
					userPlatformFilter: '',
					userPagination: {
						page: 1,
						pageSize: 20,
						total: 0
					},
					// 消息管理数据
					messages: [],
					messageSearch: '',
					messagePlatformFilter: '',
					messageDirectionFilter: '',
					messagePagination: {
						page: 1,
						pageSize: 20,
						total: 0
					},
					// 知识库管理数据
					knowledgeList: [],
					knowledgeSearch: '',
					knowledgeCategoryFilter: '',
					knowledgePagination: {
						page: 1,
						pageSize: 20,
						total: 0
					},
					// 脚本管理数据
					scriptList: [],
					scriptSearch: '',
					scriptTypeFilter: '',
					scriptStatusFilter: '',
					scriptPagination: {
						page: 1,
						pageSize: 20,
						total: 0
					},
					// 平台管理数据
					platformList: [],
					// 系统监控数据
					systemMetrics: {}
				};
			},
			mounted() {
				this.loadDashboardData();
			},
			methods: {
				// 视图切换
				switchView(view) {
					this.currentView = view;
					switch (view) {
						case 'dashboard':
							this.loadDashboardData();
							break;
						case 'users':
							this.loadUsers();
							break;
						case 'messages':
							this.loadMessages();
							break;
						case 'knowledge':
							this.loadKnowledge();
							break;
						case 'scripts':
							this.loadScripts();
							break;
						case 'platforms':
							this.loadPlatforms();
							break;
						case 'monitor':
							this.loadMonitorData();
							break;
					}
				},

				getViewTitle() {
					const titles = {
						dashboard: '仪表盘',
						users: '用户管理',
						messages: '消息管理',
						knowledge: '知识库管理',
						scripts: '脚本管理',
						platforms: '平台管理',
						monitor: '系统监控'
					};
					return titles[this.currentView] || '未知页面';
				},

				// 刷新数据
				refreshData() {
					this.switchView(this.currentView);
					ElMessage.success('数据已刷新');
				},

				// 加载仪表盘数据
				async loadDashboardData() {
					try {
						// 加载系统健康状态
						const healthResponse = await axios.get('/monitor/health/detailed');
						if (healthResponse.data.success) {
							this.healthStatus = healthResponse.data.data;
						}

						// 加载系统统计（模拟数据，实际需要对应的API）
						this.systemStats = {
							totalUsers: 1250,
							totalMessages: 8900,
							totalSessions: 450,
							totalKnowledge: 120
						};
					} catch (error) {
						console.error('加载仪表盘数据失败:', error);
					}
				},

				// 加载用户数据
				async loadUsers() {
					this.loading.users = true;
					try {
						// 注意：这里需要实现用户列表API
						// const response = await axios.get('/users', {
						//     params: {
						//         page: this.userPagination.page,
						//         page_size: this.userPagination.pageSize,
						//         search: this.userSearch,
						//         platform: this.userPlatformFilter
						//     }
						// });

						// 模拟数据
						this.users = [
							{
								id: 1,
								platform_id: '123456789',
								platform: 'qq',
								nickname: '张三',
								status: 'active',
								is_vip: false,
								message_count: 45,
								last_active_at: new Date().toISOString()
							},
							{
								id: 2,
								platform_id: '987654321',
								platform: 'wechat',
								nickname: '李四',
								status: 'active',
								is_vip: true,
								message_count: 128,
								last_active_at: new Date(Date.now() - 3600000).toISOString()
							}
						];
						this.userPagination.total = 100;
					} catch (error) {
						console.error('加载用户数据失败:', error);
					} finally {
						this.loading.users = false;
					}
				},

				// 加载消息数据
				async loadMessages() {
					this.loading.messages = true;
					try {
						// 使用现有的消息历史API
						const response = await axios.get('/messages/history', {
							params: {
								page: this.messagePagination.page,
								page_size: this.messagePagination.pageSize,
								search: this.messageSearch,
								platform: this.messagePlatformFilter,
								direction: this.messageDirectionFilter
							}
						});

						if (response.data.success) {
							this.messages = response.data.data.messages || [];
							this.messagePagination.total = response.data.data.total || 0;
						} else {
							// 模拟数据
							this.messages = [
								{
									id: 1,
									user_id: 123,
									platform: 'qq',
									direction: 'incoming',
									content: '你好，请问工作时间是什么？',
									is_ai_generated: false,
									tokens_used: 0,
									created_at: new Date().toISOString()
								},
								{
									id: 2,
									user_id: 123,
									platform: 'qq',
									direction: 'outgoing',
									content: '我们的工作时间是周一至周五 9:00-18:00，周六 9:00-12:00，周日休息。',
									is_ai_generated: true,
									tokens_used: 45,
									created_at: new Date(Date.now() + 1000).toISOString()
								}
							];
							this.messagePagination.total = 50;
						}
					} catch (error) {
						console.error('加载消息数据失败:', error);
						// 使用模拟数据
						this.messages = [
							{
								id: 1,
								user_id: 123,
								platform: 'qq',
								direction: 'incoming',
								content: '你好，请问工作时间是什么？',
								is_ai_generated: false,
								tokens_used: 0,
								created_at: new Date().toISOString()
							}
						];
						this.messagePagination.total = 1;
					} finally {
						this.loading.messages = false;
					}
				},

				// 加载知识库数据
				async loadKnowledge() {
					this.loading.knowledge = true;
					try {
						// 使用现有的知识库API
						const response = await axios.get('/knowledge/', {
							params: {
								limit: this.knowledgePagination.pageSize,
								offset: (this.knowledgePagination.page - 1) * this.knowledgePagination.pageSize,
								category: this.knowledgeCategoryFilter,
								search: this.knowledgeSearch
							}
						});

						if (response.data.success) {
							// API返回的数据结构：data.items 包含知识库列表
							this.knowledgeList = response.data.data.items || [];
							this.knowledgePagination.total = response.data.data.total || 0;
						} else {
							// 模拟数据
							this.knowledgeList = [
								{
									id: 1,
									title: '工作时间说明',
									category: 'FAQ',
									status: 'active',
									priority: 10,
									use_count: 25,
									updated_at: new Date().toISOString()
								},
								{
									id: 2,
									title: '产品功能介绍',
									category: 'Product',
									status: 'active',
									priority: 8,
									use_count: 15,
									updated_at: new Date(Date.now() - 86400000).toISOString()
								}
							];
							this.knowledgePagination.total = 20;
						}
					} catch (error) {
						console.error('加载知识库数据失败:', error);
						// 使用模拟数据
						this.knowledgeList = [
							{
								id: 1,
								title: '工作时间说明',
								category: 'FAQ',
								status: 'active',
								priority: 10,
								use_count: 25,
								updated_at: new Date().toISOString()
							}
						];
						this.knowledgePagination.total = 1;
					} finally {
						this.loading.knowledge = false;
					}
				},
				// 加载脚本数据
				async loadScripts() {
					this.loading.scripts = true;
					try {
						// 使用现有的脚本API
						const response = await axios.get('/scripts/', {
							params: {
								page: this.scriptPagination.page,
								page_size: this.scriptPagination.pageSize,
								search: this.scriptSearch,
								type: this.scriptTypeFilter,
								enabled: this.scriptStatusFilter
							}
						});

						if (response.data.success) {
							this.scriptList = response.data.data.scripts || [];
							this.scriptPagination.total = response.data.data.total || 0;
						} else {
							// 模拟数据
							this.scriptList = [
								{
									id: 1,
									name: '消息过滤器',
									type: 'filter',
									enabled: true,
									priority: 10,
									execution_count: 156,
									last_executed_at: new Date().toISOString()
								},
								{
									id: 2,
									name: 'QQ消息处理器',
									type: 'message_handler',
									enabled: true,
									priority: 8,
									execution_count: 89,
									last_executed_at: new Date(Date.now() - 3600000).toISOString()
								},
								{
									id: 3,
									name: '知识库搜索插件',
									type: 'plugin',
									enabled: false,
									priority: 5,
									execution_count: 23,
									last_executed_at: new Date(Date.now() - 86400000).toISOString()
								}
							];
							this.scriptPagination.total = 15;
						}
					} catch (error) {
						console.error('加载脚本数据失败:', error);
						// 使用模拟数据
						this.scriptList = [
							{
								id: 1,
								name: '消息过滤器',
								type: 'filter',
								enabled: true,
								priority: 10,
								execution_count: 156,
								last_executed_at: new Date().toISOString()
							}
						];
						this.scriptPagination.total = 1;
					} finally {
						this.loading.scripts = false;
					}
				},

				// 加载平台数据
				async loadPlatforms() {
					this.loading.platforms = true;
					try {
						// 使用现有的平台API
						const response = await axios.get('/platforms/');

						if (response.data.success) {
							this.platformList = response.data.data.platforms || [];
						} else {
							// 模拟数据
							this.platformList = [
								{
									name: 'qq',
									status: 'connected',
									enabled: true,
									endpoint: 'http://localhost:3000',
									last_heartbeat: new Date().toISOString(),
									message_count: 1250
								},
								{
									name: 'wechat',
									status: 'disconnected',
									enabled: false,
									endpoint: 'http://localhost:3001',
									last_heartbeat: new Date(Date.now() - 3600000).toISOString(),
									message_count: 0
								},
								{
									name: 'telegram',
									status: 'error',
									enabled: true,
									endpoint: 'https://api.telegram.org',
									last_heartbeat: new Date(Date.now() - 7200000).toISOString(),
									message_count: 45
								}
							];
						}
					} catch (error) {
						console.error('加载平台数据失败:', error);
						// 使用模拟数据
						this.platformList = [
							{
								name: 'qq',
								status: 'connected',
								enabled: true,
								endpoint: 'http://localhost:3000',
								last_heartbeat: new Date().toISOString(),
								message_count: 1250
							}
						];
					} finally {
						this.loading.platforms = false;
					}
				},

				// 加载监控数据
				async loadMonitorData() {
					try {
						// 使用现有的监控API
						const response = await axios.get('/monitor/metrics');

						if (response.data.success) {
							this.systemMetrics = response.data.data;
						} else {
							// 模拟数据
							this.systemMetrics = {
								total_requests: 12500,
								active_requests: 8,
								total_errors: 25,
								error_rate: 0.2,
								uptime: '2天15小时30分钟',
								avg_response_time: '45ms',
								p95_response_time: '120ms',
								p99_response_time: '250ms',
								status_codes: {
									'200': 12000,
									'400': 15,
									'404': 8,
									'500': 2
								}
							};
						}
					} catch (error) {
						console.error('加载监控数据失败:', error);
						// 使用模拟数据
						this.systemMetrics = {
							total_requests: 12500,
							active_requests: 8,
							total_errors: 25,
							error_rate: 0.2,
							uptime: '2天15小时30分钟',
							avg_response_time: '45ms'
						};
					}
				},

				// 用户相关方法
				searchUsers() {
					this.userPagination.page = 1;
					this.loadUsers();
				},
				resetUserSearch() {
					this.userSearch = '';
					this.userPlatformFilter = '';
					this.userPagination.page = 1;
					this.loadUsers();
				},
				handleUserPageChange(page) {
					this.userPagination.page = page;
					this.loadUsers();
				},
				handleUserPageSizeChange(size) {
					this.userPagination.pageSize = size;
					this.userPagination.page = 1;
					this.loadUsers();
				},
				viewUser(user) {
					ElMessage.info(`查看用户: ${user.nickname}`);
				},
				async blockUser(user) {
					try {
						await ElMessageBox.confirm(`确定要封禁用户 ${user.nickname} 吗？`, '确认操作', {
							type: 'warning'
						});
						// 这里调用封禁用户的API
						ElMessage.success('用户已封禁');
						this.loadUsers();
					} catch {
						// 用户取消操作
					}
				},

				// 消息相关方法
				searchMessages() {
					this.messagePagination.page = 1;
					this.loadMessages();
				},
				resetMessageSearch() {
					this.messageSearch = '';
					this.messagePlatformFilter = '';
					this.messageDirectionFilter = '';
					this.messagePagination.page = 1;
					this.loadMessages();
				},
				handleMessagePageChange(page) {
					this.messagePagination.page = page;
					this.loadMessages();
				},
				handleMessagePageSizeChange(size) {
					this.messagePagination.pageSize = size;
					this.messagePagination.page = 1;
					this.loadMessages();
				},
				viewMessage(message) {
					ElMessage.info(`查看消息详情: ${message.content.substring(0, 20)}...`);
				},

				// 知识库相关方法
				searchKnowledge() {
					this.knowledgePagination.page = 1;
					this.loadKnowledge();
				},
				resetKnowledgeSearch() {
					this.knowledgeSearch = '';
					this.knowledgeCategoryFilter = '';
					this.knowledgePagination.page = 1;
					this.loadKnowledge();
				},
				handleKnowledgePageChange(page) {
					this.knowledgePagination.page = page;
					this.loadKnowledge();
				},
				handleKnowledgePageSizeChange(size) {
					this.knowledgePagination.pageSize = size;
					this.knowledgePagination.page = 1;
					this.loadKnowledge();
				},
				showCreateKnowledgeDialog() {
					ElMessage.info('新增知识库功能开发中...');
				},
				viewKnowledge(knowledge) {
					ElMessage.info(`查看知识库: ${knowledge.title}`);
				},
				editKnowledge(knowledge) {
					ElMessage.info(`编辑知识库: ${knowledge.title}`);
				},
				async deleteKnowledge(knowledge) {
					try {
						await ElMessageBox.confirm(`确定要删除知识库 "${knowledge.title}" 吗？`, '确认删除', {
							type: 'warning'
						});
						// 这里调用删除知识库的API
						// await axios.delete(`/knowledge/${knowledge.id}`);
						ElMessage.success('知识库已删除');
						this.loadKnowledge();
					} catch {
						// 用户取消操作
					}
				},

				// 工具方法
				getUserStatusClass(status) {
					const classes = {
						active: 'status-tag status-active',
						inactive: 'status-tag status-inactive',
						blocked: 'status-tag status-error'
					};
					return classes[status] || 'status-tag';
				},
				getUserStatusText(status) {
					const texts = {
						active: '活跃',
						inactive: '非活跃',
						blocked: '已封禁'
					};
					return texts[status] || '未知';
				},
				getKnowledgeStatusClass(status) {
					const classes = {
						active: 'status-tag status-active',
						inactive: 'status-tag status-inactive',
						draft: 'status-tag status-error'
					};
					return classes[status] || 'status-tag';
				},
				getKnowledgeStatusText(status) {
					const texts = {
						active: '已发布',
						inactive: '已下线',
						draft: '草稿'
					};
					return texts[status] || '未知';
				},
				formatDateTime(dateString) {
					if (!dateString) return 'N/A';
					return new Date(dateString).toLocaleString('zh-CN');
				},

				// 脚本相关方法
				searchScripts() {
					this.scriptPagination.page = 1;
					this.loadScripts();
				},
				resetScriptSearch() {
					this.scriptSearch = '';
					this.scriptTypeFilter = '';
					this.scriptStatusFilter = '';
					this.scriptPagination.page = 1;
					this.loadScripts();
				},
				handleScriptPageChange(page) {
					this.scriptPagination.page = page;
					this.loadScripts();
				},
				handleScriptPageSizeChange(size) {
					this.scriptPagination.pageSize = size;
					this.scriptPagination.page = 1;
					this.loadScripts();
				},
				showCreateScriptDialog() {
					ElMessage.info('新增脚本功能开发中...');
				},
				viewScript(script) {
					ElMessage.info(`查看脚本: ${script.name}`);
				},
				editScript(script) {
					ElMessage.info(`编辑脚本: ${script.name}`);
				},
				async executeScript(script) {
					try {
						await ElMessageBox.confirm(`确定要执行脚本 "${script.name}" 吗？`, '确认执行', {
							type: 'warning'
						});
						// 这里调用执行脚本的API
						// await axios.post(`/scripts/${script.id}/execute`);
						ElMessage.success('脚本执行成功');
						this.loadScripts();
					} catch {
						// 用户取消操作
					}
				},
				async toggleScript(script) {
					try {
						// 这里调用切换脚本状态的API
						// await axios.put(`/scripts/${script.id}`, { enabled: script.enabled });
						ElMessage.success(`脚本已${script.enabled ? '启用' : '禁用'}`);
					} catch (error) {
						// 恢复原状态
						script.enabled = !script.enabled;
						ElMessage.error('操作失败');
					}
				},
				async deleteScript(script) {
					try {
						await ElMessageBox.confirm(`确定要删除脚本 "${script.name}" 吗？`, '确认删除', {
							type: 'warning'
						});
						// 这里调用删除脚本的API
						// await axios.delete(`/scripts/${script.id}`);
						ElMessage.success('脚本已删除');
						this.loadScripts();
					} catch {
						// 用户取消操作
					}
				},
				getScriptTypeText(type) {
					const texts = {
						message_handler: '消息处理器',
						business_rule: '业务规则',
						plugin: '插件',
						filter: '过滤器'
					};
					return texts[type] || '未知';
				},

				// 平台相关方法
				refreshPlatforms() {
					this.loadPlatforms();
					ElMessage.success('平台状态已刷新');
				},
				showAddPlatformDialog() {
					ElMessage.info('添加平台功能开发中...');
				},
				viewPlatform(platform) {
					ElMessage.info(`查看平台: ${platform.name}`);
				},
				editPlatform(platform) {
					ElMessage.info(`配置平台: ${platform.name}`);
				},
				async testPlatform(platform) {
					try {
						// 这里调用测试平台连接的API
						// await axios.post(`/platforms/${platform.name}/test`);
						ElMessage.success(`平台 ${platform.name} 连接测试成功`);
					} catch (error) {
						ElMessage.error(`平台 ${platform.name} 连接测试失败`);
					}
				},
				async togglePlatform(platform) {
					try {
						// 这里调用切换平台状态的API
						// await axios.put(`/platforms/${platform.name}`, { enabled: platform.enabled });
						ElMessage.success(`平台 ${platform.name} 已${platform.enabled ? '启用' : '禁用'}`);
					} catch (error) {
						// 恢复原状态
						platform.enabled = !platform.enabled;
						ElMessage.error('操作失败');
					}
				},
				getPlatformStatusClass(status) {
					const classes = {
						connected: 'status-tag status-active',
						disconnected: 'status-tag status-inactive',
						error: 'status-tag status-error'
					};
					return classes[status] || 'status-tag';
				},
				getPlatformStatusText(status) {
					const texts = {
						connected: '已连接',
						disconnected: '未连接',
						error: '连接错误'
					};
					return texts[status] || '未知';
				},

				// 监控相关方法
				getStatusCodeData() {
					if (!this.systemMetrics.status_codes) return [];

					const total = Object.values(this.systemMetrics.status_codes).reduce((sum, count) => sum + count, 0);
					return Object.entries(this.systemMetrics.status_codes).map(([code, count]) => ({
						code,
						count,
						percentage: total > 0 ? ((count / total) * 100).toFixed(2) : 0
					}));
				}
			}
		}).use(ElementPlus).mount('#app');
	</script>
</body>

</html>
