-- 垃圾消息过滤器
-- 检查消息是否包含垃圾内容

local spam_keywords = {
    "广告", "推广", "加微信", "免费领取",
    "点击链接", "限时优惠", "赚钱", "兼职",
    "刷单", "代理", "投资", "理财"
}

-- 检查消息是否存在
if not message or not message.content then
    log("warn", "消息内容为空")
    return true -- 空消息通过过滤
end

local content = message.content:lower()
--log("info", "检查消息: " .. content)

-- 遍历垃圾关键词
for i, keyword in ipairs(spam_keywords) do
    if content:find(keyword) then
        log("warn", "检测到垃圾消息关键词: " .. keyword)
        _should_stop = true -- 停止后续处理
        return false
    end
end

-- log("info", "消息通过垃圾过滤检查")
return true
