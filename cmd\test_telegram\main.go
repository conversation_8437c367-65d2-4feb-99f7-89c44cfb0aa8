package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// TelegramTestMessage 测试消息结构体
// 思路：模拟Telegram Bot API发送的消息格式
type TelegramTestMessage struct {
	UpdateID int `json:"update_id"`
	Message  struct {
		MessageID int   `json:"message_id"`
		From      struct {
			ID        int64  `json:"id"`
			IsBot     bool   `json:"is_bot"`
			FirstName string `json:"first_name"`
			LastName  string `json:"last_name"`
			Username  string `json:"username"`
		} `json:"from"`
		Chat struct {
			ID        int64  `json:"id"`
			Type      string `json:"type"`
			FirstName string `json:"first_name"`
			LastName  string `json:"last_name"`
			Username  string `json:"username"`
		} `json:"chat"`
		Date int64  `json:"date"`
		Text string `json:"text"`
	} `json:"message"`
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	PlatformUserID string `json:"platform_user_id"`
	PlatformChatID string `json:"platform_chat_id,omitempty"`
	Content        string `json:"content"`
	MessageType    string `json:"message_type"`
}

var (
	serverURL = flag.String("server", "http://localhost:8080", "客服系统服务器地址")
	userID    = flag.Int64("user", 123456789, "测试用户Telegram ID")
	chatID    = flag.Int64("chat", 0, "测试聊天ID（可选，默认使用用户ID）")
	message   = flag.String("msg", "你好，这是Telegram测试消息", "测试消息内容")
	mode      = flag.String("mode", "webhook", "测试模式：webhook, send, status")
)

func main() {
	flag.Parse()

	switch *mode {
	case "webhook":
		testWebHook()
	case "send":
		testSendMessage()
	case "status":
		testStatus()
	default:
		fmt.Println("支持的测试模式：webhook, send, status")
		flag.Usage()
	}
}

// testWebHook 测试WebHook接收
// 思路：模拟Telegram Bot API发送消息到客服系统
func testWebHook() {
	fmt.Println("=== 测试Telegram WebHook ===")

	// 设置聊天ID
	testChatID := *chatID
	if testChatID == 0 {
		testChatID = *userID
	}

	// 构建测试消息
	testMsg := TelegramTestMessage{
		UpdateID: int(time.Now().Unix()),
	}

	// 设置消息内容
	testMsg.Message.MessageID = int(time.Now().Unix())
	testMsg.Message.From.ID = *userID
	testMsg.Message.From.IsBot = false
	testMsg.Message.From.FirstName = "测试"
	testMsg.Message.From.LastName = "用户"
	testMsg.Message.From.Username = "test_user"

	testMsg.Message.Chat.ID = testChatID
	testMsg.Message.Chat.Type = "private"
	testMsg.Message.Chat.FirstName = "测试"
	testMsg.Message.Chat.LastName = "用户"
	testMsg.Message.Chat.Username = "test_user"

	testMsg.Message.Date = time.Now().Unix()
	testMsg.Message.Text = *message

	// 序列化消息
	msgBytes, err := json.Marshal(testMsg)
	if err != nil {
		log.Fatalf("序列化消息失败: %v", err)
	}

	// 发送到WebHook
	webhookURL := *serverURL + "/api/v1/platforms/telegram/webhook"
	fmt.Printf("发送WebHook到: %s\n", webhookURL)
	fmt.Printf("消息内容: %s\n", string(msgBytes))

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(msgBytes))
	if err != nil {
		log.Fatalf("发送WebHook失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ WebHook测试成功")
	} else {
		fmt.Println("❌ WebHook测试失败")
	}
}

// testSendMessage 测试发送消息
// 思路：通过API发送消息到Telegram平台
func testSendMessage() {
	fmt.Println("=== 测试发送Telegram消息 ===")

	// 设置聊天ID
	testChatID := *chatID
	if testChatID == 0 {
		testChatID = *userID
	}

	// 构建发送请求
	sendReq := SendMessageRequest{
		PlatformUserID: fmt.Sprintf("%d", *userID),
		PlatformChatID: fmt.Sprintf("%d", testChatID),
		Content:        *message,
		MessageType:    "text",
	}

	// 序列化请求
	reqBytes, err := json.Marshal(sendReq)
	if err != nil {
		log.Fatalf("序列化请求失败: %v", err)
	}

	// 发送请求
	sendURL := *serverURL + "/api/v1/platforms/telegram/send"
	fmt.Printf("发送消息到: %s\n", sendURL)
	fmt.Printf("请求内容: %s\n", string(reqBytes))

	resp, err := http.Post(sendURL, "application/json", bytes.NewBuffer(reqBytes))
	if err != nil {
		log.Fatalf("发送消息失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ 发送消息测试成功")
	} else {
		fmt.Println("❌ 发送消息测试失败")
	}
}

// testStatus 测试平台状态
// 思路：检查Telegram适配器的运行状态
func testStatus() {
	fmt.Println("=== 测试平台状态 ===")

	// 获取平台列表
	platformsURL := *serverURL + "/api/v1/platforms/"
	fmt.Printf("获取平台状态: %s\n", platformsURL)

	resp, err := http.Get(platformsURL)
	if err != nil {
		log.Fatalf("获取平台状态失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}

	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("平台状态: %s\n", string(body))

	// 获取详细统计
	statsURL := *serverURL + "/api/v1/platforms/stats"
	fmt.Printf("\n获取详细统计: %s\n", statsURL)

	resp2, err := http.Get(statsURL)
	if err != nil {
		log.Fatalf("获取统计信息失败: %v", err)
	}
	defer resp2.Body.Close()

	body2, err := io.ReadAll(resp2.Body)
	if err != nil {
		log.Fatalf("读取统计响应失败: %v", err)
	}

	fmt.Printf("统计信息: %s\n", string(body2))

	if resp.StatusCode == 200 && resp2.StatusCode == 200 {
		fmt.Println("✅ 状态检查成功")
	} else {
		fmt.Println("❌ 状态检查失败")
	}
}

// 使用示例：
// 
// # 测试WebHook接收
// go run cmd/test_telegram/main.go -mode=webhook -user=123456789 -msg="测试消息"
//
// # 测试群聊消息WebHook
// go run cmd/test_telegram/main.go -mode=webhook -user=123456789 -chat=-987654321 -msg="群消息测试"
//
// # 测试发送消息（需要配置Bot Token）
// go run cmd/test_telegram/main.go -mode=send -user=123456789 -msg="发送测试"
//
// # 测试平台状态
// go run cmd/test_telegram/main.go -mode=status
//
// # 指定服务器地址
// go run cmd/test_telegram/main.go -server=http://localhost:8080 -mode=status
