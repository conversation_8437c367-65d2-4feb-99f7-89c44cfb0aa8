<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #f3e5f5;
        }
        .message.system {
            background-color: #fff3e0;
            font-style: italic;
        }
        input, button, select {
            margin: 5px;
            padding: 8px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>WebSocket 客服系统测试</h1>
    
    <div class="container">
        <h3>连接状态</h3>
        <div id="status" class="status disconnected">未连接</div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>

    <div class="container">
        <h3>认证信息</h3>
        <label>平台类型:</label>
        <select id="platform">
            <option value="test">测试平台</option>
            <option value="qq">QQ</option>
            <option value="wechat">微信</option>
            <option value="telegram">Telegram</option>
        </select>
        <br>
        <label>用户ID:</label>
        <input type="text" id="platformId" value="user123" placeholder="输入用户ID">
        <br>
        <button onclick="authenticate()">认证</button>
    </div>

    <div class="container">
        <h3>消息</h3>
        <div id="messages" class="messages"></div>
        <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">发送消息</button>
        <button onclick="sendPing()">发送Ping</button>
        <button onclick="clearMessages()">清空消息</button>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let isAuthenticated = false;

        function connect() {
            if (ws) {
                ws.close();
            }

            const wsUrl = 'ws://localhost:8080/ws';
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                isConnected = true;
                updateStatus('已连接', 'connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                addMessage('系统', '已连接到服务器', 'system');
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (e) {
                    addMessage('系统', '收到无效消息: ' + event.data, 'system');
                }
            };

            ws.onclose = function(event) {
                isConnected = false;
                isAuthenticated = false;
                updateStatus('连接已断开', 'disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                addMessage('系统', '连接已断开', 'system');
            };

            ws.onerror = function(error) {
                addMessage('系统', '连接错误: ' + error, 'system');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function authenticate() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            const platform = document.getElementById('platform').value;
            const platformId = document.getElementById('platformId').value;

            if (!platformId) {
                alert('请输入用户ID');
                return;
            }

            const authMessage = {
                type: 'auth',
                data: {
                    platform: platform,
                    platform_id: platformId
                },
                timestamp: Date.now()
            };

            ws.send(JSON.stringify(authMessage));
            addMessage('我', '发送认证信息', 'sent');
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();

            if (!content) {
                return;
            }

            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            if (!isAuthenticated) {
                alert('请先进行认证');
                return;
            }

            const message = {
                type: 'chat',
                data: {
                    content: content,
                    message_type: 'text'
                },
                timestamp: Date.now()
            };

            ws.send(JSON.stringify(message));
            addMessage('我', content, 'sent');
            input.value = '';
        }

        function sendPing() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            const pingMessage = {
                type: 'ping',
                data: {},
                timestamp: Date.now()
            };

            ws.send(JSON.stringify(pingMessage));
            addMessage('我', 'Ping', 'sent');
        }

        function handleMessage(message) {
            switch (message.type) {
                case 'auth_success':
                    isAuthenticated = true;
                    addMessage('系统', '认证成功: ' + JSON.stringify(message.data), 'system');
                    break;
                case 'chat_reply':
                    addMessage('客服', message.data.content, 'received');
                    break;
                case 'pong':
                    addMessage('系统', 'Pong', 'system');
                    break;
                case 'error':
                    addMessage('系统', '错误: ' + message.data.message, 'system');
                    break;
                default:
                    addMessage('系统', '收到消息: ' + JSON.stringify(message), 'system');
            }
        }

        function addMessage(sender, content, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>${sender}</strong> [${timestamp}]: ${content}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function updateStatus(text, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = 'status ' + className;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            // 可以在这里自动连接
            // connect();
        };
    </script>
</body>
</html>
