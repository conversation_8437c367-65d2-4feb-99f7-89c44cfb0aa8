package handlers

import (
	"net/http"
	"time"

	"aike_go/internal/services"
	"github.com/gin-gonic/gin"
)

// WebSocketHandlers WebSocket处理器
// 思路：将WebSocket相关的HTTP处理器从主服务器文件中分离
// 使用例子：handlers := NewWebSocketHandlers(wsService)
type WebSocketHandlers struct {
	wsService     *services.WebSocketService
	napCatHandler interface{} // NapCat WebSocket处理器
}

// NewWebSocketHandlers 创建WebSocket处理器
// 思路：初始化WebSocket处理器，注入WebSocket服务依赖
// 使用例子：handlers := NewWebSocketHandlers(wsService, napCatHandler)
func NewWebSocketHandlers(wsService *services.WebSocketService, napCatHandler interface{}) *WebSocketHandlers {
	return &WebSocketHandlers{
		wsService:     wsService,
		napCatHandler: napCatHandler,
	}
}

// HandleWebSocket 处理WebSocket连接
// 思路：处理普通的WebSocket连接请求
// 使用例子：GET /ws
func (h *WebSocketHandlers) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket连接
	err := h.wsService.HandleConnection(c.Writer, c.Request)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "WebSocket连接失败",
			"details": err.Error(),
		})
		return
	}
}

// HandleNapCatWebSocket 处理NapCat WebSocket连接
// 思路：处理NapCat反向WebSocket连接请求
// 使用例子：GET /api/v1/ws/napcat
func (h *WebSocketHandlers) HandleNapCatWebSocket(c *gin.Context) {
	if h.napCatHandler != nil {
		// 类型断言，调用HandleWebSocket方法
		if handler, ok := h.napCatHandler.(interface {
			HandleWebSocket(c *gin.Context)
		}); ok {
			handler.HandleWebSocket(c)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "NapCat处理器不可用",
			})
		}
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "NapCat服务未启用",
		})
	}
}

// GetWSClients 获取WebSocket客户端列表处理器
// 思路：返回当前连接的WebSocket客户端信息
// 使用例子：GET /api/v1/ws/clients
func (h *WebSocketHandlers) GetWSClients(c *gin.Context) {
	// 获取客户端列表
	clients := h.wsService.GetClients()

	// 构建客户端信息
	clientList := make([]map[string]interface{}, 0)
	for _, client := range clients {
		clientInfo := map[string]interface{}{
			"id":           client.ID,
			"remote_addr":  client.RemoteAddr,
			"user_agent":   client.UserAgent,
			"connected_at": client.ConnectedAt.Unix(),
			"last_ping":    client.LastPing.Unix(),
			"is_active":    client.IsActive,
		}
		clientList = append(clientList, clientInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取WebSocket客户端列表成功",
		"data": gin.H{
			"clients": clientList,
			"count":   len(clientList),
		},
	})
}

// BroadcastMessage 广播消息处理器
// 思路：向所有连接的WebSocket客户端广播消息
// 使用例子：POST /api/v1/ws/broadcast
func (h *WebSocketHandlers) BroadcastMessage(c *gin.Context) {
	var req struct {
		Type    string      `json:"type" binding:"required"`    // 消息类型
		Content interface{} `json:"content" binding:"required"` // 消息内容
		From    string      `json:"from"`                       // 发送者
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 构建消息
	message := &services.WebSocketMessage{
		Type:      req.Type,
		Content:   req.Content,
		From:      req.From,
		Timestamp: time.Now().Unix(),
	}

	// 广播消息
	err := h.wsService.Broadcast(message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "广播消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "消息广播成功",
		"data": gin.H{
			"type":      req.Type,
			"from":      req.From,
			"sent_at":   time.Now().Unix(),
			"broadcast": true,
		},
	})
}

// SendWSMessage 发送WebSocket消息处理器
// 思路：向指定的WebSocket客户端发送消息
// 使用例子：POST /api/v1/ws/send
func (h *WebSocketHandlers) SendWSMessage(c *gin.Context) {
	var req struct {
		ClientID string      `json:"client_id" binding:"required"` // 客户端ID
		Type     string      `json:"type" binding:"required"`      // 消息类型
		Content  interface{} `json:"content" binding:"required"`   // 消息内容
		From     string      `json:"from"`                         // 发送者
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 构建消息
	message := &services.WebSocketMessage{
		Type:      req.Type,
		Content:   req.Content,
		From:      req.From,
		Timestamp: time.Now().Unix(),
	}

	// 发送消息给指定客户端
	err := h.wsService.SendToClient(req.ClientID, message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "消息发送成功",
		"data": gin.H{
			"client_id": req.ClientID,
			"type":      req.Type,
			"from":      req.From,
			"sent_at":   time.Now().Unix(),
		},
	})
}

// GetWSStats 获取WebSocket统计信息处理器
// 思路：返回WebSocket服务的统计信息
// 使用例子：GET /api/v1/ws/stats
func (h *WebSocketHandlers) GetWSStats(c *gin.Context) {
	// 获取统计信息
	stats := h.wsService.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"message": "获取WebSocket统计信息成功",
		"data": gin.H{
			"total_connections":    stats.TotalConnections,
			"active_connections":   stats.ActiveConnections,
			"messages_sent":        stats.MessagesSent,
			"messages_received":    stats.MessagesReceived,
			"bytes_sent":          stats.BytesSent,
			"bytes_received":      stats.BytesReceived,
			"connection_errors":    stats.ConnectionErrors,
			"last_connection_at":   stats.LastConnectionAt.Unix(),
			"service_start_time":   stats.ServiceStartTime.Unix(),
			"uptime_seconds":       time.Since(stats.ServiceStartTime).Seconds(),
		},
	})
}

// CloseWSConnection 关闭WebSocket连接处理器
// 思路：强制关闭指定的WebSocket连接
// 使用例子：DELETE /api/v1/ws/clients/:client_id
func (h *WebSocketHandlers) CloseWSConnection(c *gin.Context) {
	clientID := c.Param("client_id")
	if clientID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "客户端ID不能为空",
		})
		return
	}

	// 关闭连接
	err := h.wsService.CloseConnection(clientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "关闭连接失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "连接关闭成功",
		"data": gin.H{
			"client_id": clientID,
			"closed_at": time.Now().Unix(),
		},
	})
}
