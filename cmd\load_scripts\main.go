package main

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	"aike_go/internal/adapters"
	"aike_go/internal/config"
	"aike_go/internal/database"
	"aike_go/internal/services"

	"gopkg.in/yaml.v2"
)

// ScriptConfig 脚本配置结构
type ScriptConfig struct {
	Scripts []struct {
		Name        string `yaml:"name"`
		Description string `yaml:"description"`
		Type        string `yaml:"type"`
		Priority    int    `yaml:"priority"`
		Enabled     bool   `yaml:"enabled"`
		File        string `yaml:"file"`
	} `yaml:"scripts"`
}

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建服务实例
	scriptService := services.NewScriptService(database.GetDB())
	knowledgeService := services.NewKnowledgeService()
	openaiService := services.NewOpenAIService(cfg)

	// 创建脚本适配器（解决循环导入）
	scriptAdapter := adapters.NewScriptServiceAdapter(scriptService, openaiService, knowledgeService)

	// 读取脚本配置文件
	configFile := "scripts/config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	configData, err := ioutil.ReadFile(configFile)
	if err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	var scriptConfig ScriptConfig
	if err := yaml.Unmarshal(configData, &scriptConfig); err != nil {
		log.Fatalf("解析配置文件失败: %v", err)
	}

	fmt.Printf("📖 从配置文件加载 %d 个脚本定义\n", len(scriptConfig.Scripts))

	// 加载并创建脚本
	ctx := context.Background()
	successCount := 0

	for _, scriptDef := range scriptConfig.Scripts {
		// 读取Lua脚本文件
		scriptPath := filepath.Join("scripts", scriptDef.File)
		scriptContent, err := ioutil.ReadFile(scriptPath)
		if err != nil {
			log.Printf("❌ 读取脚本文件失败 %s: %v", scriptPath, err)
			continue
		}

		// 创建脚本请求
		req := &services.CreateScriptRequest{
			Name:        scriptDef.Name,
			Description: scriptDef.Description,
			Type:        scriptDef.Type,
			Priority:    scriptDef.Priority,
			Enabled:     scriptDef.Enabled,
			Content:     string(scriptContent),
		}

		// 创建脚本
		createdScript, err := scriptService.CreateScript(ctx, req)
		if err != nil {
			log.Printf("❌ 创建脚本失败 %s: %v", scriptDef.Name, err)
			continue
		}

		fmt.Printf("✅ 创建脚本成功: %s (ID: %s)\n", createdScript.Name, createdScript.ID)
		successCount++
	}

	fmt.Printf("\n🎉 脚本加载完成！成功: %d, 总计: %d\n", successCount, len(scriptConfig.Scripts))

	// 从数据库加载脚本到Lua引擎
	if successCount > 0 {
		fmt.Println("\n🔄 正在加载脚本到Lua引擎...")
		if err := scriptAdapter.LoadScriptsFromDatabase(ctx); err != nil {
			log.Printf("⚠️  加载脚本到引擎失败: %v", err)
		} else {
			fmt.Println("✅ 脚本已成功加载到Lua引擎")
		}

		fmt.Println("\n📖 使用说明:")
		fmt.Println("1. 访问 http://localhost:8082/api/v1/scripts/ 查看脚本列表")
		fmt.Println("2. 发送消息测试脚本功能")
		fmt.Println("3. 查看服务器日志了解脚本执行情况")
		fmt.Println("4. 编辑 scripts/ 目录下的 .lua 文件来修改脚本逻辑")
	}
}
