package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Script Lua脚本模型
// 思路：存储Lua脚本的元数据和内容
type Script struct {
	ID          string            `gorm:"primaryKey;size:64" json:"id"`
	Name        string            `gorm:"size:255;not null" json:"name"`
	Description string            `gorm:"type:text" json:"description"`
	Content     string            `gorm:"type:longtext;not null" json:"content"`
	Type        string            `gorm:"size:50;not null;index" json:"type"` // message_filter, message_handler, business_rule
	Enabled     bool              `gorm:"default:true;index" json:"enabled"`
	Priority    int               `gorm:"default:100;index" json:"priority"` // 执行优先级，数字越小优先级越高
	Triggers    StringArray       `gorm:"type:json" json:"triggers"`         // 触发条件
	Config      StringMap         `gorm:"type:json" json:"config"`           // 脚本配置
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	DeletedAt   gorm.DeletedAt    `gorm:"index" json:"deleted_at,omitempty"`
}

// StringArray 字符串数组类型
// 思路：用于存储JSON格式的字符串数组
type StringArray []string

// Value 实现driver.Valuer接口
func (sa StringArray) Value() (driver.Value, error) {
	if sa == nil {
		return nil, nil
	}
	return json.Marshal(sa)
}

// Scan 实现sql.Scanner接口
func (sa *StringArray) Scan(value interface{}) error {
	if value == nil {
		*sa = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, sa)
	case string:
		return json.Unmarshal([]byte(v), sa)
	default:
		return fmt.Errorf("无法将 %T 转换为 StringArray", value)
	}
}

// StringMap 字符串映射类型
// 思路：用于存储JSON格式的字符串键值对
type StringMap map[string]string

// Value 实现driver.Valuer接口
func (sm StringMap) Value() (driver.Value, error) {
	if sm == nil {
		return nil, nil
	}
	return json.Marshal(sm)
}

// Scan 实现sql.Scanner接口
func (sm *StringMap) Scan(value interface{}) error {
	if value == nil {
		*sm = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, sm)
	case string:
		return json.Unmarshal([]byte(v), sm)
	default:
		return fmt.Errorf("无法将 %T 转换为 StringMap", value)
	}
}

// TableName 指定表名
func (Script) TableName() string {
	return "scripts"
}

// BeforeCreate 创建前钩子
// 思路：设置创建时间和默认值
func (s *Script) BeforeCreate(tx *gorm.DB) error {
	if s.CreatedAt.IsZero() {
		s.CreatedAt = time.Now()
	}
	if s.UpdatedAt.IsZero() {
		s.UpdatedAt = time.Now()
	}
	if s.Priority == 0 {
		s.Priority = 100
	}
	if s.Triggers == nil {
		s.Triggers = StringArray{}
	}
	if s.Config == nil {
		s.Config = StringMap{}
	}
	return nil
}

// BeforeUpdate 更新前钩子
// 思路：更新修改时间
func (s *Script) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}

// IsValid 验证脚本有效性
// 思路：检查脚本的基本字段是否有效
func (s *Script) IsValid() error {
	if s.Name == "" {
		return fmt.Errorf("脚本名称不能为空")
	}
	if s.Content == "" {
		return fmt.Errorf("脚本内容不能为空")
	}
	if s.Type == "" {
		return fmt.Errorf("脚本类型不能为空")
	}

	// 验证脚本类型
	validTypes := map[string]bool{
		"message_filter":  true,
		"message_handler": true,
		"business_rule":   true,
	}
	if !validTypes[s.Type] {
		return fmt.Errorf("无效的脚本类型: %s", s.Type)
	}

	return nil
}

// GetTriggerList 获取触发器列表
// 思路：返回触发器字符串数组
func (s *Script) GetTriggerList() []string {
	if s.Triggers == nil {
		return []string{}
	}
	return []string(s.Triggers)
}

// SetTriggerList 设置触发器列表
// 思路：设置触发器字符串数组
func (s *Script) SetTriggerList(triggers []string) {
	if triggers == nil {
		s.Triggers = StringArray{}
	} else {
		s.Triggers = StringArray(triggers)
	}
}

// GetConfigValue 获取配置值
// 思路：根据键获取配置值
func (s *Script) GetConfigValue(key string) (string, bool) {
	if s.Config == nil {
		return "", false
	}
	value, exists := s.Config[key]
	return value, exists
}

// SetConfigValue 设置配置值
// 思路：设置配置键值对
func (s *Script) SetConfigValue(key, value string) {
	if s.Config == nil {
		s.Config = make(StringMap)
	}
	s.Config[key] = value
}

// GetConfigMap 获取配置映射
// 思路：返回配置的map格式
func (s *Script) GetConfigMap() map[string]string {
	if s.Config == nil {
		return make(map[string]string)
	}
	return map[string]string(s.Config)
}

// SetConfigMap 设置配置映射
// 思路：设置配置的map格式
func (s *Script) SetConfigMap(config map[string]string) {
	if config == nil {
		s.Config = StringMap{}
	} else {
		s.Config = StringMap(config)
	}
}

// Clone 克隆脚本
// 思路：创建脚本的副本
func (s *Script) Clone() *Script {
	clone := &Script{
		ID:          s.ID,
		Name:        s.Name,
		Description: s.Description,
		Content:     s.Content,
		Type:        s.Type,
		Enabled:     s.Enabled,
		Priority:    s.Priority,
		CreatedAt:   s.CreatedAt,
		UpdatedAt:   s.UpdatedAt,
	}

	// 深拷贝触发器
	if s.Triggers != nil {
		clone.Triggers = make(StringArray, len(s.Triggers))
		copy(clone.Triggers, s.Triggers)
	}

	// 深拷贝配置
	if s.Config != nil {
		clone.Config = make(StringMap)
		for k, v := range s.Config {
			clone.Config[k] = v
		}
	}

	return clone
}

// String 字符串表示
// 思路：返回脚本的字符串描述
func (s *Script) String() string {
	return fmt.Sprintf("Script{ID: %s, Name: %s, Type: %s, Enabled: %t, Priority: %d}",
		s.ID, s.Name, s.Type, s.Enabled, s.Priority)
}

// ScriptExecution 脚本执行记录模型
// 思路：记录脚本的执行历史和结果
type ScriptExecution struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	ScriptID  string    `gorm:"size:64;not null;index" json:"script_id"`
	RequestID string    `gorm:"size:128;index" json:"request_id"`
	Success   bool      `gorm:"index" json:"success"`
	Duration  int64     `json:"duration"` // 执行时间（毫秒）
	Error     string    `gorm:"type:text" json:"error,omitempty"`
	Result    string    `gorm:"type:text" json:"result,omitempty"`
	Context   string    `gorm:"type:text" json:"context,omitempty"`
	CreatedAt time.Time `json:"created_at"`

	// 关联
	Script *Script `gorm:"foreignKey:ScriptID;references:ID" json:"script,omitempty"`
}

// TableName 指定表名
func (ScriptExecution) TableName() string {
	return "script_executions"
}

// BeforeCreate 创建前钩子
func (se *ScriptExecution) BeforeCreate(tx *gorm.DB) error {
	if se.CreatedAt.IsZero() {
		se.CreatedAt = time.Now()
	}
	return nil
}

// IsSuccessful 检查执行是否成功
func (se *ScriptExecution) IsSuccessful() bool {
	return se.Success && se.Error == ""
}

// GetDurationMs 获取执行时间（毫秒）
func (se *ScriptExecution) GetDurationMs() int64 {
	return se.Duration
}

// GetDurationSeconds 获取执行时间（秒）
func (se *ScriptExecution) GetDurationSeconds() float64 {
	return float64(se.Duration) / 1000.0
}
