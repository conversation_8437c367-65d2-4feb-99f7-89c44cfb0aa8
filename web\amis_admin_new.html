<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI客服系统管理后台</title>
	<!-- 引入amis的CSS和JS -->
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/sdk/sdk.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/sdk/helper.css" />
	<link rel="stylesheet" href="https://unpkg.com/amis@latest/sdk/iconfont.css" />
	<script src="https://unpkg.com/amis@latest/sdk/sdk.js"></script>

</head>

<body>
	<div id="root" class="app-wrapper">
		<div class="loading">
			<div class="spinner"></div>
			<p>正在加载管理后台配置...</p>
		</div>
	</div>

	<script type="text/javascript">
		(function () {
			let amis = amisRequire('amis/embed');

			// 从后端获取Amis配置
			fetch('/api/v1/admin/amis-config')
				.then(response => {
					if (!response.ok) {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}
					return response.json();
				})
				.then(schema => {
					// 使用从后端获取的配置初始化Amis
					let amisScoped = amis.embed('#root', schema, {
						locale: 'zh-CN'
					});
				})
				.catch(error => {
					console.error('加载Amis配置失败:', error);
					// 显示错误信息
					document.getElementById('root').innerHTML = `
                        <div class="error">
                            <h2>⚠️ 配置加载失败</h2>
                            <p>无法从服务器获取管理后台配置</p>
                            <p style="color: #666; font-size: 14px;">错误信息: ${error.message}</p>
                            <button onclick="location.reload()">🔄 重新加载</button>
                        </div>
                    `;
				});
		})();
	</script>
</body>

</html>
