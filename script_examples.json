{"spam_filter": {"name": "垃圾消息过滤器", "description": "过滤包含广告、垃圾信息的消息", "type": "message_filter", "priority": 100, "enabled": true, "content": "-- 垃圾消息过滤器\n-- 检查消息是否包含垃圾内容\n\nlocal spam_keywords = {\n    \"广告\", \"推广\", \"加微信\", \"免费领取\",\n    \"点击链接\", \"限时优惠\", \"赚钱\", \"兼职\"\n}\n\n-- 检查消息内容\nlocal content = message.content:lower()\nlog(\"info\", \"检查消息: \" .. content)\n\n-- 遍历垃圾关键词\nfor i, keyword in ipairs(spam_keywords) do\n    if content:find(keyword) then\n        log(\"warn\", \"检测到垃圾消息关键词: \" .. keyword)\n        _should_stop = true  -- 停止后续处理\n        return false\n    end\nend\n\nlog(\"info\", \"消息通过垃圾过滤检查\")\nreturn true"}, "auto_reply": {"name": "自动回复处理器", "description": "根据关键词自动回复消息", "type": "message_handler", "priority": 200, "enabled": true, "content": "-- 自动回复处理器\n-- 根据关键词自动回复\n\nlocal content = message.content\nlocal user_id = message.from.platform_user_id\n\nlog(\"info\", \"处理用户消息: \" .. content)\n\n-- 问候语回复\nif content == \"你好\" or content == \"hi\" or content == \"hello\" then\n    send_message(user_id, \"您好！欢迎使用AI客服系统，我是您的智能助手。\")\n    return \"已发送问候回复\"\nend\n\n-- 帮助信息\nif content == \"帮助\" or content == \"help\" then\n    local help_text = \"可用命令:\\n\" ..\n                     \"• 你好 - 问候\\n\" ..\n                     \"• 帮助 - 显示此帮助\\n\" ..\n                     \"• 时间 - 获取当前时间\\n\" ..\n                     \"• 状态 - 查看系统状态\"\n    send_message(user_id, help_text)\n    return \"已发送帮助信息\"\nend\n\n-- 时间查询\nif content == \"时间\" or content == \"现在几点\" then\n    local current_time = os.date(\"%Y-%m-%d %H:%M:%S\")\n    send_message(user_id, \"当前时间: \" .. current_time)\n    return \"已发送时间信息\"\nend\n\n-- 状态查询\nif content == \"状态\" or content == \"status\" then\n    send_message(user_id, \"系统运行正常 ✅\\n平台: \" .. message.platform)\n    return \"已发送状态信息\"\nend\n\nlog(\"info\", \"未匹配到自动回复规则\")\nreturn nil"}, "business_rule": {"name": "工作时间检查", "description": "检查是否在工作时间内，非工作时间自动回复", "type": "business_rule", "priority": 50, "enabled": true, "content": "-- 工作时间检查业务规则\n-- 检查当前是否在工作时间内\n\nlocal current_hour = tonumber(os.date(\"%H\"))\nlocal current_day = tonumber(os.date(\"%w\"))  -- 0=周日, 1=周一, ..., 6=周六\n\nlog(\"info\", \"当前时间: \" .. current_hour .. \"点, 星期\" .. current_day)\n\n-- 工作时间: 周一到周五 9:00-18:00\nlocal is_workday = current_day >= 1 and current_day <= 5\nlocal is_workhour = current_hour >= 9 and current_hour < 18\n\nif not (is_workday and is_workhour) then\n    local user_id = message.from.platform_user_id\n    local off_work_msg = \"您好！现在是非工作时间。\\n\" ..\n                        \"工作时间：周一至周五 9:00-18:00\\n\" ..\n                        \"您的消息我们已收到，工作时间会及时回复您。\"\n    \n    send_message(user_id, off_work_msg)\n    log(\"info\", \"已发送非工作时间自动回复\")\n    \n    -- 设置变量标记已处理\n    set_var(\"handled_by_workhour_check\", true)\n    return \"非工作时间自动回复\"\nend\n\nlog(\"info\", \"当前在工作时间内\")\nreturn \"工作时间内，继续处理\""}}