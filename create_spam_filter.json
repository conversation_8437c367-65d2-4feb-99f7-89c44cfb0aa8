{"name": "垃圾消息过滤器", "description": "过滤包含广告、垃圾信息的消息", "type": "message_filter", "priority": 100, "enabled": true, "content": "-- 垃圾消息过滤器\n-- 检查消息是否包含垃圾内容\n\nlocal spam_keywords = {\n    \"广告\", \"推广\", \"加微信\", \"免费领取\",\n    \"点击链接\", \"限时优惠\", \"赚钱\", \"兼职\"\n}\n\n-- 检查消息内容\nlocal content = message.content:lower()\nlog(\"info\", \"检查消息: \" .. content)\n\n-- 遍历垃圾关键词\nfor i, keyword in ipairs(spam_keywords) do\n    if content:find(keyword) then\n        log(\"warn\", \"检测到垃圾消息关键词: \" .. keyword)\n        _should_stop = true  -- 停止后续处理\n        return false\n    end\nend\n\nlog(\"info\", \"消息通过垃圾过滤检查\")\nreturn true"}